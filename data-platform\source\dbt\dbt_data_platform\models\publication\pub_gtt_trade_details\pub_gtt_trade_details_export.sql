{{ config(materialized="view", tags=["gtt_trade_details"],  alias='export') }}

SELECT 
   tradeflow,
   ismirror,
   periodtype,
   CAST(year_start AS number) AS year_start,
   CAST(month_start AS number) AS month_start,
   CAST(year_end AS number) AS year_end,
   CAST(month_end AS number) AS month_end,
   CAST(year AS number) AS year,
   CAST(month AS number) AS month,
   reportercountryno,
   reportercode,
   reporterisoalpha3code,
   reporterisonumeric3code,
   reportername,
   reporterdescription,
   firstavailableperiod,
   lastavailableperiod,
   reportersource,
   incoterm,
   partnercountryno,
   partnercode,
   partnerisoalpha3code,
   partnerisonumeric3code,
   partnername,
   hscode,
   commoditydescriptionoriginal,
   commoditydescriptiontranslation,
   hs2code,
   hs2descriptionoriginal,
   hs2descriptiontranslation,
   hs4code,
   hs4descriptionoriginal,
   hs4descriptiontranslation,
   hs6code,
   hs6descriptionoriginal,
   hs6descriptiontranslation,
   commodityexplodedhscode,
   commodityexplodeddescriptionoriginal,
   commodityexplodeddescriptiontranslation,
   CAST(hseditionfrom AS number) AS hseditionfrom,
   CAST(hseditionto AS number) AS hseditionto,
   currency,
   CAST(value AS DECIMAL(20,8)) AS value,
   unit1,
   CAST(quantity1 AS NUMBER) AS quantity1,
   CAST(q1estimation AS DECIMAL(20,8)) as q1estimation,
   unit2,
   CAST(quantity2 AS number) AS quantity2,
   CAST(q2estimation AS DECIMAL(20,8)) as q2estimation,
   priceunit1,
   CAST(price1 AS DECIMAL(20,8)) AS price1,
   transport,
   subdivision,
   port,
   priceunit2,
   CAST(price2 AS DECIMAL(20,8)) AS price2,
   customsregime,
   foreignport,
   suppression,
   usstate,
   trade_date
from {{ ref("int_gtt_trade_details_export")}}