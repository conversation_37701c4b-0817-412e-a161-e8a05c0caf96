from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.timetables.trigger import CronTriggerTimetable
from datetime import datetime, timedelta
import pendulum
from util.dataset import to_s3
from airflow.providers.http.operators.http import HttpOperator
from util.validate import check_for_anomalies
import os

dag_id = os.path.basename(__file__).replace(".py","")

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 11, 4, tz="America/New_York"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1),
}

doc_md_DAG='This DAG downloads the ICE MFT Markers and Indices data (EOD_FuturesIM_All) from SFTP to JFS and to S3'
dag = DAG(
    dag_id=dag_id,
    default_args=default_args,
    description='This DAG downloads ICE MFT Markers and Indices data from SFTP to JFS and to S3',
    schedule=CronTriggerTimetable('30 20 * * 1-5',timezone="America/New_York"),
    tags=["phdata","ice","mft","markersandindices"],
    catchup=False,
    doc_md=doc_md_DAG,
)

op_kwargs = {
    "dataset": "ice.mft.markersandindices",
}

raw = PythonOperator(
    task_id="jg-raw-ice-mft-markersandindices-gl-jfs-s3",
    python_callable=to_s3,
    dag=dag,
    op_kwargs=op_kwargs,
)

validation_job = HttpOperator(
    task_id="call-daily-data-validation-api-markersandindices",
    http_conn_id="vendor_http", 
    endpoint=f"getJFSFeedAvailabilitySatusDailyDownload/{dag_id}",
    method="GET",
    headers={"Content-Type": "application/json"},
    response_check=check_for_anomalies,
    extra_options={"check_response": True},
    log_response=True,
    dag=dag
)

raw >> validation_job 