{{ config(materialized='table', tags=['spacinsider', 'participants']) }}


SELECT 
    ROW_NUMBER() OVER (ORDER BY 1) as record_id,
    filename,
    to_date(EXTRACT(year FROM TO_DATE(SPLIT_PART(SPLIT_PART(filename, '_', -1), '.', 1),'YYYYMMDD')) || '-' ||
    EXTRACT(month FROM TO_DATE(SPLIT_PART(SPLIT_PART(filename, '_', -1), '.', 1),'YYYYMMDD')) || '-' ||
    EXTRACT(day FROM TO_DATE(SPLIT_PART(SPLIT_PART(filename, '_', -1), '.', 1),'YYYYMMDD'))) AS snapshot_date,
    {{ auto_generate_json_query('VENDOR_RAW.SPACINSIDER.SPACINSIDER_SPACS_RAW', 'payload') }}

FROM VENDOR_RAW.SPACINSIDER.SPACINSIDER_PARTICIPANTS_RAW
WHERE payload IS NOT NULL