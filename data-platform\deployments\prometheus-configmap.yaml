apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-prometheus-server
  namespace: default
  labels:
    app: prometheus
    chart: prometheus-19.7.2
    component: server
    heritage: Helm
    release: loki
data:
  alerting_rules.yml: |
    {}
  alerts: |
    {}
  allow-snippet-annotations: "false"
  prometheus.yml: |
    global:
      evaluation_interval: 1m
      scrape_interval: 1m
      scrape_timeout: 10s
    rule_files:
      - /etc/config/recording_rules.yml
      - /etc/config/alerting_rules.yml
      - /etc/config/rules
      - /etc/config/alerts
    scrape_configs:
      - job_name: 'node-exporter'
        kubernetes_sd_configs:
          - role: endpoints
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: platform-monitoring-service
      - job_name: prometheus
        static_configs:
          - targets: ['localhost:9090']
      - job_name: kubernetes-pods
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_promtail_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_promtail_io_job]
            action: replace
            target_label: job
          - source_labels: [__meta_kubernetes_pod_annotation_promtail_io_path]
            action: replace
            target_label: __path__
      - job_name: JG_Interconnect
        static_configs:
          - targets:
              - ***********:9091
            labels:
              environment: prod
          - targets:
              - ************:9091
            labels:
              environment: dr
    alerting:
      alertmanagers:
        - kubernetes_sd_configs:
            - role: pod
          tls_config:
            ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
          bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
          relabel_configs:
            - source_labels: [__meta_kubernetes_namespace]
              regex: default
              action: keep
            - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_instance]
              regex: loki
              action: keep
            - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
              regex: alertmanager
              action: keep
            - source_labels: [__meta_kubernetes_pod_container_port_number]
              regex: "9093"
              action: keep
  recording_rules.yml: |
    {}
  rules: |
    {}
