CREATE DATABASE BLOOMBERG;
CREATE SCHEMA BLOOMBERG.SNAP;
CREATE ROLE DR_BLOOMBERG_DB_READ_WRITE;
CREATE ROLE DR_BLOOMBERG_DB_READ_ONLY;
CREATE ROLE DR_BLOOMBERG_OWNER;

GRANT ROLE DR_BLOOMBERG_OWNER TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_BLOOMBERG_DB_READ_ONLY TO ROLE FR_APACST;

GRANT ALL ON DATABASE BLOOMBERG TO ROLE DR_BLOOMBERG_OWNER;

--Grant all gives create schema--
GRANT USAGE ON SCHEMA BLOOMBERG.SNAP TO DR_BLOOMBERG_OWNER;
GRANT USAGE ON SCHEMA BLOOMBERG.SNAP TO DR_BLOOMBERG_DB_READ_ONLY;
GRANT USAGE ON SCHEMA BLOOMBERG.SNAP TO DR_BLOOMBERG_DB_READ_WRITE;
GRANT USAGE ON DATABASE BLOOMBERG TO DR_BLOOMBERG_DB_READ_ONLY;
GRANT USAGE ON DATABASE BLOOMBERG TO DR_BLOOMBERG_DB_READ_WRITE;

--Use access role for access to warehouse
GRANT ROLE AR_APACTST_WH TO ROLE DR_BLOOMBERG_OWNER;
GRANT ROLE AR_APACTST_WH TO ROLE DR_BLOOMBERG_DB_READ_ONLY;
GRANT ROLE AR_APACTST_WH TO ROLE DR_BLOOMBERG_DB_READ_WRITE;

--added schema name
GRANT USAGE ON SCHEMA BLOOMBERG.SNAP TO FR_APACST;

GRANT SELECT ON ALL TABLES IN DATABASE BLOOMBERG TO DR_BLOOMBERG_DB_READ_ONLY;
GRANT SELECT ON ALL TABLES IN SCHEMA BLOOMBERG.SNAP TO DR_BLOOMBERG_DB_READ_ONLY;

--change all to ownership
GRANT OWNERSHIP ON ALL TABLES IN DATABASE BLOOMBERG TO DR_BLOOMBERG_OWNER;
GRANT ALL PRIVILEGES on schema BLOOMBERG.SNAP to role DR_BLOOMBERG_OWNER;
GRANT SELECT ON ALL VIEWS  IN DATABASE BLOOMBERG TO DR_BLOOMBERG_DB_READ_ONLY;
GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON ALL TABLES IN DATABASE BLOOMBERG TO DR_BLOOMBERG_DB_READ_WRITE;
GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON ALL VIEWS IN DATABASE BLOOMBERG TO DR_BLOOMBERG_DB_READ_WRITE;

--Add Section for Future Objects 
GRANT SELECT ON FUTURE TABLES IN SCHEMA BLOOMBERG.SNAP TO DR_BLOOMBERG_DB_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA BLOOMBERG.SNAP TO DR_BLOOMBERG_DB_READ_ONLY;
GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA BLOOMBERG.SNAP TO DR_BLOOMBERG_DB_READ_WRITE;
GRANT SELECT  ON FUTURE VIEWS  IN SCHEMA BLOOMBERG.SNAP TO DR_BLOOMBERG_DB_READ_WRITE;

--change all to ownership
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA BLOOMBERG.SNAP TO DR_BLOOMBERG_OWNER;

GRANT USAGE ON SCHEMA BLOOMBERG.SNAP TO ROLE DR_BLOOMBERG_SNAP_OWNER;
GRANT CREATE TABLE ON SCHEMA BLOOMBERG.SNAP TO ROLE DR_BLOOMBERG_SNAP_OWNER;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA BLOOMBERG.SNAP TO ROLE DR_BLOOMBERG_SNAP_OWNER;
GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA BLOOMBERG.SNAP TO ROLE DR_BLOOMBERG_SNAP_OWNER;

GRANT USAGE ON SCHEMA BLOOMBERG.SNAP TO ROLE DR_BLOOMBERG_SNAP_DB_READ_WRITE;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA BLOOMBERG.SNAP TO ROLE DR_BLOOMBERG_SNAP_DB_READ_WRITE;
GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA BLOOMBERG.SNAP TO ROLE DR_BLOOMBERG_SNAP_DB_READ_WRITE;

GRANT ROLE DR_BLOOMBERG_SNAP_OWNER TO ROLE JG_MSK;
GRANT ROLE DR_BLOOMBERG_SNAP_DB_READ_WRITE TO ROLE JG_MSK;




--Adding Bloomberg Events Schema and Roles
--Currently Bloomberg db is owned by accountadmin
USE ROLE ACCOUNTADMIN; 

--DBT stage schema models are ephemeral(not physically staged)
CREATE SCHEMA IF NOT EXISTS BLOOMBERG.INTEGRATION;
CREATE SCHEMA IF NOT EXISTS BLOOMBERG.EVENTS;

CREATE ROLE IF NOT EXISTS DR_BLOOMBERG_EVENTS_OWNER;
CREATE ROLE IF NOT EXISTS DR_BLOOMBERG_EVENTS_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_BLOOMBERG_EVENTS_READ_ONLY;

--STAGING schema exists, so do not create dbt STAGE schema
GRANT OWNERSHIP ON SCHEMA BLOOMBERG.INTEGRATION TO ROLE DR_BLOOMBERG_OWNER;
GRANT OWNERSHIP ON SCHEMA BLOOMBERG.EVENTS      TO ROLE DR_BLOOMBERG_EVENTS_OWNER;


--
--
--
GRANT ROLE DR_BLOOMBERG_EVENTS_OWNER TO ROLE FR_DATA_PLATFORM_LEADS;
GRANT ROLE DR_BLOOMBERG_EVENTS_READ_WRITE TO ROLE FR_DATA_PLATFORM;


----------------------
--DB USAGE
----------------------
GRANT USAGE ON DATABASE BLOOMBERG TO ROLE DR_BLOOMBERG_EVENTS_OWNER;
GRANT USAGE ON DATABASE BLOOMBERG TO ROLE DR_BLOOMBERG_EVENTS_READ_WRITE;
GRANT USAGE ON DATABASE BLOOMBERG TO ROLE DR_BLOOMBERG_EVENTS_READ_ONLY;


----------------------
--READ WRITE ROLE SCHEMA USAGE
----------------------
GRANT USAGE ON SCHEMA BLOOMBERG.INTEGRATION      TO ROLE DR_BLOOMBERG_EVENTS_READ_WRITE;
GRANT USAGE ON SCHEMA BLOOMBERG.EVENTS           TO ROLE DR_BLOOMBERG_EVENTS_READ_WRITE;

-----
--READ ROLE SCHEMA USAGE
-----
GRANT USAGE ON SCHEMA BLOOMBERG.EVENTS TO ROLE DR_BLOOMBERG_EVENTS_READ_ONLY;


----
--Read Write Schema Grants
----
GRANT CREATE TABLE, CREATE VIEW ON  SCHEMA INTEGRATION TO ROLE DR_BLOOMBERG_EVENTS_READ_WRITE;
GRANT CREATE TABLE, CREATE VIEW ON  SCHEMA EVENTS      TO ROLE DR_BLOOMBERG_EVENTS_READ_WRITE;

----
--FUTURE GRANTS  READ ROLE
----
GRANT SELECT ON FUTURE TABLES IN SCHEMA BLOOMBERG.EVENTS TO ROLE DR_BLOOMBERG_EVENTS_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA BLOOMBERG.EVENTS TO ROLE DR_BLOOMBERG_EVENTS_READ_ONLY;

----
--Roll Events Read Only Role into Read Write Role
----
GRANT ROLE DR_BLOOMBERG_EVENTS_READ_ONLY TO ROLE DR_BLOOMBERG_EVENTS_READ_WRITE;


