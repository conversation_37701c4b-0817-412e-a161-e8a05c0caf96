import os
from abc import ABC, abstractmethod
import pandas as pd
import logging
from numpy import dtype

from utils.file import read_n_to_last_line


START_OF_DATA = "START-OF-DATA"
END_OF_DATA = "END-OF-DATA"

START_OF_FIELDS = "START-OF-FIELDS"
END_OF_FIELDS = "END-OF-FIELDS"

START_SECURITY = "START SECURITY"
END_SECURITY = "END SECURITY"

END_OF_FILE = "END-OF-FILE"


class ParseStrategy(ABC):
    def __init__(self, file, metadata, **pd_kwargs):
        self.file = file
        self.metadata = metadata
        self.pd_kwargs = pd_kwargs

    @abstractmethod
    def parse_data(include_metadata=[], **kwargs) -> pd.DataFrame:
        pass


class HistoryStrategy(ParseStrategy):
    def __init__(self, file, metadata, **pd_kwargs):
        ParseStrategy.__init__(self, file, metadata, **pd_kwargs)

    def extract_columns(self):
        columns = ["Ticker", "status1", "status2"]

        with open(self.file, "r") as f:
            is_fields = False
            for line in f:
                if line.strip() == START_OF_FIELDS:
                    is_fields = True
                elif line.strip() == END_OF_FIELDS:
                    break
                elif is_fields:
                    columns.append(line.strip())
        f.close()
        return columns

    def parse_data(self, include_metadata=[], **kwargs):
        columns = self.extract_columns()
        data_table = []
        with open(self.file, "r") as file:
            nextField = columns[3]
            is_data = False
            field_id = None
            for line in file:
                if line.strip() == START_OF_DATA:
                    is_data = True

                elif line.strip() == END_OF_DATA:
                    break

                elif line[:12] == END_SECURITY:
                    continue

                elif line[:14] == START_SECURITY:
                    tempLine = line.split("|")
                    nextField = tempLine[2]
                    continue

                elif is_data:
                    row = line.split("|")
                    field_id = nextField
                    ticker = row[0]
                    value = row[2]
                    date = row[1]

                    data_table.append([ticker, field_id, value, date])
        file.close()
        df = pd.DataFrame(data_table, columns=["Ticker", "Field", "Value", "Date"])
        return df


class SnapStrategy(ParseStrategy):
    def __init__(self, file, metadata, **pd_kwargs):
        ParseStrategy.__init__(self, file, metadata, **pd_kwargs)

    def parse_data(self, include_metadata=[], **kwargs):
        if len(self.metadata["fields"]) == 1 and self.metadata["fields"] == [
            "DVD_HIST_ALL"
        ]:
            column_names = [
                "SECURITY",
                "BC_EQY_DVD_HIST_ALL_ANN_DT",
                "BC_EQY_DVD_HIST_ALL_EX_DT",
                "BC_EQY_DVD_HIST_ALL_REC_DT",
                "BC_EQY_DVD_HIST_ALL_PAY_DT",
                "BC_EQY_DVD_HIST_ALL_AMT",
                "DVD_FREQ",
                "CP_DVD_TYP",
            ]
        elif len(self.metadata["fields"]) == 1 and self.metadata["fields"] == [
            "BDVD_ALL_PROJECTIONS"
        ]:
            column_names = [
                "SECURITY",
                "DVD_DECLARED_DT",
                "DVD_EX_DT",
                "BC_BDVD_PER_SHARE_AMT",
                "BC_BDVD_TREND",
                "BC_BDVD_OPT_IMPLIED_RANGE_LOW",
                "BC_BDVD_OPT_IMPLIED_RANGE_HIGH",
            ]
        elif len(self.metadata["fields"]) == 1 and self.metadata["fields"] == [
            "EQY_DVD_HIST_SPLITS"
        ]:
            column_names = [
                "SECURITY",
                "BC_EQY_DVD_HIST_SPLITS_ANN_DT",
                "BC_EQY_DVD_HIST_SPLITS_EX_DT",
                "BC_EQY_DVD_HIST_SPLITS_REC_DT",
                "BC_EQY_DVD_HIST_SPLITS_PAY_DT",
                "BC_EQY_DVD_HIST_SPLITS_AMT",
                "DVD_FREQ",
                "CP_DVD_TYP",
            ]
        elif len(self.metadata["fields"]) == 1 and self.metadata["fields"] == [
            "ECO_FUTURE_RELEASE_DATE_LIST"
        ]:
            column_names = [
                "SECURITY",
                "ECO_RELEASE_DATETIME",
            ]
        elif "BVALTIER" in self.metadata.keys() or "BVALSNAPSHOT" in self.metadata.keys():
            column_names = ["SECURITIES", "ERROR_CODE", "NUM_FLDS"] + self.metadata["fields"] + ["EMPTY_COL"]
        else:
            column_names = self.metadata["fields"]

        if kwargs.get("headers_from_metadata", False):
            df = pd.read_csv(
                self.file,
                skiprows=kwargs.get("start_idx", 0),
                skipfooter=kwargs.get("end_idx", 0),
                names=["SECURITIES", "ERROR_CODE", "NUM_FLDS"]
                + self.metadata["fields"],
                **self.pd_kwargs,
            )
        else:
            df = pd.read_csv(
                self.file,
                skiprows=kwargs.get("start_idx", 0),
                skipfooter=kwargs.get("end_idx", 0),
                names=column_names,
                **self.pd_kwargs,
            )
        type_dict = df.dtypes.to_dict()
        for col in type_dict:
            if type_dict[col] == dtype("O"):
                df[col] = df[col].astype("str")

        for e in include_metadata:
            if e in self.metadata:
                df[e] = self.metadata[e]
            else:
                logging.warning(f"Metadata field {e} does not exist")
        return df


class GetDataStrategy(ParseStrategy):
    def __init__(self, file, metadata, **pd_kwargs):
        ParseStrategy.__init__(self, file, metadata, **pd_kwargs)

    def parse_data(self, include_metadata=[], **kwargs):
        column_names = self.metadata["fields"]

        if kwargs.get("headers_from_metadata", False):
            df = pd.read_csv(
                self.file,
                skiprows=kwargs.get("start_idx", 0),
                skipfooter=kwargs.get("end_idx", 0),
                names=["SECURITY", "ERROR_CODE", "NUM_FLDS"]
                + self.metadata["fields"],
                index_col=False,
                **self.pd_kwargs,
            )
        else:
            df = pd.read_csv(
                self.file,
                skiprows=kwargs.get("start_idx", 0),
                skipfooter=kwargs.get("end_idx", 0),
                names=["SECURITY", "ERROR_CODE", "NUM_FLDS"]
                + self.metadata["fields"],
                index_col=False,
                **self.pd_kwargs,
            )

        type_dict = df.dtypes.to_dict()
        for col in type_dict:
            if type_dict[col] == dtype("O"):
                df[col] = df[col].astype("str")

        for e in include_metadata:
            if e in self.metadata:
                df[e] = self.metadata[e]
            else:
                logging.warning(f"Metadata field {e} does not exist")
        return df


class BloombergParser:
    def __init__(self, file, headers_from_metadata=False, **pd_kwargs):
        self.file = file
        self.headers_from_metadata = headers_from_metadata
        self.pd_kwargs = pd_kwargs
        self.metadata = dict()
        self.metadata["fields"] = []
        self.start_idx = 0
        self.end_idx = 0
        self.line_count = 0
        self.check_file()
        self.extract_first_metadata()
        self.extract_last_metadata()
        self.parse_strategy = self.select_parse_strategy()
        print(self.metadata)

    def check_file(self):
        if os.path.getsize(self.file) == 0:
            raise ValueError("Empty file")

    def extract_first_metadata(self):
        with open(self.file, "r") as f:
            is_extracting_fields = False
            for line in f:
                self.start_idx += 1
                line = line.strip()
                if line == START_OF_DATA:
                    break
                elif line == START_OF_FIELDS:
                    is_extracting_fields = True
                elif line == END_OF_FIELDS:
                    is_extracting_fields = False
                else:
                    if is_extracting_fields:
                        self.metadata["fields"].append(line)
                    else:
                        kv = line.split("=")
                        self.metadata[kv[0]] = kv[1] if len(kv) != 1 else None

    def extract_last_metadata(self):
        last_line = 1
        line = read_n_to_last_line(self.file, last_line)
        while line != END_OF_DATA:
            kv = line.split("=")
            self.metadata[kv[0]] = kv[1] if len(kv) != 1 else None
            last_line += 1
            line = read_n_to_last_line(self.file, last_line)
        self.end_idx = last_line

    def select_parse_strategy(self):
        if self.metadata["PROGRAMNAME"].lower() in ["getsnap", "getdata"] and self.metadata.get("OUTPUTFORMAT","").lower() == "bulklist":
            return SnapStrategy(self.file, self.metadata, **self.pd_kwargs)
        if self.metadata["PROGRAMNAME"].lower() in ["getdata"]:
            return GetDataStrategy(self.file, self.metadata, **self.pd_kwargs)
        elif self.metadata["PROGRAMNAME"].lower() in ["gethistory"]:
            return HistoryStrategy(self.file, self.metadata, **self.pd_kwargs)
        else:
            raise ValueError(
                f'Program name {self.metadata["PROGRAMNAME"].lower()} not implemented'
            )

    def parse_data(self, include_metadata=[]):
        return self.parse_strategy.parse_data(
            include_metadata=include_metadata,
            start_idx=self.start_idx,
            end_idx=self.end_idx,
            headers_from_metadata=self.headers_from_metadata,
        )


if __name__ == "__main__":
    # file = "/jfs/tech1_share/pulkit.vora/bbg_per_security/eqvol/dvd_hist/responses/dp_eqvol2_2412241058.out"
    # file = "/jfs/tech1_share/pulkit.vora/bbg_per_security/eqvol/dvd_proj/responses/dp_eqvol1_2412241054.out"
    file = "/jfs/tech1_share/pulkit.vora/bbg_per_security/eqvol/splits_hist/responses/dp_eqvol3_2412241101.out"
    parser = BloombergParser(file, sep="|", skipinitialspace=True, on_bad_lines="warn")
    df_data = parser.parse_data()
    print(df_data)
    print(df_data.dtypes)
