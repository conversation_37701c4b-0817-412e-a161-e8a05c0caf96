from airflow import DAG
from airflow.operators.bash import BashOperator
from airflow.timetables.trigger import CronTriggerTimetable
from datetime import timedelta, datetime
import pendulum
from airflow.providers.http.operators.http import HttpOperator
from util.validate import check_for_anomalies
import os

dag_id = os.path.basename(__file__).replace(".py","")

JGDATA_PATH = os.environ.get("JGDATA_PATH")
BASH_COMAND = f"python3 {JGDATA_PATH}/bin/sec_gov.13f.py"

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 11, 4, tz="America/New_York"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1),
}


doc_md_DAG=f'This DAG downloads the data from Sec Gov 13F from website by scrap to JSF and to S3'
dag = DAG(
    dag_id=dag_id,
    default_args=default_args,
    description='This DAG downloads the data from Sec Gov 13F from website by scrap to JSF and to S3',
     schedule=CronTriggerTimetable('0 22 1-10 * *',timezone="America/New_York"), # runs on the 1st to 10th of each month at 22:00 NY time
    tags=["phdataa","gov_sec","13f"],
    catchup=False,
    doc_md=doc_md_DAG,
)

op_kwargs = {
    "dataset": "spglobal.estimates",
}

raw = BashOperator(
    task_id="jg-raw-eex-secgov-adv-gl-jfs-s3",
    bash_command=BASH_COMAND,
    dag=dag
)


validation_job = HttpOperator(
    task_id="call-daily-data-validation-api",
    http_conn_id="http_default", 
    endpoint=f"getJFSFeedAvailabilitySatusDailyDownload/{dag_id}",
    method="GET",
    headers={"Content-Type": "application/json"},
    response_check=check_for_anomalies,
    extra_options={"check_response": True},
    log_response=True,
    dag=dag
)

raw >> validation_job