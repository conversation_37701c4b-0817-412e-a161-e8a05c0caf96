name = 'spglobal.markit.ihs'
class = 'stcommon.infra.rawstore.SftpZipLoader'
schedule = '5 20 * * *'
tz = 'UTC'

[kwargs]
host = 'sftp1.spglobal.com'
disable_hostkey = 'True'
target = 's3://jg-data-dp-vendor-data/spglobal/markit/IHS/'
source = 'output'
extract_zip = true
remove_zip = true

syncdays = 4
region = 'na'

dates = [
    'Options/Credit_Index_Options_L-1530-$DATE$.zip',
    'Tranches/Credit_Index_Tranche_Composites_L-1530-$DATE$.zip'
]   
