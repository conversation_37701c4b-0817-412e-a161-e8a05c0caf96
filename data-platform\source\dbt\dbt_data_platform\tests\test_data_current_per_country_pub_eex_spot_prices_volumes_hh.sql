{{ 
    config( 
        tags=["eex_spot"]) 
}}


---- 30 Minute
---- Test conditionally with data values on/after 17:30 CET
with cur_cet_time as
(
select convert_timezone('UTC', 'Europe/Berlin', current_timestamp) cet_time
),
time_check as
(
    --select false as time_to_test
    select case when to_char(cet_time, 'HH24:MI') > '17:30' then true else false end as time_to_test
    from cur_cet_time
)
--select * from time_check
select 
    pv.country, max(case when t.time_to_test then timestamp::date  else null end) max_date
from 
    {{ ref("pub_eex_spot_price_volumes")}} pv
cross join time_check t
where granularity = '30 MIN'
group by pv.country,time_to_test
minus 
select distinct 
    country,
    case when t.time_to_test then  dateadd(day,1,current_date) else null end 
from {{ ref("pub_eex_spot_price_volumes")}} pv
cross join time_check t