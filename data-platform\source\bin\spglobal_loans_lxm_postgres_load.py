#!/tools/conda/envs/202405-1/bin/python
import sys, os, glob, argparse
import pandas as pd
from strunner import *
from sqlalchemy import create_engine, text
setupEnvironment() # sets up environment
from jgdata import *
from stcommon.infra.python.module import instance_from_config
from stcommon.infra.python.fileio import read_toml
import io
import re
import numpy as np
from stcommon.infra.rds.postgres_adaptor import PostgresAdaptor
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import boto3
from sqlalchemy import create_engine, text
import pandas as pd
from io import StringIO
from datetime import datetime, timedelta
import pytz


# Database configuration
DB_CONFIG = {
    'host': '',
    'dbname': 'fe_risk',
    'schema': 'crdt',
    'user': '',
    'password': '',
    'port': 5432
}


MARKS_COLUMN_TYPES = {
    "LoanX ID": str,
    "Mark Date": "datetime64[ns]",
    "Bid": np.float64,
    "Offer": np.float64,
    "Depth": "Int64",
    "Evaluated Price": np.float64,
    "Close Bid": np.float64,
    "Close Offer": np.float64,
    "Close Date": "datetime64[ns]",
    "Contributed": str
}

def get_engine():
    url = f"postgresql+psycopg2://{DB_CONFIG['user']}:{DB_CONFIG['password'].replace('@', '%40')}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['dbname']}"
    return create_engine(url, future=True)

def download_file_from_s3(s3, bucket, key):
    response = s3.get_object(Bucket=bucket, Key=key)
    return io.StringIO(response['Body'].read().decode('utf-8'))


def load_csv_files_from_s3(
    s3_bucket: str,
    s3_prefix: str,
    file_pattern: str,
    column_types: dict,
    table_name: str,
    date: str = None
):
    s3 = boto3.client("s3")
    paginator = s3.get_paginator('list_objects_v2')
    
    if date:
        try:
            date = datetime.strptime(date, '%Y-%m-%d')
            specific_file_pattern = f"{s3_prefix}LoanXMarks_NY_{date.strftime('%Y-%m-%d')}.csv"
            log.info(f"Using specific date pattern: {specific_file_pattern}")
            pattern = re.compile(specific_file_pattern)
        except ValueError as e:
            log.error(f"Date format error: {e}")
            return
    else:
        log.info(f"Using original file pattern: {file_pattern}")
        pattern = re.compile(file_pattern)

    matched_keys = []

    log.info(f"Scanning S3 prefix: s3://{s3_bucket}/{s3_prefix}")
    for page in paginator.paginate(Bucket=s3_bucket, Prefix=s3_prefix):
        for obj in page.get('Contents', []):
            key = obj['Key']
            if pattern.search(key):
                matched_keys.append(key)

    if not matched_keys:
        log.info(f"No matched files found for this execution where date was {date}")
    else:
        log.info(f"Found {len(matched_keys)} matching files for table {table_name}")
        log.info(matched_keys)
        engine = get_engine()
        for key in sorted(matched_keys):
            try:
                log.info(f"Processing file: {key}")
                buffer = download_file_from_s3(s3, s3_bucket, key)
                df = pd.read_csv(buffer, dtype=str)

                # Normalize column names
                df = df.rename(columns={col: col.strip() for col in df.columns})
                df.columns = [col.strip().lower().replace(" ", "_").replace("-", "_") for col in df.columns]

                # Keep only the expected columns
                expected_cols = [
                    col.lower().replace(" ", "_").replace("-", "_")
                    for col in column_types.keys()
                ]
                df = df[[col for col in df.columns if col in expected_cols]]

                # Handle blanks and cast
                df = df.replace("", pd.NA)
                typed_cols = {
                    col.lower().replace(" ", "_").replace("-", "_"): dtype
                    for col, dtype in column_types.items()
                    if col.lower().replace(" ", "_").replace("-", "_") in df.columns
                }

                # Explicit datetime formats
                if "mark_date" in df.columns:
                    df["mark_date"] = pd.to_datetime(df["mark_date"], format="%Y-%m-%d")

                if "price_timestamp" in df.columns:
                    df["price_timestamp"] = pd.to_datetime(
                        df["price_timestamp"],
                        format="%d-%b-%y %H:%M:%S",
                        utc=True
                    ).dt.tz_localize(None)

                if "depth" in df.columns:
                    df["depth"] = df["depth"].replace("implied", -1)
                    df["depth"] = df["depth"].replace(r"^\s*$", pd.NA, regex=True)  # replace empty strings or whitespace
                    df["depth"] = pd.to_numeric(df["depth"], errors="coerce").astype("Int64")

                df = df.astype(typed_cols)

                # Add load timestamp
                df["load_timestamp"] = pd.Timestamp.now(tz='America/New_York')

                with engine.begin() as conn:
                    log.info(f"Loading table {table_name}")

                    df.to_sql(
                        name=f'{table_name}',
                        con=conn,
                        schema=DB_CONFIG["schema"],
                        if_exists='append',
                        index=False,
                        chunksize=10000,
                        method='multi'
                    )
                    log.info(f"Finished writing to table {table_name}")                
                log.info(f"Inserted {len(df)} rows from {key}")
            except Exception as e:
                log.exception(f"Failed to process file {key}")


if __name__ == "__main__":

    parser = argparse.ArgumentParser(description="Load CSV files from S3")

    parser.add_argument("--date", required=False, help="Date for file pattern")

    args = parser.parse_args()

    bucket_name = 'jg-data-dp-vendor-data'
    prefix = 'spglobal/loans/1.0/Loans/'
    table_name = 'spg_loanx_marks_nyc'
    fpath = os.environ.get('JGDATA_PATH')
    
    if fpath is None:
        raise ValueError(f"Missing JGDATA_PATH Environment Variable")
    config_secret=read_toml(f'{fpath}/conf/sources/.secret.toml')
  
    #If you find the credentials in the secret.toml, use that.
    key_to_check = "postgres.fe.risk.prod" +".kwargs"

    if key_to_check in config_secret:
        DB_CONFIG['host'] = config_secret[key_to_check]['host']
        DB_CONFIG['user'] = config_secret[key_to_check]['postgres_fe_risk_username']
        DB_CONFIG['password'] = config_secret[key_to_check]['postgres_fe_risk_password']
    else:
        raise ValueError(f"No credentials found for: {key_to_check} - Check the secret toml file.")

    load_csv_files_from_s3(bucket_name, prefix, "LoanXMarks_NY_\d{4}-\d{2}-\d{2}\.csv$", MARKS_COLUMN_TYPES, table_name, date=args.date)
