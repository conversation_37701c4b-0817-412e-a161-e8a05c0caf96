from airflow import DAG
from airflow.providers.ssh.operators.ssh import SSHOperator
from airflow.providers.ssh.hooks.ssh import SSHHook
from datetime import datetime, timedelta
from airflow.timetables.trigger import CronTriggerTimetable
from airflow.providers.http.operators.http import HttpOperator
from util.jg_ssh_dbt_operator import JainGlobalSSHDBTOperator
import os, sys, pendulum, subprocess, logging
import pendulum
from airflow.hooks.base import BaseHook
from util.etl import check_for_anomalies
from airflow.operators.python import PythonOperator

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()



dag_id = os.path.basename(__file__).replace(".py", "")
ny_time = pendulum.now("America/New_York")
CURRENT_DATE = ny_time.strftime("%Y_%m_%d")

def get_jg_env():
    env_value = os.getenv('jg_env', 'prod').lower()
    if env_value not in ['prod', 'uat']:
        raise ValueError(f"Invalid jg_env value: {env_value}. Allowed values are 'prod' or 'uat'.")
    return env_value

env = get_jg_env()

source='ice' 
dataset = 'flash'
tag = 'flash+'
jsvc_path = '/opt/jsvc-datait'
source_path = f'{jsvc_path}/prod/JG-DATA-PLATFORM/source'
python_file_path = f'{source_path}/bin'
command_pipeline_rawdata = f'--dataset {source}.{dataset} --date {CURRENT_DATE}'
command_copy = f'--dataset {source}.{dataset} --date {CURRENT_DATE} --keypair'

conn = BaseHook.get_connection("dbt_conn_id")
key_file = conn.extra_dejson.get("path")


def download_ice_flash():
    

    JGDATA_PATH = os.environ.get("JGDATA_PATH")

    script_path = f"{JGDATA_PATH}/bin/ice_flash_downloader.py"
    command = f"python3 {script_path}"

    try:
        logging.info("Executing command: %s", command)
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise


default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.yesterday(tz="America/New_York"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

doc_md_DAG=f'Create dbt run command for {source}.{dataset}'
with DAG(
    dag_id=dag_id,
    default_args=default_args,
    description=f'This DAG runs ETL in DBT for {source} {dataset}',
    schedule= CronTriggerTimetable(
        cron="0-50/10 17-20 * * 1-5", # Every hour and every 10 minutes, minutes 10 through 50 past the hour, between 05:00 PM and 08:59 PM, Monday through Friday
        timezone="America/New_York"
    ),
    tags=["phdata", dataset, source, "dbt"],
    catchup=False,
    doc_md=doc_md_DAG
) as dag:


    ssh_hook = SSHHook(
        remote_host='*************',
        username='jsvc-datait',
        key_file=key_file,
        port=22
    )

    download_data = PythonOperator(
    task_id="run_raw_download_command",
    python_callable=download_ice_flash,
    dag=dag
    )

    run_python_command1 = SSHOperator(
        task_id='run_raw_data_command',
        ssh_hook=ssh_hook,
        command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_rawdata.py {command_pipeline_rawdata}',
        do_xcom_push=True,
        conn_timeout=None, # Set to None to disable timeout,
        cmd_timeout=None, # Set to None to disable timeout
    )


    run_python_command2 = SSHOperator(
        task_id='run_pipeline_sf_copy_command',
        ssh_hook=ssh_hook,
        command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_sf_copy.py {command_copy}',
        do_xcom_push=True,
        conn_timeout=None, # Set to None to disable timeout,
        cmd_timeout=None, # Set to None to disable timeout
    )


    run_dbt_command = JainGlobalSSHDBTOperator(
        task_id='run_dbt_command',
        ssh_hook=ssh_hook,
        base_command = 'run',
        profile = 'dbt_data_platform',
        select_args = [f"tag:{tag}"],
        target = env,
        env = env,
        timeout=None # Set to None to disable timeout
    )

    run_dbt_tests = JainGlobalSSHDBTOperator(
        task_id='run_dbt_test_ice_flash',
        ssh_hook=ssh_hook,
        base_command = 'test',
        profile = 'dbt_data_platform',
        select_args = [f"tag:{dataset}"],
        target = env,
        env = env,
    )


    validation_job = HttpOperator(
        task_id="call-daily-data-validation-api",
        http_conn_id="http_default_raw", 
        endpoint="getJFSFeedAvailabilitySatusDailyDownload/jgetl-dbt-ice-flash",
        method="GET",
        headers={"Content-Type": "application/json"},
        response_check=check_for_anomalies,
        extra_options={"check_response": True},
        log_response=True,
        dag=dag
    )
    


    download_data >> run_python_command1 >> run_python_command2 >> run_dbt_command >> run_dbt_tests >> validation_job