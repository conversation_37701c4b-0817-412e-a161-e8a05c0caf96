name=snowpipe_BPIPE_Tickdata_prod_poc
rest.port=8400
connector.class=com.snowflake.kafka.connector.SnowflakeSinkConnector
topics=bpipe-tick
snowflake.private.key.passphrase=sf_msk
snowflake.database.name=BLOOMBERG
snowflake.schema.name=SNAP
snowflake.topic2table.map=bpipe-tick:MSK_BBRG_TICK_RT_PROD
snowflake.url.name=byb06077.us-east-1.snowflakecomputing.com
snowflake.user.name=JSVC_MSK
snowflake.private.key=<PRIVATE_KEY>
snowflake.role.name=JG_MSK
snowflake.ingestion.method=snowpipe_streaming
value.converter.schemas.enable=false
jmx=true
key.converter=org.apache.kafka.connect.storage.StringConverter
value.converter=org.apache.kafka.connect.json.JsonConverter
errors.tolerance=all
offset.storage.file.filename=/tmp/connect.offsets
offset.storage.topic=connect-offsets
enable.auto.commit=false
offset.flush.interval.ms=100
tasks.max=1
buffer.count.records=100
buffer.flush.time=1
buffer.size.bytes=20000000
snowflake.streaming.max.client.lag=1
consumer.override.auto.offset.reset=earliest
consumer.override.fetch.min.bytes=1
consumer.override.fetch.max.wait.ms=5