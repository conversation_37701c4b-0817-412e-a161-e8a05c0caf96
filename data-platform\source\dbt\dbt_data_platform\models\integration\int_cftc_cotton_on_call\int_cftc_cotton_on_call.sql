{{
   config(
       tags=['cftc_cotton_on_call'],
       materialized='table',
   )
}}


WITH parsed AS (
 SELECT
   *,
   -- Extract year from filename (looking for 4-digit year)
   CAST(REGEXP_SUBSTR(filename, '\\d{4}') AS INTEGER) AS year_from_filename,
   -- Extract week number from filename (digits after 'week')
   CAST(REGEXP_SUBSTR(filename, 'week[_\\s]*(\\d+)', 1, 1, 'i', 1) AS INTEGER) AS week_from_filename
 FROM {{ ref("stg_cftc_cotton_on_call") }}
),


with_report_date AS (
 SELECT
   *,
   -- Calculate the report date based on the week number from filename
   -- Using ISO week standard where week 1 is the week with the first Thursday of the year
   CASE
     WHEN year_from_filename IS NOT NULL AND week_from_filename IS NOT NULL
     THEN DATEADD(
       'day',
       4 - DAYOFWEEK(DATE_FROM_PARTS(year_from_filename, 1, 1)),
       DATEADD('week', week_from_filename - 1, DATE_FROM_PARTS(year_from_filename, 1, 1))
     )
     ELSE NULL
   END AS report_date
 FROM parsed
),


futures_transformed AS (
 SELECT
   *,
   -- Transform futures_based_on column
   CASE
     -- Handle old format "March '01" -> "March 2001"
     WHEN REGEXP_LIKE(futures_based_on, '^\\w+\\s+''\\d{2}$')
     THEN CONCAT(
       REGEXP_SUBSTR(futures_based_on, '^\\w+'),
       ' 20',
       REGEXP_SUBSTR(futures_based_on, '\\d{2}$')
     )
     ELSE futures_based_on
   END AS futures_clean
 FROM with_report_date
),


final AS (
 SELECT
   -- Transform to CT futures symbols
   CONCAT(
     'CT',
     -- Month mapping
     CASE UPPER(REGEXP_SUBSTR(futures_clean, '^\\w+'))
       WHEN 'JANUARY' THEN 'F'
       WHEN 'FEBRUARY' THEN 'G'
       WHEN 'MARCH' THEN 'H'
       WHEN 'APRIL' THEN 'J'
       WHEN 'MAY' THEN 'K'
       WHEN 'JUNE' THEN 'M'
       WHEN 'JULY' THEN 'N'
       WHEN 'AUGUST' THEN 'Q'
       WHEN 'SEPTEMBER' THEN 'U'
       WHEN 'OCTOBER' THEN 'V'
       WHEN 'NOVEMBER' THEN 'X'
       WHEN 'DECEMBER' THEN 'Z'
     END,
     -- Year
     REGEXP_SUBSTR(futures_clean, '\\d{4}$')
   ) AS futures,
   unfixed_call_sales,
   change_from_previous_week_sales,
   unfixed_call_purchases,
   change_from_previous_week_purchases,
   at_close,
   change_from_previous_week_close,
   -- Date columns
   report_date,
   YEAR(report_date) AS year,
   MONTH(report_date) AS month,
   COALESCE(week_from_filename, NULL) AS report_week,
   -- Keep original columns for debugging if needed
   futures_based_on AS futures_original,
   filename,
   year_from_filename,
   week_from_filename
 FROM futures_transformed
)


SELECT
 futures,
 unfixed_call_sales,
 change_from_previous_week_sales,
 unfixed_call_purchases,
 change_from_previous_week_purchases,
 at_close,
 change_from_previous_week_close,
 report_date,
 year,
 month,
 report_week
FROM final
ORDER BY report_date, futures
