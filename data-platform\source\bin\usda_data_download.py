import argparse
import requests
import json
import os
import glob
import csv
import sys

from strunner import *
setupEnvironment() 
from stcommon.infra.rds.snowflake_bulk_loader import SnowflakeBulkLoader

parser = argparse.ArgumentParser(description="Process previous date")
parser.add_argument("--date", required=True, help="Specify the processing date in format YYYYMMDD")
args = parser.parse_args()
current_date = args.date
path="/jfs/tech1/apps/rawdata/usda_agri"



usda_urls={
"ESR":{
"ESR_regions": "https://api.fas.usda.gov/api/esr/regions",
"ESR_commodities": "https://api.fas.usda.gov/api/esr/commodities",
"ESR_countries": "https://api.fas.usda.gov/api/esr/countries",
"ESR_unitsOfMeasure": "https://api.fas.usda.gov/api/esr/unitsOfMeasure",
"ESR_datareleasedates": "https://api.fas.usda.gov/api/esr/datareleasedates"
},
"GATS": {
"GATS_census_exports_datareleasedates": "https://api.fas.usda.gov/api/gats/census/data/exports/dataReleaseDates",
"GATS_census_imports_datareleasedates": "https://api.fas.usda.gov/api/gats/census/data/imports/dataReleaseDates",
"GATS_UNTrade_exports_datareleasedates": "https://api.fas.usda.gov/api/gats/UNTrade/data/exports/dataReleaseDates",
"GATS_UNTrade_imports_datareleasedates": "https://api.fas.usda.gov/api/gats/UNTrade/data/imports/dataReleaseDates",
"GATS_regions": "https://api.fas.usda.gov/api/gats/regions",
"GATS_commodities": "https://api.fas.usda.gov/api/gats/commodities",
"GATS_HS6Commodities": "https://api.fas.usda.gov/api/gats/HS6Commodities",
"GATS_countries": "https://api.fas.usda.gov/api/gats/countries",
"GATS_unitsOfMeasure": "https://api.fas.usda.gov/api/gats/unitsOfMeasure",
"GATS_CustomsDistricts": "https://api.fas.usda.gov/api/gats/customsDistricts",
 },
"PSD":
 {
"PSD_api_regions":"https://api.fas.usda.gov/api/psd/regions",
 "PSD_api_countries":"https://api.fas.usda.gov/api/psd/countries",
 "PSD_api_uom":"https://api.fas.usda.gov/api/psd/unitsOfMeasure",
 "PSD_api_attributes":"https://api.fas.usda.gov/api/psd/commodityAttributes",
 "PSD_api_commodity":"https://api.fas.usda.gov/api/psd/commodities"
}

}


def read_config_secrets():
    config_secret_path = os.path.join('/jfs/tech1/apps/datait/jg-code/secure/prod', 'config_secret.json')
    with open(config_secret_path, 'r') as f:
        config_secret = json.load(f)
    return config_secret

config=read_config_secrets()
api_key=config["api_key"][0]


for key, api_details in usda_urls.items():
    for name, url in api_details.items():

        res=requests.get(url,headers={'x-api-key':api_key})
        if res.status_code == 200:
            # Parse the JSON response
            data = res.json()
        else:
            print(f"Failed! Status code: {res.status_code}")


        list_of_files = glob.glob(f'{path}/{name}_*.json') # * means all if need specific format then *.csv
        latest_file = max(list_of_files, key=os.path.getctime) if list_of_files else []
        content=""
        if latest_file:    
            with open(latest_file, 'r') as file:
                content = json.load(file)
        

        if data !=content:        
            # Save the data to a json file
            with open(f'{path}/{name}_{current_date}.json', 'w') as file:
                json.dump(data, file, indent=4)
            print(f"Data saved to {path}/{name}_{current_date}.json")

            # Save the data to a  CSV file
            keys = data[0].keys() 
                
            with open(f'{path}/{name}_{current_date}.csv', 'w', newline='', encoding='utf-8') as output_file:
                dict_writer = csv.DictWriter(output_file, fieldnames=keys)
                dict_writer.writeheader()
                dict_writer.writerows(data)

            print(f"Data saved to {path}/{name}_{current_date}.csv")


            
            if name=='PSD_api_commodity':

                    data_release_date_data=[]
                    for row in data:
                        commodityCode=row["commodityCode"]
                        domain=f"https://api.fas.usda.gov/api/psd/commodity/{commodityCode}/dataReleaseDates"
                        domain_res=requests.get(domain,headers={'x-api-key':api_key})
                        data_release_date_data.extend(domain_res.json())

                    if data_release_date_data:

                        with open(f'{path}/PSD_DATA_RELEASE_DATE_{current_date}.csv', 'w', newline='', encoding='utf-8') as output_file:
                            dict_writer = csv.DictWriter(output_file, fieldnames=data_release_date_data[0].keys())
                            dict_writer.writeheader()
                            dict_writer.writerows(data_release_date_data)

                        print(f"Data saved to {path}/PSD_DATA_RELEASE_DATE_{current_date}.csv")

