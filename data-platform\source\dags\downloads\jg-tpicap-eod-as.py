from airflow import DAG
from airflow.operators.python import <PERSON>Operator
from datetime import datetime, timedelta, date
import os, sys, pendulum, subprocess, logging
from airflow.providers.http.operators.http import HttpOperator
from util.validate import check_for_anomalies
from airflow.timetables.trigger import CronTriggerTimetable

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()
from stcommon.time import *
import stcommon.tools.dates as dates
# importlib.import_module('jgdata.datasets.tpicap.raw')
JGDATA_PATH = os.environ.get("JGDATA_PATH")

def run_TPICAP_EOD_script():
    schedule_type = 'HK_BOD'
    command = (
        f"python3 {JGDATA_PATH}/bin/tpicap.raw.eod.py --schedule_type {schedule_type}"
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2025, 2, 8, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id="jg-etl-tpicap-eod-as",
    description="This DAG runs ETL for TPICAP EOD",
    default_args=default_args,
    # schedule_interval='0 8-12 * * 1-6',
    schedule=CronTriggerTimetable('30 7 * * *', timezone="Asia/Hong_Kong"),
    tags=["jgdata","TPICAP"],
    catchup=False
)

etl_job = PythonOperator(
    task_id="jg-etl-TPICAP-EOD",
    python_callable=run_TPICAP_EOD_script,
    dag=dag
)

etl_job