name: Deploy to UAT DB

# on:
#   push:
#     branches:
#       - uat

on:
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy SQL to UAT Snowflake
    runs-on:
      group: core
    environment: uat

    env:
      SNOW<PERSON>AKE_CONNECTIONS_DEFAULT_USER: ${{ secrets.SN<PERSON><PERSON>A<PERSON>_USER }}
      SNOWFLAKE_CONNECTIONS_DEFAULT_PASSWORD: ${{ secrets.SNOWFLAKE_PASSWORD }}
      SNOWFLAKE_CONNECTIONS_DEFAULT_ACCOUNT: ${{ secrets.SNOWFLAKE_ACCOUNT }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Micromamba Setup
        run: |
          export MAMBA_EXE="/jfs/tools/conda/exec/micromamba"
          export MAMBA_ROOT_PREFIX="/jfs/tech1/conda/"
          eval "$($MAMBA_EXE shell hook --shell bash --root-prefix $MAMBA_ROOT_PREFIX)"
          micromamba activate tech1-datait
          echo "Micromamba environment activated."

      - name: Execute scripts via Snow CLI
        run: |

          snow git fetch my_git_repo
          snow git execute @my_git_repo/branches/uat/scripts/* \
            -D "env='uat'"
