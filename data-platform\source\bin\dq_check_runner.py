import toml
import sys
import os
from strunner import *
setupEnvironment() # sets up environment
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root_temp = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.append(project_root_temp)
print(project_root_temp)
from stcommon.infra.rds.snowflake_operation import SnowflakeDML
from stcommon import calendar
from stcommon.email_util_k8s import EmailUtility
import jglib.infra.python.fileio as fio
import argparse
from datetime import datetime
import uuid
import logging
import sys


logger = logging.getLogger("dq_check_runner")
logger.setLevel(logging.INFO)

console_handler = logging.StreamHandler(sys.stdout)
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
console_handler.setFormatter(formatter)

logger.addHandler(console_handler)

logger.info("DQ CHECK job started.")


class DatasetConfig:
    """Handles dataset configuration loaded from a TOML file."""

    def __init__(self, config_file):
        self.config = self._load_config(config_file)

    def _load_config(self, config_file):
        """Load the TOML file."""
        return toml.load(config_file)

    def get_query(self, dataset_name):
        """Fetch the SQL query for a dataset."""
        dataset = self.config.get("Dataset", {}).get(dataset_name)
        if not dataset:
            raise ValueError(f"No dataset configuration found for: {dataset_name}")
        query = dataset.get("Query")
        if not query:
            raise ValueError(f"No query found for dataset: {dataset_name}")
        return query


class DataQualityRunner:
    """Manages the execution of the data quality process."""

    def __init__(self, snowflake_dml, dataset_config):
        self.snowflake_dml = snowflake_dml
        self.dataset_config = dataset_config

    def create_temp_view(self, dataset_name):
        """Creates a temporary view in Snowflake."""
        query = self.dataset_config.get_query(dataset_name)
        print(f"Creating temporary view DATASET for {dataset_name}...")
        create_view_query = f"CREATE OR REPLACE TEMPORARY VIEW DATA_PLATFORM_CORE.EXCEPTIONS.DATASET AS {query}"
        self.snowflake_dml.execute_query(create_view_query)
        print("Temporary view created successfully.")

    def call_stored_procedure(self, ref_date, application_cd, uuid_cd):
        """Calls the Snowflake stored procedure."""
        print(f"Calling stored procedure for application {application_cd}...")
        procedure_call = f"CALL DATA_PLATFORM_CORE.EXCEPTIONS.RUN_DQ_EXCEPTIONS('{ref_date}', '{application_cd}', '{uuid_cd}')"
        print(f"Stored Procedure call Statement: {procedure_call}")
        self.snowflake_dml.execute_query(procedure_call)
        print("Stored procedure executed successfully.")

    def run(self, dataset_name, ref_date, application_cd, uuid_cd):
        """Main execution flow."""
        self.create_temp_view(dataset_name)
        self.call_stored_procedure(ref_date, application_cd, uuid_cd)



TABLE_STYLE = """
                <style>
                    table {
                        border-collapse: collapse;
                        width: auto; /* Auto width based on content */
                        max-width: 100%; /* Ensures it doesn't overflow */
                        font-size: 12px; /* Small font */
                    }
                    th {
                        background-color: #4CAF50; /* Green header */
                        color: white;
                        padding: 5px;
                        text-align: left;
                        border: 1px solid #ddd;
                    }
                    td {
                        padding: 5px;
                        text-align: left;
                        # border: 1px solid #ddd;
                        border: 1px solid black; 
                    }
                    tr:nth-child(odd) {
                        background-color: #3bdbed; 
                    }
                    tr:nth-child(even) {
                        background-color: #7ae8f5; 
                    }
                </style>
                """
                

def format_dataframe_html(df, title):
    return f"<strong>{title}:</strong><br>" + TABLE_STYLE + df.to_html(index=False, escape=False) + "<br><br>"


if __name__ == "__main__":
    
    default_uuid = str(uuid.uuid4())

    parser = argparse.ArgumentParser(description="Run Data Quality checks.")
    parser.add_argument("--dataset_name", required=True, help="The dataset name to process.")
    parser.add_argument("--application_cd", required=True, help="The application code.")
    parser.add_argument("--uuid", default=default_uuid, help="Optional UUID, default is a new UUID4.")

    args = parser.parse_args()

    CONFIG_FILE = project_root_temp + "/conf/sources/dq_check/dq_check.toml"
    DATASET_NAME = args.dataset_name  #APACST_JPM
    APPLICATION_CD = args.application_cd #APACST_JPM
    UUID = args.uuid
    
    REF_DATE = calendar.getPriorBusDate('gl',datetime.now().strftime("%Y%m%d"))
    REF_DATE = datetime.strptime(str(REF_DATE), "%Y%m%d").strftime("%Y-%m-%d")
    print(f"Ref Date: {REF_DATE}")
    

    SNOWFLAKE_PARAMS = {
        "db_name": "DATA_PLATFORM_CORE",
    }

 
    dataset_config = DatasetConfig(CONFIG_FILE)
    snowflake_dml =  SnowflakeDML(**SNOWFLAKE_PARAMS)
    
    #Comment it out if you want to test
    dq_runner = DataQualityRunner(snowflake_dml, dataset_config)
    dq_runner.run(DATASET_NAME, REF_DATE, APPLICATION_CD, UUID)
    
    #Enable for Testing
    # APPLICATION_CD = 'APACST_JPM'
    # UUID = '4d05233e-ffbe-4ae0-b4db-782e4add6c59'
    
    # EmailUtility Object intialization
    email_util = EmailUtility()
    current_date = datetime.now().strftime("%Y-%m-%d")
    
    # Snowflake query Call
    query_config = f'''
                select distinct f.CONFIG_ID, f.APPLICATION_CD, d.config_name  from DATA_PLATFORM_CORE.EXCEPTIONS.EXCEPTIONS f
                inner join DATA_PLATFORM_CORE.EXCEPTIONS.CHECK_CONFIG d on
                f.config_id = d.config_id and f.application_cd = d.application_cd where 
                f.APPLICATION_CD = '{APPLICATION_CD}'
                and f.RUN_ID = '{UUID}' 
                order by f.CONFIG_ID asc, f.APPLICATION_CD asc;
            '''
    print(query_config)
    df_config = snowflake_dml.fetch_query(query_config)
    
    query = f'''
                SELECT *
                FROM DATA_PLATFORM_CORE.EXCEPTIONS.EXCEPTIONS
                WHERE APPLICATION_CD='{APPLICATION_CD}' AND RUN_ID = '{UUID}'
                order by config_id asc, identifier asc;
            '''
    
    print(f"The exception data is fetch for app_code {APPLICATION_CD} and run id: {UUID}")
    print(f"Query Executed is {query}")        
    df_exceptions = snowflake_dml.fetch_query(query)
    
    if df_exceptions is not None:
        is_exception_present = not df_exceptions.empty

        body = (
            "Hi team,<br><br>The data looks good for the current run. No exceptions identified.<br>"
            if not is_exception_present
            else "Hi team,<br><br>The data check for the current run has failed. Exceptions have been identified and added to the DATA_PLATFORM_CORE.EXCEPTIONS.EXCEPTIONS table.<br><br>Please find the identified exceptions in the below table for review:<br>"
        )

        if df_config is not None and not df_config.empty:
            body += format_dataframe_html(df_config, "Configuration Details")

        if is_exception_present:
            body += format_dataframe_html(df_exceptions, "Exception Details")
            logger.error(f"[{APPLICATION_CD}][ERROR] Data Quality Check Report for {APPLICATION_CD} on {current_date} for RUN_ID: {UUID}")

            email_util.send_email(
                to_recipient=["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"],
                # to_recipient = ["<EMAIL>"],
                subject=f"[{'Success' if not is_exception_present else 'Failure'}] Data Quality Check Report for {APPLICATION_CD} on {current_date}",
                body=body,
                df=None
            )
        