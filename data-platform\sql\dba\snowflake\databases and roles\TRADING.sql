use role ACCOUNTADMIN;

CREATE DATABASE TRADING;

USE DATABASE TRADING;


CREATE ROLE IF NOT EXISTS DR_TRADING_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_TRADING_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_TRADING_DB_OWNER;

GRANT USAGE ON WAREHOUSE TRADING_PROD_WH TO ROLE DR_TRADING_READ_ONLY;
GRANT USAGE ON WAREHOUSE TRADING_PROD_WH TO ROLE DR_TRADING_READ_WRITE;
GRANT USAGE ON WAREHOUSE TRADING_PROD_WH TO ROLE DR_TRADING_DB_OWNER;


GRANT ROLE DR_TRADING_DB_OWNER TO ROLE FR_DATA_PLATFORM_LEADS;
GRANT OWNERSHIP ON DATABASE TRADING TO ROLE DR_TRADING_DB_OWNER;


GRANT USAGE ON DATABASE TRADING TO ROLE DR_TRADING_READ_ONLY;
GRANT USAGE ON DATABASE TRADING TO ROLE DR_TRADING_READ_WRITE;
GRANT USAGE ON DATABASE TRADING TO ROLE DR_TRADING_DB_OWNER;


GRANT ROLE DR_TRADING_DB_OWNER TO USER VIJAYVASUDEVAN;
GRANT ROLE DR_TRADING_DB_OWNER TO USER SVC_TRADING_PROD;

GRANT ROLE DR_TRADING_READ_WRITE TO USER JAMESOCALLAGHAN ;

GRANT ROLE DR_TRADING_READ_ONLY TO ROLE FR_TRADING_PROD ;





CREATE SCHEMA TRADING.TRD_CONFIG ;
GRANT ALL ON SCHEMA TRADING.TRD_CONFIG TO ROLE DR_TRADING_DB_OWNER;

GRANT USAGE ON SCHEMA TRADING.TRD_CONFIG TO ROLE DR_TRADING_READ_WRITE;
GRANT USAGE ON SCHEMA TRADING.TRD_CONFIG TO ROLE DR_TRADING_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA TRADING.TRD_CONFIG TO DR_TRADING_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA TRADING.TRD_CONFIG TO DR_TRADING_READ_ONLY;

GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA TRADING.TRD_CONFIG TO DR_TRADING_READ_WRITE;
GRANT SELECT  							   ON FUTURE VIEWS  IN SCHEMA TRADING.TRD_CONFIG TO DR_TRADING_READ_WRITE;




CREATE SCHEMA TRADING.TRD_POSITIONS ;
GRANT ALL ON SCHEMA TRADING.TRD_POSITIONS TO ROLE DR_TRADING_DB_OWNER;

GRANT USAGE ON SCHEMA TRADING.TRD_POSITIONS TO ROLE DR_TRADING_READ_WRITE;
GRANT USAGE ON SCHEMA TRADING.TRD_POSITIONS TO ROLE DR_TRADING_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA TRADING.TRD_POSITIONS TO DR_TRADING_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA TRADING.TRD_POSITIONS TO DR_TRADING_READ_ONLY;

GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA TRADING.TRD_POSITIONS TO DR_TRADING_READ_WRITE;
GRANT SELECT  							   ON FUTURE VIEWS  IN SCHEMA TRADING.TRD_POSITIONS TO DR_TRADING_READ_WRITE;



CREATE SCHEMA TRADING.TRD_OMS ;
GRANT ALL ON SCHEMA TRADING.TRD_OMS TO ROLE DR_TRADING_DB_OWNER;

GRANT USAGE ON SCHEMA TRADING.TRD_OMS TO ROLE DR_TRADING_READ_WRITE;
GRANT USAGE ON SCHEMA TRADING.TRD_OMS TO ROLE DR_TRADING_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA TRADING.TRD_OMS TO DR_TRADING_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA TRADING.TRD_OMS TO DR_TRADING_READ_ONLY;

GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA TRADING.TRD_OMS TO DR_TRADING_READ_WRITE;
GRANT SELECT  							   ON FUTURE VIEWS  IN SCHEMA TRADING.TRD_OMS TO DR_TRADING_READ_WRITE;



CREATE SCHEMA TRADING.TRD_SIGNALS ;
GRANT ALL ON SCHEMA TRADING.TRD_SIGNALS TO ROLE DR_TRADING_DB_OWNER;

GRANT USAGE ON SCHEMA TRADING.TRD_SIGNALS TO ROLE DR_TRADING_READ_WRITE;
GRANT USAGE ON SCHEMA TRADING.TRD_SIGNALS TO ROLE DR_TRADING_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA TRADING.TRD_SIGNALS TO DR_TRADING_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA TRADING.TRD_SIGNALS TO DR_TRADING_READ_ONLY;

GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA TRADING.TRD_SIGNALS TO DR_TRADING_READ_WRITE;
GRANT SELECT  							   ON FUTURE VIEWS  IN SCHEMA TRADING.TRD_SIGNALS TO DR_TRADING_READ_WRITE;



CREATE SCHEMA TRADING.TRD_DATA ;
GRANT ALL ON SCHEMA TRADING.TRD_DATA TO ROLE DR_TRADING_DB_OWNER;

GRANT USAGE ON SCHEMA TRADING.TRD_DATA TO ROLE DR_TRADING_READ_WRITE;
GRANT USAGE ON SCHEMA TRADING.TRD_DATA TO ROLE DR_TRADING_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA TRADING.TRD_DATA TO DR_TRADING_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA TRADING.TRD_DATA TO DR_TRADING_READ_ONLY;

GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA TRADING.TRD_DATA TO DR_TRADING_READ_WRITE;
GRANT SELECT  							   ON FUTURE VIEWS  IN SCHEMA TRADING.TRD_DATA TO DR_TRADING_READ_WRITE;
