#!/bin/bash

# Configuration and output file paths
config_file="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/conf/connectors_services_process.yaml"
output_file="/etc/node_exporter/connector_status.prom"
connectors=$(yq e '.connectors' "$config_file" -o=json)

# Infinite loop to run every 5 seconds
while true; do
    temp_file="/tmp/kafka_connector_status.prom.$$"
    echo "# HELP kafka_connector_status Connector status (1=RUNNING, 0=FAILED/NOT RUNNING)" > "$temp_file"
    echo "# TYPE kafka_connector_status gauge" >> "$temp_file"

    echo "=== $(date): Checking connector statuses ==="

    echo "$connectors" | jq -c '.[]' | while IFS= read -r entry; do
        prop_file=$(echo "$entry" | jq -r '.property_loc')
        server=$(echo "$entry" | jq -r '.server')

        if [[ ! -f "$prop_file" ]]; then
            echo "⚠️  Property file $prop_file not found! Skipping..."
            continue
        fi

        connector_name=$(grep '^name=' "$prop_file" | cut -d'=' -f2)
        port=$(grep '^rest.port=' "$prop_file" | cut -d'=' -f2)

        echo "🔍 Checking connector: $connector_name on $server:$port"

        response=$(curl -s "http://$server:$port/connectors/$connector_name/status")

        if [[ -z "$response" ]]; then
            echo "❌ Connector $connector_name on $server:$port is DOWN or unreachable."
            metric_value=-1.0
            total_tasks=0
            running_tasks=0
        else
            connector_state=$(echo "$response" | jq -r '.connector.state')
            total_tasks=$(echo "$response" | jq '.tasks | length')
            running_tasks=$(echo "$response" | jq '[.tasks[] | select(.state == "RUNNING")] | length')

            if [[ "$connector_state" == "RUNNING" ]]; then
                if [[ "$total_tasks" -gt 0 ]]; then
                    scaled_metric=$((running_tasks * 100 / total_tasks))
                    integer_part=$((scaled_metric / 100))
                    decimal_part=$((scaled_metric % 100))
                    metric_value="${integer_part}.${decimal_part}"
                else
                    metric_value=0.0
                fi
                echo "✅ Connector $connector_name is UP [$running_tasks/$total_tasks tasks]. Metric: $metric_value"
            else
                metric_value=0.0
                echo "⚠️  Connector $connector_name is NOT RUNNING. Metric: $metric_value"
            fi
        fi

        echo "kafka_connector_status{server=\"$server\",port=\"$port\",connector=\"$connector_name\",total_tasks=\"$total_tasks\",tasks_alive=\"$running_tasks\"} $metric_value" >> "$temp_file"
    done

    mv "$temp_file" "$output_file"
    chmod 644 "$output_file"
    echo "📦 Metrics written to $output_file"
    echo
    sleep 60
done
