
[Unit]
Description=TPICAP Connector Service
After=network.target
Requires=cwiqfs.service

[Service]
Type=simple
ExecStart=/bin/bash -c ' \
export MAMBA_EXE="/jfs/tools/conda/exec/micromamba"; \
export MAMBA_ROOT_PREFIX="/jfs/tech1/conda/"; \
__mamba_setup="$("$MAMBA_EXE" shell hook --shell bash --root-prefix "$MAMBA_ROOT_PREFIX" 2> /dev/null)"; \
eval "$__mamba_setup"; \
unset __mamba_setup; \
micromamba activate tech1-datait; \
/opt/jsvc-datait/kafka/bin/connect-standalone.sh /opt/jsvc-datait/kafka/snowpipe-streaming/kafka/tpicap-connect-standalone-prod.properties /opt/jsvc-datait/kafka/snowpipe-streaming/config/snowflakeconnector-tpicap-prod.properties >> /opt/data/process_logs/tp_icap_conn$(hostname)_$(date +%%Y%%m%%d)_prod.log 2>&1'
ExecStop=/bin/bash -c ' \
   pkill -f /opt/jsvc-datait//kafka/bin/connect-standalone.sh /opt/jsvc-datait/kafka/snowpipe-streaming/kafka/tpicap-connect-standalone-prod.properties /opt/jsvc-datait/kafka/snowpipe-streaming/config/snowflakeconnector-tpicap-prod.properties'
Restart=on-failure

[Install]
WantedBy=multi-user.target
 