--Change the Account role

use role ACCOUNTADMIN;

--Creating database

-- Database
create database APACPOWER_PROD ;

use database APACPOWER_PROD ;

-- Roles
CREATE ROLE IF NOT EXISTS DR_APACPOWER_PROD_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_APACPOWER_PROD_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_APACPOWER_PROD_DB_OWNER ;


--Granting ownership 

GRANT ROLE DR_APACPOWER_PROD_DB_OWNER TO ROLE ACCOUNTADMIN;
GRANT OWNERSHIP ON DATABASE APACPOWER_PROD TO ROLE DR_APACPOWER_PROD_DB_OWNER;


-- Grant usage on database to roles
GRANT USAGE ON DATABASE APACPOWER_PROD TO ROLE DR_APACPOWER_PROD_READ_ONLY;
GRANT USAGE ON DATABASE APACPOWER_PROD TO ROLE DR_APACPOWER_PROD_READ_WRITE;
GRANT USAGE ON DATABASE APACPOWER_PROD TO ROLE DR_APACPOWER_PROD_DB_OWNER;

-- Public Schema USAGE

GRANT USAGE ON SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_READ_ONLY;
GRANT USAGE ON SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_READ_WRITE;
GRANT USAGE ON SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_DB_OWNER;


-- CRUD Privileges for DB owner (SELECT, INSERT, UPDATE, DELETE, etc)

GRANT ALL PRIVILEGES ON SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_DB_OWNER;

GRANT ALL PRIVILEGES ON FUTURE TABLES IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_DB_OWNER;
GRANT ALL PRIVILEGES ON FUTURE VIEWS IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_DB_OWNER;

GRANT ALL ON ALL TABLES IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_DB_OWNER;
GRANT ALL ON ALL VIEWS  IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_DB_OWNER;

-- Privileges for READ_WRITE Role

GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_READ_WRITE;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL VIEWS  IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_READ_WRITE;

GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_READ_WRITE;
GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE VIEWS  IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_READ_WRITE;

-- Privileges for READ_ONLY Role

GRANT SELECT ON ALL TABLES IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_READ_ONLY;
GRANT SELECT ON ALL VIEWS  IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA APACPOWER_PROD.PUBLIC TO ROLE DR_APACPOWER_PROD_READ_ONLY;

-- Creating Functional Role

CREATE ROLE IF NOT EXISTS FR_APAC_POWER;
GRANT ROLE DR_APACPOWER_PROD_READ_ONLY TO ROLE FR_APAC_POWER;

-- Add members to functional role
GRANT ROLE DR_APACPOWER_PROD_DB_OWNER TO USER OONTONGTAN;


GRANT ROLE FR_APAC_POWER TO USER OONTONGTAN;
GRANT ROLE FR_APAC_POWER TO USER OWENLOGAN;
GRANT ROLE FR_APAC_POWER TO USER MUHAMMADABDULLAH;

GRANT ROLE FR_APAC_POWER TO USER NITINTHAKRAL;
GRANT ROLE FR_APAC_POWER TO USER PULKITVORA;

