import os
import json
import logging
from utils.postgres.adaptor import PostgresAdaptor

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

base_path = os.path.dirname(__file__)
with open(f"{base_path}/conf/spiderrock_api.json", "r") as f:
    config = json.load(f)

if __name__ == "__main__":
    logger.info("Resetting SpiderRock DB partitions...")

    loader = PostgresAdaptor(
        host=config["pg_host"],
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    loader.execute_query_no_ret("CALL eqvol.manage_sr_option_quote_partitions_hourly();")
    loader.execute_query_no_ret("CALL eqvol.manage_bbg_stock_quote_partitions_hourly();")

    logger.info("SpiderRock DB partitions reset successfully.")