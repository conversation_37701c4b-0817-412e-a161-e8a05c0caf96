create or replace view COMMOD.AGRI_USDA.PSD_ALL_COMM_DATA(
	COMMODITY_CODE,
	COMMODITY_NAME,
	COUNTRY_CODE,
	COUNTRY_NAME,
	MARKET_YEAR,
	<PERSON><PERSON><PERSON>AR_YEAR,
	MONTH,
	ATTRIB<PERSON><PERSON>_ID,
	ATTRIBUTE_NAME,
	UNIT_ID,
	UNIT_DESCRIPTION,
	VALUE,
	FILE_NAME,
	TIME_STAMP
) as
WITH cte AS (
SELECT
	t1.*,
	ROW_NUMBER() OVER(PARTITION BY commodity_code,
	country_code,
	market_year,
	MONTH,
	attribute_id,
	unit_id
ORDER BY
	time_stamp DESC) rnk
FROM
	vendor_raw.usda_commod.psd_all_commod_raw t1)
SELECT
	COMMODITY_CODE,
	COMMODITY_DESCRIPTION,
	COUNTRY_CODE,
	COUNTRY_NAME,
	MARKET_YEAR,
	CALENDAR_YEAR,
	MONTH,
	ATTRIBUTE_ID,
	ATTRIBUTE_DESCRIPTION,
	UNIT_ID,
	UNIT_DESCRIPTION,
	VALUE,
	FILE_NAME,
	TIME_STAMP
FROM
	cte
WHERE
	rnk = 1;
