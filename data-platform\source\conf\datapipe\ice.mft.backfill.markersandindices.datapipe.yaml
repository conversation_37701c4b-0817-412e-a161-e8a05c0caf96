raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/ice/mft/1.0"  ## Location of Raw Files
  s3_bucket: "jg-data-dp-vendor-data" ## S3 with Snowflake Acess
  s3_prefix: "ice/mft" 
  include_prefix: false

  structure: '[
    "Markers_and_Indices/EOD_FuturesIM_All_$DATE$*.csv"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "ICE_MFT"

  table_map:
    MARKERS_AND_INDICES_RAW:
      pattern: ".*EOD_FuturesIM_All_$DATE$.*.csv" ## Need to be a regex format
      col_num: 9
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/mft/Markers_and_Indices/" ##<stage name>/<stage path>
      file_format: "FF_ICE_MFT"

