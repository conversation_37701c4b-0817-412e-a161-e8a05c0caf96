import datetime
import os
from jgdata import *
from jgdata.datasets.hsbc.tradeconfirmation.__defs__ import tradeConfirmsSchema

HSBC_ROOT_PATH = f'{getRawStoreMountPath()}HSBC/trades/1.0'

HSBC_CONFIRMS_FIELDS = {
'Trade Date':'trade_date',
'Trade ID (Client)':'trade_id_client',
'Fund Name':'fund_name',
'EB':'executing_broker',
'Product Type':'product_type',
'BUY/SELL':'buysell',
'Client/EB Trade':'clienteb_trade',
'Currency Option Type':'currency_option_type',
'Currency 1':'currency_1',
'Notional Amount 1':'notional_amount_1',
'Currency 2 / Reference Currency':'currency_2_reference_currency',
'Notional / Reference Currency Amount 2':'notional_reference_currency_amount_2',
'Strike / Rate':'strike_rate',
'Currency Option Style':'currency_option_style',
'Valuation Date':'valuation_date',
'Expiration Date':'expiration_date',
'Value Date':'value_date',
'Settlement Rate Option':'settlement_rate_option',
'Settlement Type':'settlement_type',
'Expiration Time':'expiration_time',
'Expiration Zone':'expiration_zone',
'Settlement Currency':'settlement_currency',
'Settlement Amount':'settlement_amount',
'Premium Currency':'premium_currency',
'Premium Amount':'premium_amount',
'Premium Payment Date':'premium_payment_date',
'Calculation Agent / Barrier Determining Agent':'calculation_agent_barrier_determining_agent',
'Barrier Event Type':'barrier_event_type',
'Barrier Direction':'barrier_direction',
'Digital Event Rate Source':'digital_event_rate_source',
'Barrier Event Rate Source':'barrier_event_rate_source',
'Barrier Level':'barrier_level',
'Upper Barrier Level':'upper_barrier_level',
'Barrier Style':'barrier_style',
'Event Period Start Date and Time':'event_period_start_date_and_time',
'Event Period End Date and Time':'event_period_end_date_and_time',
'Payout Style':'payout_style',
'PM Settlement Location':'pm_settlement_location',
'Fixing Rate':'fixing_rate',
'Delta Amount 1':'delta_amount_1',
'Delta Amount 2':'delta_amount_2',
'Implied Volatility':'implied_volatility',
'Vega (USD)':'vega_usd',
'Reval Rate':'reval_rate',
'MTM (USD)':'mtm_usd',
'Fee (USD)':'fee_usd',
'Trade ID (HSBC)':'trade_id_hsbc',
'Trade ID (TRM)':'trade_id_trm',
'Regulatory Reference':'regulatory_reference',
'Matching Status':'matching_status',
'Allocation Status':'allocation_status',
'Option Status':'option_status',
'Trade Creation Time':'trade_creation_time',
'Trade Matching Time':'trade_matching_time',
'Notional (USD)':'notional_usd',
'Client Block ID':'client_block_id',
'UPI':'upi',
'ISIN':'isin',
'CFI':'cfi'
}

def parseFile(d, **kwargs):
  t = get_confirms_data(d)
  return t

def convert_dates(df, schema):
    for col, dtype in schema.items():
      actual_type = dtype.data_type if isinstance(dtype, schemafield) else dtype
      if actual_type == np.datetime64:
        # df[col] = pd.to_datetime(df[col], format="%d-%b-%Y").dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        df[col] = pd.to_datetime(df[col], format='mixed', errors='coerce')
        df[col] = df[col].dt.strftime("%Y-%m-%d %H:%M:%S.%f").str[:-3]
    return df

def get_confirms_data(d):

  # df_c = datalake.readDate('gl','hsbc.tradeconfirmation','confirms',d,colz=['fileName'])
  # files_loaded = df_c['fileName'].to_list()
  file_prefix = "JainGlobal_JGRPT_TradeConfirmation"

  cmd = f'ls -1 {HSBC_ROOT_PATH}/{file_prefix}_{d}*'
  r = runcmd(cmd)
  if r is None:
    raise ValueError(f"No files found in the directory - {HSBC_ROOT_PATH}/{file_prefix}_{d}*")
  files_with_full_path = r.split()
  files_without_full_path = [os.path.basename(x) for x in files_with_full_path]

  # files_available_to_load = [item for item in files_without_full_path if item not in files_loaded]

  df = []
  for file in files_without_full_path:
    t = read_csv(f'{HSBC_ROOT_PATH}/{file}',  encoding='utf-8')
    t['fileName']=file
    log.info(f"Processing {file}")

    filename_date = file.split('_')
    timestamp_str = f"{filename_date[3]} {filename_date[4].split('.')[0]}"
    timestamp_obj = datetime.strptime(timestamp_str, "%Y%m%d %H%M%S")
    t['avail_date'] = timestamp_obj.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    t['date'] = to_datetime(d)
    df.append(t)
  df = concat(df)
  df = rename_columns(df,HSBC_CONFIRMS_FIELDS)
  df = convert_dates(df, tradeConfirmsSchema())
  df = df.drop_duplicates(
    subset=['trade_date', 'trade_id_client', 'fileName', 'date'],
    keep='last'  
  )
  # print(df)
  return df
