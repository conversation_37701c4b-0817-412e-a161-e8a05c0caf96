import os
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging
from strunner import *
setupEnvironment()
import stcommon.tools.aws as aws
import datetime as dt
import argparse

logger = logging.getLogger()
logging.basicConfig(level=logging.INFO)


PAGE_URL = "https://www.sec.gov/data-research/sec-markets-data/form-13f-data-sets"
DOWNLOAD_PATH = "/jfs/tech1/apps/rawdata/sec_gov/13f/1.0"
S3_PATH  = "s3://jg-data-dp-vendor-data/sec_gov/13f/1.0"


def download_all_files(page_url, download_folder,all_files=False):

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'en-US,en;q=0.9,en-IN;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-CH-UA': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
        'Sec-CH-UA-Mobile': '?0',
        'Sec-CH-UA-Platform': '"Linux"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1'
    }
    
    response = requests.get(page_url, headers=headers)

    if response.status_code != 200:
        logger.error("Error fetching page: Status Code {}".format(response.status_code))
        return 

    soup = BeautifulSoup(response.text, "html.parser")
    
    zip_links = [
                    urljoin(page_url, a["href"]) for a in soup.find_all("a", href=True)
                        if a["href"].lower().endswith(".zip") 
                  
                 ] 
                 
    if not all_files:
        zip_links = [l for l in zip_links if str(dt.datetime.now().year) in l]

    logger.info(f"Found {len(zip_links)} zip files.")
    
    for zip_url in zip_links:
        download_zip_files(zip_url, download_folder, headers)
        time.sleep(0.5)
        
def download_zip_files(zip_url, download_folder, headers):
    
    filename = zip_url.split("/")[-1]
    file_path = os.path.join(download_folder, filename)

    if os.path.exists(file_path):
        logger.info(f"File already exists: {filename}")
        return

    response = requests.get(zip_url, headers=headers, stream=True)
    if response.status_code == 200:
        with open(file_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)
        logger.info(f"Downloaded: {filename}")
     
    else:
        logger.info(f"failed to download {zip_url} - status_code: {response.status_code}")

def send_to_s3(download_folder, s3_path):
    bucket, *target_list = s3_path.split('s3://')[1].split('/')
    for file in os.listdir(download_folder):  # Corrected variable name from 'files' to 'file'
        local_file_path = os.path.join(download_folder, file)  # Use download_folder for full path
        s3_file_path = os.path.join(*target_list, file)
        if aws.s3exists(bucket, s3_file_path):
            logger.info(f"s3://{bucket}/{s3_file_path} exists, skipping")
            continue
        logger.info(f"Uploading {local_file_path} to s3://{bucket}/{s3_file_path}")
        aws.s3put(local_file_path, bucket, s3_file_path)
        logger.info(f"Uploaded {file} to S3")
   
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--all", help="if included will download all files on pages, by default only current year files", action="store_true")
    options = parser.parse_args()
    
    os.makedirs(DOWNLOAD_PATH, exist_ok=True)
    download_all_files(PAGE_URL, DOWNLOAD_PATH, options.all)
    send_to_s3(DOWNLOAD_PATH, S3_PATH)
    logger.info(f"All files downloaded and uploaded to S3")