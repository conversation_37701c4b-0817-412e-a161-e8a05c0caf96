from confluent_kafka import Producer, KafkaException
import time
import json
import base64
import pandas as pd
import numpy as np
import logging, json, os, signal, traceback, time, argparse
import warnings
import snowflake.connector
logger = logging.getLogger()
objconfig = None

import os
import json
import base64
import time
import pandas as pd
import logging
from confluent_kafka import Producer
import snowflake.connector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailUtility:
    def __init__(self):
        """Initialize Email Utility with configuration."""
        self._topic = 'fo_tools_alerts'
        self._conf = {
            'bootstrap.servers': 'kafka1.jainglobal.net:9092,kafka2.jainglobal.net:9092',
            'message.max.bytes': 10485760  # 10 MB
        }
        self.producer = Producer(self._conf)
        self.TABLE_STYLE = """
                <style>
                    table {
                        border-collapse: collapse;
                        width: auto; /* Auto width based on content */
                        max-width: 100%; /* Ensures it doesn't overflow */
                        font-size: 12px; /* Small font */
                    }
                    th {
                        background-color: #4CAF50; /* Green header */
                        color: white;
                        padding: 5px;
                        text-align: left;
                        border: 1px solid #ddd;
                    }
                    td {
                        padding: 5px;
                        text-align: left;
                        # border: 1px solid #ddd;
                        border: 1px solid black; 
                    }
                    tr:nth-child(odd) {
                        background-color: #3bdbed; 
                    }
                    tr:nth-child(even) {
                        background-color: #7ae8f5; 
                    }
                </style>
                """

    def _load_config(self):
        """Load main configuration file."""
        config_path = os.environ.get('CONFIG_PATH', os.getcwd())
        config_file = os.path.join(config_path, 'config.json')

        if not os.path.exists(config_file):
            raise FileNotFoundError(f"Config file not found: {config_file}")

        with open(config_file, 'r') as f:
            return json.load(f)

    def _read_config_secrets(self):
        """Read sensitive secrets from config_secret.json."""
        config_secret_path = os.path.join(self.config["JG_CONFIG_PATH"], 'config_secret.json')

        if not os.path.exists(config_secret_path):
            raise FileNotFoundError(f"Secret config file not found: {config_secret_path}")

        with open(config_secret_path, 'r') as f:
            return json.load(f)

    def sf_query(self, conn_config, query):
        """Execute a query on Snowflake and return a DataFrame."""
        try:
            conn = snowflake.connector.connect(**conn_config)
            return pd.read_sql(query, conn)
        except Exception as e:
            logger.error(f"Error querying Snowflake: {e}")
            return None

    def send_email(self, to_recipient, subject, body, df=None, cc_recipient=None, from_=None, attachment=None):
        """
        Send an email with optional DataFrame content and attachment.
        
        :param to_recipient: List of email recipients.
        :param subject: Email subject.
        :param body: Email body/message included.
        :param df: DataFrame to include as an HTML table.
        :param cc_recipient: List of CC recipients (optional).
        :param from_: Sender email (optional).
        :param attachment: File path for attachment (optional).
        """
        if cc_recipient is None:
            cc_recipient = []
            
        email_body = body
        
        email_body += df.to_html() if df is not None else ""

        email_param = {
            "to": to_recipient,
            "cc": cc_recipient,
            "subject": subject,
            "from_": "<EMAIL>" if from_ is None else from_,
            "body": email_body,
        }

        if attachment:
            if not os.path.isfile(attachment):
                raise FileNotFoundError(f"Attachment file '{attachment}' does not exist.")
            
            with open(attachment, "rb") as file:
                encoded_attachment = base64.b64encode(file.read()).decode("utf-8")

            # email_param["attachment"] = {
            #     "file_name": os.path.basename(attachment),
            #     "content": encoded_attachment,
            #     "content_type": "text/csv"
            # }
            
            email_param["attachment"] = os.path.basename(attachment)
            email_param["attachment_content"] = encoded_attachment

        # Send email via Kafka
        timestamp = time.strftime('%Y%m%d%H%M%S')
        record_key = f'email_{timestamp}'
        self.producer.produce(self._topic, key=record_key, value=json.dumps(email_param), callback=self.delivery_report)
        self.producer.flush()

    @staticmethod
    def delivery_report(err, msg):
        """Kafka delivery report callback function."""
        if err:
            logger.error(f'Message delivery failed: {err}')
        else:
            logger.info(f'Message delivered to {msg.topic()} [{msg.partition()}] at offset {msg.offset()}')
                        
    
    def format_dataframe_html(self, df, title):
        return f"<strong>{title}:</strong><br>" + self.TABLE_STYLE + df.to_html(index=False, escape=False) + "<br><br>"
    

# # Usage Example
# if __name__ == "__main__":
#     email_util = EmailUtility()
    
#     email_util.config = email_util._load_config()
#     email_util.objconfig = email_util._read_config_secrets()
    
#     sf_conn_config={
#         "user":email_util.objconfig['sf_user'],
#         "password":email_util.objconfig['sf_password'],
#         "account":email_util.objconfig['sf_account'],
#         "warehouse":email_util.objconfig['sf_data_platform_core_warehouse'],
#         "database":email_util.objconfig['sf_bloomberg_database'],
#         "schema":email_util.objconfig['sf_data_platform_core_schema'],
#         "role":email_util.objconfig['sf_data_platform_core_owner'],
#     }

#     # Snowflake query Call
#     query = "SELECT REF_DATE, APPLICATION_CD, IDENTIFIER, VALUE, MESSAGE FROM DATA_PLATFORM_CORE.EXCEPTIONS.EXCEPTIONS ORDER BY RECORD_TS DESC LIMIT 100"
#     df_exceptions = email_util.sf_query(sf_conn_config, query)

#     if df_exceptions is not None:
#         email_util.send_email(
#             to_recipient=["<EMAIL>"],
#             subject="DQ Exceptions",
#             body="""Hi team,<br>                   
#                     Testing email utility.<br>
#                     Please find the dataframe added below<br><br>                
#                 """,
#             df=df_exceptions
            # attachment="test_ric_config.csv"
#         )
