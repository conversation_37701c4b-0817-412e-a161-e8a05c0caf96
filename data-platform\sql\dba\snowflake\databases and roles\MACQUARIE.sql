--Change the Account role

use role ACCOUN<PERSON>DMIN;

--Creating database

create database MACQUARIE ;

use database MACQUARIE ;


-- Roles
CREATE ROLE IF NOT EXISTS DR_MACQUARIE_DB_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_MACQUARIE_DB_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_MACQUARIE_DB_OWNER ;


--Granting ownership 

GRANT ROLE DR_MACQUARIE_DB_OWNER TO ROLE ACCOUNTADMIN;
GRANT OWNERSHIP ON <PERSON>AT<PERSON>ASE MACQUARIE TO ROLE DR_MACQUARIE_DB_OWNER;


-- Grant usage on database to roles
GRANT USAGE ON DAT<PERSON>AS<PERSON> MACQUARIE TO ROLE DR_MACQUARIE_DB_READ_ONLY;
GRANT USAGE ON DATABASE MACQUARIE TO ROLE DR_MACQUARIE_DB_READ_WRITE;
GRANT USAGE ON DATABASE MACQUARIE TO ROLE DR_MACQUARIE_DB_OWNER;

--Public Schema USAGE

GRANT USAGE ON SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_READ_ONLY;
GRANT USAGE ON SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_READ_WRITE;
GRANT USAGE ON SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_OWNER;


-- CRUD Privileges for DB owner (SELECT, INSERT, UPDATE, DELETE, etc)

GRANT ALL PRIVILEGES ON SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_OWNER;

GRANT ALL PRIVILEGES ON FUTURE TABLES IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_OWNER;
GRANT ALL PRIVILEGES ON FUTURE VIEWS IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_OWNER;

GRANT ALL ON ALL TABLES IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_OWNER;
GRANT ALL ON ALL VIEWS  IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_OWNER;

-- Privileges for READ_WRITE Role

GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_READ_WRITE;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL VIEWS  IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_READ_WRITE;

GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_READ_WRITE;
GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE VIEWS  IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_READ_WRITE;

-- Privileges for READ_ONLY Role

GRANT SELECT ON ALL TABLES IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_READ_ONLY;
GRANT SELECT ON ALL VIEWS  IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA MACQUARIE.PUBLIC TO ROLE DR_MACQUARIE_DB_READ_ONLY;



-- Schema & role creation for Schema Price

USE ROLE DR_MACQUARIE_DB_OWNER;

CREATE SCHEMA MACQUARIE.PRICE;

GRANT USAGE ON SCHEMA MACQUARIE.PRICE TO DR_MACQUARIE_DB_READ_WRITE;
GRANT USAGE ON SCHEMA MACQUARIE.PRICE TO DR_MACQUARIE_DB_READ_ONLY;

GRANT OWNERSHIP ON SCHEMA MACQUARIE.PRICE TO ROLE DR_MACQUARIE_DB_OWNER;


USE ROLE ACCOUNTADMIN; -- needed for FUTURE grants

GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA MACQUARIE.PRICE TO DR_MACQUARIE_DB_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA MACQUARIE.PRICE TO DR_MACQUARIE_DB_OWNER;

GRANT ALL ON SCHEMA MACQUARIE.PRICE TO DR_MACQUARIE_DB_OWNER;

GRANT ROLE DR_MACQUARIE_DB_OWNER TO ROLE FR_CORE_SERVICES_DB_ADMINS;






