-- TODO: Replace SECURITY_MASTER_UAT.ST_CHECK with SECURITY_MASTER.ST_CHECK once the latter has been created

CREATE TABLE SECURITY_MASTER.GSC_ENRICHMENT.JGIDS(
	MARKET_INSTRUMENT_CHARACTERISTICS_SOK VARCHAR,
    <PERSON><PERSON><PERSON> VARCHAR(14),
    REASON VARCHAR
)
COMMENT = 'Temporary table to maintain JGID per GoldenSource Equity listing in the REFINED.GSC_MARKET_INSTRUMENT_CHARACTERISTICS table with primary key MARKET_INSTRUMENT_CHARACTERISTICS_SOK. Once these ids are maintained in the GoldenSource ODS DB, this table will be dropped.';

GRANT SELECT ON TABLE SECURITY_MASTER.GSC_ENRICHMENT.JGIDS TO DR_SECURITY_MASTER_READER;

-- 1) Find listings that still should be assigned a JGID
CREATE OR REPLACE TEMPORARY TABLE JGID_CANDIDATE_LISTINGS AS
WITH 
ranked_inst AS (
  SELECT
    inst.*,
    ROW_NUMBER() OVER (
      PARTITION BY inst.instrument_key_sok
      ORDER BY
        inst.LAST_CHANGE_DATETIME DESC
    ) AS rnk
  FROM
    GOLDENSOURCE.refined.gsc_financial_instrument AS inst
  WHERE inst.version_start_timestamp <= (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN) 
  AND inst.version_end_timestamp > (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN)
),
ranked_mkch AS (
  SELECT
    mkch.*,
    ROW_NUMBER() OVER (
      PARTITION BY mkch.market_instrument_characteristics_sok
      ORDER BY
        mkch.LAST_CHANGE_DATETIME DESC
    ) AS rnk
  FROM
    GOLDENSOURCE.refined.gsc_market_instrument_characteristics AS mkch
    -- repetetive inline scalar subquery is much faster than easier to read CTE :(
  WHERE mkch.version_start_timestamp <= (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN) 
  AND mkch.version_end_timestamp > (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN)
)
SELECT 
    -- debug columns, uncomment as needed
    mkch.LAST_CHANGE_DATETIME,
    mkch.version_start_timestamp,
    mkch.version_end_timestamp,
    inst.ISIN_ID,
    mkch.BB_EXCHANGE_TICKER_ID,
    mkch.BB_COMPOSITE_TICKER_ID,
    mkch.ADDITIONAL_LISTING_QUALIFICATION_TYPE,
    mkch.BB_GLOBAL_ID,
    mkch.BB_COMPOSITE_GLOBAL_ID,
    mkch.GEOGRAPHIC_UNIT_ID,
    mkch.TRADING_CURRENCY_CODE,
    mkch.PRIMARY_TRADING_MARKET_IND,
    
    mkch.MARKET_INSTRUMENT_CHARACTERISTICS_SOK,
    tfigis.TRADING_FIGI
    
from ranked_mkch mkch
left join ranked_inst inst on inst.instrument_key_sok = mkch.instrument_key_sok
left join SECURITY_MASTER.GSC_ENRICHMENT.TRADING_FIGIS tfigis on  mkch.MARKET_INSTRUMENT_CHARACTERISTICS_SOK = tfigis.MARKET_INSTRUMENT_CHARACTERISTICS_SOK
    -- choose latest version of GoldenSource listing record for the effective date
    where mkch.rnk = 1 and inst.rnk = 1
    -- only pick equity-like instruments - non-equity securities probably can have a simpler logic
    and inst.instrument_type in ('EQSHR', 'RECEIPTS', 'PFD', 'LTDPART', 'REALESTA', 'FUND', 'UNIT')
    -- only pick euro markets for initial scope - extend as necessary to eventually cover all equities
    and mkch.GEOGRAPHIC_UNIT_ID in ('AT', 'BE', 'CH', 'DE', 'DK', 'ES', 'FI', 'FR', 'GB', 'IE', 'IT', 'NL', 'NO', 'PT', 'SE', 'US', 'ZA')
    -- only pick listings with exchange ticker & compo figi
    and mkch.BB_EXCHANGE_TICKER_ID is not null and mkch.BB_COMPOSITE_TICKER_ID is not null
    -- only pick records whose MARKET_INSTRUMENT_CHARACTERISTICS_SOK has not yet been added to the JGIDS table (manually review & update those if needed)
    and mkch.MARKET_INSTRUMENT_CHARACTERISTICS_SOK not in (SELECT MARKET_INSTRUMENT_CHARACTERISTICS_SOK FROM SECURITY_MASTER.GSC_ENRICHMENT.JGIDS);
    ;


-- 2) Find all ISIN/TRADING_CURRENCY pairs with a single TRADING_FIGI and use its composite FIGI as JGID
CREATE OR REPLACE TEMPORARY TABLE NEW_JGIDS_FROM_UNIQUE_TRADING_FIGIS AS
WITH 
unique_trading_figi AS (
    select ISIN_ID, TRADING_CURRENCY_CODE
    from JGID_CANDIDATE_LISTINGS
    where TRADING_FIGI is not null
    group by ISIN_ID, TRADING_CURRENCY_CODE
    having COUNT(TRADING_FIGI) = 1
    ),
unique_trading_figi_jgids AS (
    select a.ISIN_ID, a.TRADING_CURRENCY_CODE, CONCAT('JG', BB_COMPOSITE_GLOBAL_ID) as JGID
    from unique_trading_figi a
    left join JGID_CANDIDATE_LISTINGS b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE)
    where TRADING_FIGI is not null
    )
select a.MARKET_INSTRUMENT_CHARACTERISTICS_SOK, b.JGID, 
    CONCAT('Unique TradingFigi for (', b.ISIN_ID, ', ', b.TRADING_CURRENCY_CODE, ') on ', (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN)) as REASON
    from JGID_CANDIDATE_LISTINGS a
    join unique_trading_figi_jgids b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE)
;



-- 3) Find all ISIN/TRADING_CURRENCY pairs with a multiple TRADING_FIGIs
CREATE OR REPLACE TEMPORARY TABLE multiple_trading_figis AS
WITH
multiple_trading_figi_isins AS (
    select ISIN_ID, TRADING_CURRENCY_CODE, COUNT(TRADING_FIGI)
    from JGID_CANDIDATE_LISTINGS
    where TRADING_FIGI is not null
    group by ISIN_ID, TRADING_CURRENCY_CODE
    having COUNT(TRADING_FIGI) > 1
    )
select b.ISIN_ID, b.TRADING_CURRENCY_CODE, BB_COMPOSITE_GLOBAL_ID, TRADING_FIGI, PRIMARY_TRADING_MARKET_IND, GEOGRAPHIC_UNIT_ID
    from multiple_trading_figi_isins a
    left join JGID_CANDIDATE_LISTINGS b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE)
    where TRADING_FIGI is not null
    ;


-- 3a) Find all non-primary exchange TRADING_FIGIs that are composite Figis and use composite Figi as JGID for all listings with the same composite Figi
CREATE OR REPLACE TEMPORARY TABLE NEW_JGIDS_FROM_MULTIPLE_TRADING_FIGIS_COMPOSITES AS
WITH
compo_trading_figis AS (
    select * from multiple_trading_figis
    where BB_COMPOSITE_GLOBAL_ID = TRADING_FIGI and PRIMARY_TRADING_MARKET_IND = 'N'
    )
select a.MARKET_INSTRUMENT_CHARACTERISTICS_SOK, CONCAT('JG', b.BB_COMPOSITE_GLOBAL_ID) as JGID, 
    CONCAT('Use Composite TradingFigi out of multiple TradingFigis for (', b.ISIN_ID, ', ', b.TRADING_CURRENCY_CODE, ') on ', (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN)) as REASON
    from JGID_CANDIDATE_LISTINGS a
    join compo_trading_figis b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE and a.BB_COMPOSITE_GLOBAL_ID = b.BB_COMPOSITE_GLOBAL_ID)
    ;


-- 3b) Find all non-primary exchange TRADING_FIGIs that are exchange Figis (= are not composite Figis)
CREATE OR REPLACE TEMPORARY TABLE exchange_trading_figis_per_country AS
select ISIN_ID, TRADING_CURRENCY_CODE, GEOGRAPHIC_UNIT_ID, COUNT(TRADING_FIGI) as TRADING_FIGI_COUNT
    from multiple_trading_figis
    where BB_COMPOSITE_GLOBAL_ID <> TRADING_FIGI and PRIMARY_TRADING_MARKET_IND = 'N'
    group by ISIN_ID, TRADING_CURRENCY_CODE, GEOGRAPHIC_UNIT_ID;

-- 3b.i) Check that all exchange Trading Figis per country are unique: If the below query returns any rows, these will need to be handled explicitly
select * from exchange_trading_figis_per_country where TRADING_FIGI_COUNT <> 1;

-- 3b.ii) Find all non-primary unique exchange TRADING_FIGIs per country and use its composite Figi as JGID
CREATE OR REPLACE TEMPORARY TABLE NEW_JGIDS_FROM_MULTIPLE_TRADING_FIGIS_BY_COUNTRY AS
WITH
unique_exchange_trading_figis_per_country AS (
    select a.* from multiple_trading_figis a
    left join exchange_trading_figis_per_country b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE and a.GEOGRAPHIC_UNIT_ID = b.GEOGRAPHIC_UNIT_ID)
    where b.TRADING_FIGI_COUNT = 1
    )
select a.MARKET_INSTRUMENT_CHARACTERISTICS_SOK, CONCAT('JG', b.BB_COMPOSITE_GLOBAL_ID) as JGID, 
    CONCAT('Use Composite Figi of Exchange TradingFigi listing out of multiple TradingFigis for all ', b.GEOGRAPHIC_UNIT_ID, ' listings of (', b.ISIN_ID, ', ', b.TRADING_CURRENCY_CODE, ') on ', (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN)) as REASON
    from JGID_CANDIDATE_LISTINGS a
    join unique_exchange_trading_figis_per_country b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE and a.GEOGRAPHIC_UNIT_ID = b.GEOGRAPHIC_UNIT_ID);


-- 3c) Find all primary exchange TRADING_FIGIs 
CREATE OR REPLACE TEMPORARY TABLE primary_exchange_trading_figis AS
select ISIN_ID, TRADING_CURRENCY_CODE, PRIMARY_TRADING_MARKET_IND, COUNT(TRADING_FIGI) as TRADING_FIGI_COUNT
    from multiple_trading_figis
    where PRIMARY_TRADING_MARKET_IND = 'Y'
    group by ISIN_ID, TRADING_CURRENCY_CODE, PRIMARY_TRADING_MARKET_IND;

-- 3c.i) Check that all primary exchange Trading Figis are unique: If the below query returns any rows, these will need to be handled explicitly
select * from primary_exchange_trading_figis where TRADING_FIGI_COUNT <> 1;

-- 3c.ii) Find all primary exchange TRADING_FIGIs and use its composite Figi as JGID for all remaining listing not already covered by 4a) and 4b)
CREATE OR REPLACE TEMPORARY TABLE NEW_JGIDS_FROM_MULTIPLE_TRADING_FIGIS_PRIMARY_EXCHANGE AS
WITH
unique_primary_exchange_trading_figis AS (
    select a.* from multiple_trading_figis a
    left join primary_exchange_trading_figis b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE and a.PRIMARY_TRADING_MARKET_IND = b.PRIMARY_TRADING_MARKET_IND)
    where b.TRADING_FIGI_COUNT = 1
    )
select a.MARKET_INSTRUMENT_CHARACTERISTICS_SOK, CONCAT('JG', b.BB_COMPOSITE_GLOBAL_ID) as JGID, 
    CONCAT('Use Composite Figi of Primary Exchange listing out of multiple TradingFigis for all remaining listings of (', b.ISIN_ID, ', ', b.TRADING_CURRENCY_CODE, ') on ', (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN)) as REASON
    from JGID_CANDIDATE_LISTINGS a
    join unique_primary_exchange_trading_figis b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE)
    where a.MARKET_INSTRUMENT_CHARACTERISTICS_SOK not in (select MARKET_INSTRUMENT_CHARACTERISTICS_SOK from NEW_JGIDS_FROM_MULTIPLE_TRADING_FIGIS_COMPOSITES)
    and a.MARKET_INSTRUMENT_CHARACTERISTICS_SOK not in (select MARKET_INSTRUMENT_CHARACTERISTICS_SOK from NEW_JGIDS_FROM_MULTIPLE_TRADING_FIGIS_BY_COUNTRY)
    ;


-- 4) Insert all newly identified JGIDs into JGID table
INSERT INTO SECURITY_MASTER.GSC_ENRICHMENT.JGIDS
select * from NEW_JGIDS_FROM_UNIQUE_TRADING_FIGIS 
    union select * from NEW_JGIDS_FROM_MULTIPLE_TRADING_FIGIS_COMPOSITES
    union select * from NEW_JGIDS_FROM_MULTIPLE_TRADING_FIGIS_BY_COUNTRY
    union select * from NEW_JGIDS_FROM_MULTIPLE_TRADING_FIGIS_PRIMARY_EXCHANGE
    ;
