import os
import subprocess
import snowflake.connector
from pathlib import Path
from difflib import unified_diff
import sqlparse
import re

# Set ROOT to repo root, not sql/snowflake
ROOT_SQL_PATH = os.environ.get(
    "ROOT_SQL_PATH",
    "/mnt/c/Users/<USER>/Codebase/prod-data-platform/data-platform"
)

def get_changed_sql_files():
    current_dir = os.getcwd()
    os.chdir(ROOT_SQL_PATH)
    try:
        result = subprocess.run(
            ["git", "diff", "--name-only", "origin/main...HEAD", "--", "*.sql"],
            stdout=subprocess.PIPE, text=True
        )
        return [line.strip() for line in result.stdout.splitlines() if line.endswith(".sql")]
    finally:
        os.chdir(current_dir)

def parse_path(filepath):
    parts = Path(filepath).parts
    if len(parts) < 4:
        raise ValueError(f"Unexpected file structure: {filepath}")
    db = parts[-4]
    schema = parts[-3]
    object_type = parts[-2].lower()
    object_name = Path(filepath).stem
    return db, schema, object_type, object_name

def connect_snowflake():
    return snowflake.connector.connect(
        user=os.environ['SNOWFLAKE_USER'],
        password=os.environ['SNOWFLAKE_PASSWORD'],
        account=os.environ['SNOWFLAKE_ACCOUNT']
    )

def get_ddl(conn, db, schema, obj_type, obj_name):
    obj_type_map = {
        'tables': 'TABLE',
        'views': 'VIEW',
        'procedures': 'PROCEDURE',
        'stages': 'STAGE'
    }
    qualified_name = f'"{db}"."{schema}"."{obj_name}"'
    cursor = conn.cursor()
    cursor.execute(f"SELECT GET_DDL('{obj_type_map[obj_type]}', {qualified_name!r})")
    return cursor.fetchone()[0]

def normalize_sql(sql):
    sql = sqlparse.format(sql, keyword_case='upper', identifier_case='upper', strip_comments=True, reindent=True)
    return simplify_types(sql)

def simplify_types(sql):
    # Normalize any VARCHAR(n) to VARCHAR
    sql = re.sub(r'VARCHAR\s*\(\s*\d+\s*\)', 'VARCHAR', sql, flags=re.IGNORECASE)

    # Normalize any NUMBER(p,s) to NUMBER
    sql = re.sub(r'NUMBER\s*\(\s*\d+\s*,\s*\d+\s*\)', 'NUMBER', sql, flags=re.IGNORECASE)

    # Normalize TIMESTAMP_NTZ(n) to TIMESTAMP_NTZ
    sql = re.sub(r'TIMESTAMP_NTZ\s*\(\s*\d+\s*\)', 'TIMESTAMP_NTZ', sql, flags=re.IGNORECASE)

    # Simplify AUTOINCREMENT clause (any START/INCREMENT)
    sql = re.sub(
        r'AUTOINCREMENT\s+START\s+\d+\s+INCREMENT\s+\d+(\s+NOORDER|\s+ORDER)?',
        'AUTOINCREMENT',
        sql,
        flags=re.IGNORECASE
    )

    # Normalize INT to NUMBER
    sql = re.sub(r'\bINT\b', 'NUMBER', sql, flags=re.IGNORECASE)

    # Remove extra qualifiers like NOORDER
    sql = re.sub(r'\s+NOORDER\b', '', sql, flags=re.IGNORECASE)

    return sql


def compare_ddl(file_content, db_ddl):
    diff = unified_diff(
        db_ddl.splitlines(),
        file_content.splitlines(),
        fromfile='snowflake_db',
        tofile='git_file',
        lineterm=''
    )
    return "\n".join(diff)

def main():
    print("ROOT PATH =", ROOT_SQL_PATH)
    changed_files = get_changed_sql_files()
    if not changed_files:
        print("No changed SQL files detected.")
        return

    conn = connect_snowflake()
    for file in changed_files:
        try:
            db, schema, obj_type, obj_name = parse_path(file)
            abs_path = os.path.join(ROOT_SQL_PATH, file)
            print(f"Checking: {db}.{schema}.{obj_type}.{obj_name}")
            print(f"→ Reading file from: {abs_path}")
            with open(abs_path) as f:
                file_sql_raw = f.read()
            file_sql = normalize_sql(file_sql_raw)
            db_sql = normalize_sql(get_ddl(conn, db, schema, obj_type, obj_name))
            diff = compare_ddl(file_sql, db_sql)
            if diff:
                print(f"\n❗Diff detected in {file}:\n{diff}\n")
            else:
                print("No drift.\n")
        except Exception as e:
            print(f"Error processing {file}: {e}")

if __name__ == "__main__":
    main()
