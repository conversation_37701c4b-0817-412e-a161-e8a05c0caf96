from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.providers.http.operators.http import HttpOperator
from datetime import datetime, timedelta, date
import os, sys, pendulum, subprocess, logging, pytz
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from airflow.timetables.trigger import CronTriggerTimetable
from strunner import *
setupEnvironment()
from util.validate import check_for_anomalies

today = datetime.now(tz=pytz.timezone('America/New_York'))
date_pattern = today.strftime('%Y%m%d')
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)

def download():
    command = (
        f"python3 {DATA_PIPELINE_PATH}/factset/sync_factset_files.py"
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 8, 3, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id='jg-sync-factset',
    default_args=default_args,
    description='This DAG downloads the daily files for Factset',
    schedule=CronTriggerTimetable('30 * * * *', timezone="America/New_York"),
    tags=["jgdata", "factset"],
    max_active_runs = 1,
    catchup=False,
)

download_op = PythonOperator(
    task_id="jg-sync-factset",
    python_callable=download,
    dag=dag
)

validation_job = HttpOperator(
    task_id="call-daily-data-validation-api",
    http_conn_id="http_default", 
    endpoint="getJFSFeedAvailabilitySatusDailyDownload/jg-sync-factset",
    method="GET",
    headers={"Content-Type": "application/json"},
    response_check=check_for_anomalies,
    extra_options={"check_response": True},
    log_response=True,
    dag=dag
)

download_op >> validation_job
