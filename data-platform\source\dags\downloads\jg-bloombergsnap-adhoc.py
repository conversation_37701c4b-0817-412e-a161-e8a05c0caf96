from airflow.decorators import dag, task
from airflow.models.param import Param
from datetime import timedelta
import pendulum
import logging
import subprocess
import os
import json
import sys
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()
import stcommon.tools.dates as dates
JGDATA_PATH = os.environ.get("JGDATA_PATH")

@dag(
    default_args={
        "owner": "airflow",
        "depends_on_past": False,
        "start_date": pendulum.datetime(2025, 3, 10, tz="Asia/Singapore"),
        "email": ["<EMAIL>"],
        "email_on_failure": False,
        "email_on_retry": False,
        "retries": 1,
        "retry_delay": timedelta(minutes=1),
    },
    schedule=None,
    catchup=False,
    tags=["adhoc", "bloomberg"],
    params={
        "snap_configs": Param(
            
                [
                 "IDENTIFIER=ABC, SNAP_TYPE=BDH, ATTRIBUTE=PX_LAST",
                 "IDENTIFIER=XYZ, SNAP_TYPE=BDP, ATTRIBUTE=PX_LAST"
                ]
                ,
            type="array",
            description="List of dicts with IDENTIFIER, FIELD, and SNAP_TYPE (bdp/bdh)",
            title="Snap Configs"
        )
    },
)
def jg_bloombergsnap_adhoc():

    @task
    def get_snap_configs(params=None):
        if not params or "snap_configs" not in params:
            logging.warning("No snap_configs found in params.")
            return []
        return params["snap_configs"]

    @task
    def run_bsnap_adhoc(snap_configs):
        snap_config_str = ";".join(snap_configs)
        logging.info(f"snap_config_str: {snap_config_str}")
        command = f"python3 {JGDATA_PATH}/bin/bloomberg.snap.adhoc.py --snap_configs '{snap_config_str}'"
        
        try:
            logging.info(f"Running command: {command}")
            result = subprocess.run(
                command,
                shell=True,
                check=True,
                capture_output=True,
                text=True
            )
            logging.info("Command output: %s", result.stdout)
            logging.info("Command error output: %s", result.stderr)
        except subprocess.CalledProcessError as e:
            logging.error("Command failed with exit status %d", e.returncode)
            logging.error("STDOUT: %s", e.stdout)
            logging.error("STDERR: %s", e.stderr)
            raise

    snap_configs = get_snap_configs()
    run_bsnap_adhoc(snap_configs)

dag = jg_bloombergsnap_adhoc()