import os
import shutil
import tempfile
import logging
import pandas as pd
import argparse
import calendar
from datetime import datetime, date
from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from utils.compress_tools.recursive_uncompressor import RecursiveUncompressor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.date_utils import get_now

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

_columns = ["SECURITY", "TH_BAR_TIME", "TH_BAR_TYPE", "TH_BAR_OPEN", "TH_BAR_HIGH", "TH_BAR_LOW", "TH_BAR_CLOSE", "TH_BAR_VOLUME", "TH_BAR_VWAP", "TH_BAR_TICK_COUNT", "TH_BAR_OPEN_TIME", "TH_BAR_HIGH_TIME", "TH_BAR_LOW_TIME", "TH_BAR_CLOSE_TIME", "TH_BAR_SNAP", "TH_BAR_SNAP_TIME"]
# _core_data_columns = ["TH_BAR_OPEN", "TH_BAR_HIGH", "TH_BAR_LOW", "TH_BAR_CLOSE", "TH_BAR_SNAP"]
_timestamp_cols_dtypes = {"TH_BAR_OPEN_TIME": str, "TH_BAR_HIGH_TIME": str, "TH_BAR_LOW_TIME": str, "TH_BAR_CLOSE_TIME": str, "TH_BAR_SNAP_TIME": str}
_primary_key = ["SECURITY", "TH_BAR_TIME", "TH_BAR_TYPE"]
_bar_type = {"TRADE": "trd", "BID": "bid", "ASK": "ask"}

_dict_futures = {
    "BOND_FUT": {
        'TY': 3,
        'FV': 3,
        'TU': 3,
        'UXY': 3,
        'US': 3,
        'WN': 3,
        'FF': 36,
        'SER': 13,
        'SFR': 31
    },
    "EQ_FUT": {
        "ES": 4,
    },
    "COMM_FUT": {
        "CL": 3,
    }
}

_ongoing_lookback_bus_days = 2

def _get_future_tickers(start_date, end_date):
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )
    future_tickers = []
    for asset_class, root_dict in _dict_futures.items():
        for root, look_ahead_contracts in root_dict.items():
            start_date_str = start_date.strftime("%Y-%m-%d")
            end_date_str = end_date.strftime("%Y-%m-%d")
            
            query = f"""
            WITH CTE_RANKED_FUTURES AS (
            select BBG_FULL_TICKER, LAST_TRADEABLE_DT, FUT_FIRST_TRADE_DT,  RANK() OVER(PARTITION by ID_BB_GLOBAL_COMPANY ORDER BY LAST_TRADEABLE_DT) AS DEPTH  from BLOOMBERG.BBGH_FUTURES.VW_FUTURE_REF 
            where JG_FUT_CATEGORY = '{asset_class}' and FUTURE_ROOT = '{root}' AND LAST_TRADEABLE_DT >= '{start_date_str}' and FUT_FIRST_TRADE_DT <= '{end_date_str}'
            )
                select DISTINCT BBG_FULL_TICKER  from CTE_RANKED_FUTURES
                where DEPTH <= {look_ahead_contracts}
            """
            # print(query)
            df_securities = sf_adaptor.read_data(
                "BBGH_FUTURES",
                query,
            )
            future_tickers.extend(df_securities["BBG_FULL_TICKER"].tolist())
    
    sf_adaptor.close()
    return future_tickers


def _get_from_to_dates(month_year: str=None):
    if not month_year:
        end_date = get_now("UTC").replace(minute=0, second=0, microsecond=0)
        start_date = (pd.Timestamp(end_date) - pd.tseries.offsets.BDay(_ongoing_lookback_bus_days)).to_pydatetime()

        # The purpose of this to make sure our DL tick history usage doesnt span multiple months, save money.
        if start_date.month < end_date.month or start_date.year < end_date.year:
            start_date = datetime(end_date.year, end_date.month, 1, 0, 0)

        return start_date, end_date
    else:
        try:
            year, month = map(int, month_year.split("_"))
            start_date = datetime(year, month, 1, 0, 0)
            end_date = datetime(year, month, calendar.monthrange(year, month)[1], 23, 59, 59)
            if end_date > get_now("UTC"):
                end_date = get_now("UTC").replace(minute=0, second=0, microsecond=0)
            return start_date, end_date
        except ValueError:
            raise ValueError("Invalid month_year format. Use YYYY_MM.")

def get_inputs():
    parser = argparse.ArgumentParser(description="Bbg DL Tick History Runner")
    parser.add_argument("--bar_type", type=str, required=True, choices=_bar_type.keys(), help="TRADE, BID or ASK")
    parser.add_argument("--month_year", type=str, required=False, default=None, help="Month Year in format YYYY_MM")
    return parser.parse_args()


if __name__ == "__main__":
    args = get_inputs()
    (start, end) = _get_from_to_dates(args.month_year)
    securities =_get_future_tickers(start, end)
    datetime_range = f"{start.strftime('%Y%m%d_%H:%M:%S')}|{end.strftime('%Y%m%d_%H:%M:%S')}"
    
    per_sec_req_type = PerSecurityRequestType.gettickhistory
    batch_name = "dp_fth_{bar_type}".format(bar_type=_bar_type[args.bar_type])
    logger.info(f"Processing {batch_name}")
    
    request_dict = {
            "firm_name": "dl47544",
            "program_flag": "adhoc",
            "date_time_range": datetime_range,
            "sec_id": "TICKER",
            "content_type": "ohlc",
            "bar_interval": "1m",
            "event_type": args.bar_type,
            "securities": securities,
            "fields": [],
        }
    
    # print(request_dict)
    logger.info(f"Request dict: {request_dict}")
    
    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_1min_bars/"
    fut_th_file_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)
    
    # fut_th_file_path = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_1min_bars/responses/dp_fth_bid_2504071139.out.tar"
    logger.info(f"Filepath: {fut_th_file_path}")

    structure = {
        "*.out.tar": {"*.csv.gz": ["*"]}
    }

    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_FUTURES",
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER"
    )

    with tempfile.TemporaryDirectory() as temp_dir:
        root_folder = os.path.join(temp_dir, "root")
        if not os.path.exists(root_folder):
            os.makedirs(root_folder)
        shutil.copy(fut_th_file_path, root_folder)
        recursive_uncompressor = RecursiveUncompressor(root_folder, structure)
        for flat_file in recursive_uncompressor.run_generator(temp_dir):
            base_file_name=os.path.basename(flat_file)
            logger.info(f"Processing {base_file_name}")
            df_fut_1mb = pd.read_csv(flat_file, dtype=_timestamp_cols_dtypes)
            df_fut_1mb = df_fut_1mb[_columns]

            for ts_col in _timestamp_cols_dtypes.keys():
                df_fut_1mb[ts_col] = pd.to_datetime(df_fut_1mb[ts_col], errors='coerce')

            ret_val = adaptor.upsert(df_fut_1mb, "FUTURE_1M_BAR", _primary_key)
            logger.info(f"Upsert result for {base_file_name}: {ret_val}")
            
            
            
            