from confluent_kafka import Consumer, KafkaError
import json
import time
from datetime import datetime, timedelta
import os
import re
import yaml

print("Starting the script")
 
# Kafka consumer configuration
def read_config_secrets():
    config_secret_path = os.path.join('/jfs/tech1/apps/datait/jg-code/secure/prod', 'config_secret.json')
    with open(config_secret_path, 'r') as f:
        config_secret = json.load(f)
    return config_secret

def get_kafka_config():
    config_secret = read_config_secrets()
    bootstrap_server = config_secret['kafka_bootstrap_prod']
    group_id = "validation_1"
    security_protocol = config_secret['kafka_sec_protocol']
    kakfa_sasl_username = config_secret['kakfa_sasl_user']
    kafka_sasl_password = config_secret['kafka_sasl_pass']
    kafka_sasl_mechanism = config_secret['kafka_sasl_mech']
    return {
        'bootstrap.servers': bootstrap_server,
        'group.id': group_id,
        'auto.offset.reset': 'latest',
        'security.protocol': security_protocol,
        'sasl.username': kakfa_sasl_username,
        'sasl.password': kafka_sasl_password,
        'sasl.mechanisms': kafka_sasl_mechanism,
        "enable.ssl.certificate.verification": False,
    }

consumer_config = get_kafka_config()
 
# Path to Node Exporter textfile

exporter_file = "/etc/node_exporter/kafka_status.prom"
config_file = "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/conf/connectors_services_process.yaml"

# Function to parse topics from the config file
def parse_config():
    try:
        hosts_set = set()
        properties_set = set()
        property_host_map = {}

        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)  # Load YAML safely

        if not config_data or 'connectors' not in config_data:
            raise ValueError("Invalid configuration format: 'connectors' key missing")

        for connector in config_data['connectors']:
            # Extract producer server(s)
            servers = connector.get('server', '')
            if isinstance(servers, str):
                server_list = [s.strip() for s in servers.split(',') if s.strip()]  # Handle comma-separated servers
            elif isinstance(servers, list):
                server_list = [s.strip() for s in servers if s.strip()]  # Clean list
            else:
                server_list = []

            # Extract property location
            property_loc = connector.get('property_loc', '').strip()
            if property_loc:
                properties_set.add(property_loc)

                # Map property location to producer servers (inverse mapping)
                if property_loc not in property_host_map:
                    property_host_map[property_loc] = set()
                property_host_map[property_loc].update(server_list)

            hosts_set.update(server_list)  # Store all producer servers

        # Convert sets to lists in the final output
        property_host_map = {k: list(v) for k, v in property_host_map.items()}

        return list(hosts_set), list(properties_set), property_host_map

    except Exception as e:
        print(f"Error reading config file: {e}")
        return [], [], {}

def read_property_file(property_file):
    properties = {}
    property_file = property_file.replace("$HOME", "/opt/jsvc-datait")
    filename = os.path.basename(property_file)
    property_file = f"/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/snowpipe-streaming/config/{filename}"
    try:
        with open(property_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    properties[key.strip()] = value.strip() 
        properties["topic"] = properties.get("topics", "").split(',')
        properties["db_name"] = properties.get("snowflake.database.name", "")
        properties["schema"] = properties.get("snowflake.schema.name", "")
        properties["table"] = properties.get("snowflake.topic2table.map", "").split(':')[-1]
    except Exception as e:
        print(f"Error reading property file {property_file}: {e}")
    return properties

# Dictionary to store counts { (topic, host, minute) : count }
message_counts = {}

def get_minute_timestamps():
    now = datetime.now()
    lower_limit = now.replace(second=0, microsecond=0).strftime('%Y-%m-%d %H:%M:%S')
    upper_limit = (now.replace(second=0, microsecond=0) + timedelta(minutes=1) - timedelta(seconds=1)).strftime('%Y-%m-%d %H:%M:%S')
    return lower_limit, upper_limit

def process_message(msg):
    try:
        message_value = msg.value().decode('utf-8')
        message_json = json.loads(message_value)
        host = next((value for key, value in message_json.items() if "host" in key.lower()), "unknown_host")
        topic = msg.topic()
        key = (topic, host)
        message_counts[key] = message_counts.get(key, 0) + 1
        
        # print(f"Received message from topic '{topic}', host: '{host}', at {timestamp} : {message}")
        # print(message_counts)
    except json.JSONDecodeError:
        print(f"Error decoding JSON message: {msg.value()}")
    except Exception as e:
        print(f"Error processing message: {e}")

def get_latest_timestamp(topics):

    #To store the latest timestamp of message received for each topic
    latest_message_timestamp = {}
    latest_message_timestamp = {topic: "NA" for topic in topics}  # Default all topics to 'NA'
    if not os.path.exists(exporter_file):
        print("File not found. Setting all topics to 'NA'.")
        return latest_message_timestamp

    with open(exporter_file, "r") as file:
        for line in file:
            match = re.search(r'kafka_lag\{.*?topic="([^"]+)".*?latest_msg_timestamp="([^"]+)"', line)
            if match:
                topic, timestamp = match.groups()
                if topic in latest_message_timestamp:
                    latest_message_timestamp[topic] = timestamp  # Extracted timestamp

    return latest_message_timestamp

    
def write_to_node_exporter(topics, latest_message_timestamp, topic_producerServer_map):
    try:
        timestamp_start, timestamp_stop = get_minute_timestamps()  
        with open(exporter_file, "w") as f:
            print(f"Current Message Count: {message_counts}")
            print("Metrics:")

            topic_host_counts = {}

            # Organizing counts per (topic, host)
            for (topic, host), count in message_counts.items():
                if topic not in topic_host_counts:
                    topic_host_counts[topic] = {}
                topic_host_counts[topic][host] = count

            # Processing each topic for lag calculation
            for topic in topics:
                hosts = topic_host_counts.get(topic, {})

                hostname1 = next(iter(hosts.keys()), "NA")

                hostname1 = hostname1 if any(server in hostname1 for server in topic_producerServer_map.get(topic, [])) else f"Unknown Host 1 : {hostname1}"
                count1 = hosts.get(hostname1, "NA")

                hostname2 = next(iter(set(hosts.keys()) - {hostname1}), "NA")
                hostname2 = hostname2 if any(server in hostname2 for server in topic_producerServer_map.get(topic, [])) else f"Unknown Host 2 : {hostname2}"
                count2 = hosts.get(hostname2, "NA")

                # Convert to int if available, else keep as 0
                count1 = int(count1) if count1 != "NA" else 0
                count2 = int(count2) if count2 != "NA" else 0

                # Compute lag difference
                
                difference = abs(count1 - count2)
                if count1 != count2:
                    lagging_hostname = hostname1 if count1 < count2 else hostname2
                else:
                    lagging_hostname = "None"
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M')
                latest_message_timestamp[topic] = timestamp

                # Writing metric
                metric_line = (
                    f'kafka_lag{{topic="{topic}", timestamp_start="{timestamp_start}", '
                    f'timestamp_stop="{timestamp_stop}", hostname1="{hostname1}", count1="{count1}", '
                    f'hostname2="{hostname2}", count2="{count2}", '
                    f'lagging_hostname="{lagging_hostname}", latest_msg_timestamp="{latest_message_timestamp[topic]}"}} {difference}\n'
                )
                print(metric_line)
                f.write(metric_line)

    except Exception as e:
        print(f"Error writing metrics: {e}")


hosts, properties, property_host_map = parse_config()

topics = []
topic_producerServer_map = {}

modified_properties = []
for property_file in properties:
    result = read_property_file(property_file)
    if result:
        if 'topic' in result:
            topic_list = result['topic'] if isinstance(result['topic'], list) else [result['topic']]
            print(f"The topic listtttt : {topic_list}")
            topics.extend(topic_list)

            for topic in topic_list:
                formatted_servers = [server.replace('.', '-') for server in property_host_map.get(property_file, [])]
                topic_producerServer_map[topic] = formatted_servers

        modified_properties.append(result)  

print(f"Parsed topics: {topics}")

# Initialize Kafka Consumer
try:
    print("Initializing Kafka consumer...")
    consumer = Consumer(consumer_config)
    consumer.subscribe(topics)
except Exception as e:
    print(f"Error initializing Kafka consumer: {e}")
    exit(1)

print("Starting to consume messages from Kafka...")

try:
    last_flush_time = time.time()
    while True:
        msg = consumer.poll(1.0)
        if msg is None:
            # print("No message is being received")
            pass
        if msg:
            if msg.error():
                print(f"Kafka error: {msg.error()}")
                continue
            process_message(msg)
        
        if time.time() - last_flush_time >= 60:
            latest_message_timestamp = get_latest_timestamp(topics)
            if message_counts:
                #print("Few messages were consumed in the last minute")
                pass
            else:
                print("No messages were consumed in the last minute")
            write_to_node_exporter(topics, latest_message_timestamp, topic_producerServer_map)
            message_counts.clear()
            last_flush_time = time.time()
except KeyboardInterrupt:
    print("Stopping consumer...")
finally:
    consumer.close()
    print("Kafka consumer closed.")