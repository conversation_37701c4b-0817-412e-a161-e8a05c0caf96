from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
import os, sys, pendulum, subprocess, logging, pytz
from airflow.timetables.trigger import CronTriggerTimetable

# Setup environment
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()

# Environment paths
JGDATA_PATH = os.environ.get("JGDATA_PATH")
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)

# Default DAG arguments
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.yesterday(tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

# Python callable to run SS&C PnL process
def run_ssnc_pnl():

    command = f"python3 {DATA_PIPELINE_PATH}/ssnc/process_pnl.py"

    try:
        logging.info(f"Running command: {command}")
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

# Define the DAG
with DAG(
    dag_id="jg-etl-ssnc-pnl-load",
    description="This DAG runs ETL for SS&C PnL data files",
    default_args=default_args,
    schedule=CronTriggerTimetable('*/10 5-12 * * 1-5', timezone="America/New_York"),
    max_active_runs = 1,
    catchup=False,
    tags=["jgdata", "ssnc"]
) as dag:

    PythonOperator(
        task_id=f"run_ssnc_pnl",
        python_callable=run_ssnc_pnl,
        dag=dag
    )