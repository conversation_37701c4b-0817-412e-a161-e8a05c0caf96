import requests
import csv,os,json
import argparse
import pandas as pd
from requests.auth import <PERSON><PERSON><PERSON><PERSON><PERSON>c<PERSON><PERSON>
from strunner import *

setupEnvironment() 
from stcommon.infra.rds.snowflake_bulk_loader import SnowflakeBulkLoader
from stcommon.infra.python.fileio import read_toml


configPath = os.environ.get('CONFIG_PATH', os.getcwd())
with open(f'{configPath}/config.json', 'r') as f:
    config = json.load(f)

def jg_data_path():
    return config["JG_DATA_PATH"]

def jg_config_path():
    return config["JG_CONFIG_PATH"]

def read_config_secrets():
    config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
    with open(config_secret_path, 'r') as f:
        config_secret = json.load(f)
    return config_secret

config_secret=read_config_secrets()
path = os.path.join(jg_data_path(), 'usda_agri')


username= config_secret["snp_agri_username"]
password=config_secret["snp_agri_pasword"]
raw_db=config_secret["usda_raw_db"]
commod_schema=config_secret["usda_commod_schema"]

project_root_temp= os.environ.get('JGDATA_PATH')
TOML_CONFIG_FILE = project_root_temp + "/conf/sources/agri_config.toml"

toml_config = read_toml(TOML_CONFIG_FILE)

snp_urls=toml_config.get("snp_urls")

auth=HTTPBasicAuth(username,password)
conn = SnowflakeBulkLoader(warehouse="DATA_PLATFORM_WH",database=raw_db,schema=f'{raw_db}.{commod_schema}',role="FR_DATA_PLATFORM")
cur = conn.cursor

parser = argparse.ArgumentParser(description="Process previous date")
parser.add_argument("--date", required=True, help="Specify the processing date in format YYYYMMDD")
args = parser.parse_args()
current_date = args.date

for url in snp_urls:
    file_name=url.split("_Agriculture_API_Data")[0].split("/")[-1] #US or Global
    table_name=f'SNP_{file_name}_DATA_RAW' # SNP_US_DATA_RAW or SNP_Global_DATA_RAW
    query=f" Select max(last_update ) from {raw_db}.{commod_schema}.{table_name}"
    max_date=cur.execute(query).fetchone()[0]

    all_data=[]
    response=requests.get(url,auth=auth)
    data=response.json()
    df=pd.DataFrame(data["value"])
    df1=df[pd.to_datetime(df["last_update"])>pd.Timestamp(max_date).tz_localize("UTC")+pd.Timedelta(minutes=5)]


    while not df1.empty:
        print(df1.empty)
        df=pd.DataFrame(data["value"])
        df1=df[pd.to_datetime(df["last_update"])>pd.Timestamp(max_date).tz_localize("UTC")+pd.Timedelta(minutes=5)]
        all_data.extend(df1.to_dict(orient='records'))
        if data.get("@odata.nextLink"):
            url=data["@odata.nextLink"]
            print(url)
            response=requests.get(url,auth=auth)
            data=response.json()
        else: break

    if all_data:

        with open(f'{path}/snp_data_{file_name}_{current_date}.csv', 'w', newline='', encoding='utf-8') as output_file:
            dict_writer = csv.DictWriter(output_file, fieldnames=all_data[0].keys())
            dict_writer.writeheader()
            dict_writer.writerows(all_data)

        print(f"Data saved to {path}/snp_data_{file_name}_{current_date}.csv")