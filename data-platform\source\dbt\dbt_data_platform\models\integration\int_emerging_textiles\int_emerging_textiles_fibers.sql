{{
   config(
       tags=['emerging_textiles_fibers_yarns'],
       materialized='table',
   )
}}


WITH parsed AS (
 SELECT
   *,
   CASE
     WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
     THEN TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD')
     ELSE NULL
   END AS file_dt,
   CASE WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
        THEN EXTRACT(year  FROM TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD'))
   END AS file_year,
   CASE WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
        THEN EXTRACT(month FROM TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD'))
   END AS file_month,
   CASE WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
        THEN EXTRACT(day   FROM TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD'))
   END AS file_day
 FROM {{ ref("stg_emerging_textiles_fibers") }}
),
dedup AS (
 SELECT
   parsed.*,
   CASE
     WHEN file_dt IS NULL
     THEN 1
     ELSE ROW_NUMBER() OVER (
            PARTITION BY file_year, file_month
            ORDER BY file_day DESC
          )
   END AS rn
 FROM parsed
)
SELECT
"Date"                                             as "date",
"Cotton_New_York_Nearby_SLM_US_Cents_Lb"           as "cotton_new_york_nearby_slm_us_cents_lb",
"Cotton_A_Index_Far_East_US_Cents_Lb"              as "cotton_a_index_far_east_us_cents_lb",
"Cotton_China_domestic_3128B_Yuan_MT"              as "cotton_china_domestic_3128b_yuan_mt",
"Cotton_China_domestic_2227B_Yuan_MT"              as "cotton_china_domestic_2227b_yuan_mt",
"Cotton_China_domestic_2129B_Yuan_MT"              as "cotton_china_domestic_2129b_yuan_mt",
"Cotton_China_domestic_3128B_US_Cents_Lb"          as "cotton_china_domestic_3128b_us_cents_lb",
"Cotton_China_domestic_2227B_US_Cents_Lb"          as "cotton_china_domestic_2227b_us_cents_lb",
"Cotton_China_domestic_2129B_US_Cents_Lb"          as "cotton_china_domestic_2129b_us_cents_lb",
"Cotton_China_domestic_3128B_US_S_Kilo"            as "cotton_china_domestic_3128b_us_s_kilo",
"Cotton_China_domestic_2227B_US_S_Kilo"            as "cotton_china_domestic_2227b_us_s_kilo",
"Cotton_China_domestic_2129B_US_S_Kilo"            as "cotton_china_domestic_2129b_us_s_kilo",
"Cotton_China_Import_FC_Index_S_US_Cents_Lb"       as "cotton_china_import_fc_index_s_us_cents_lb",
"Cotton_China_Import_FC_Index_M_US_Cents_Lb"       as "cotton_china_import_fc_index_m_us_cents_lb",
"Cotton_China_Import_FC_Index_L_US_Cents_Lb"       as "cotton_china_import_fc_index_l_us_cents_lb",
"Cotton_India_Shankar-6_Guj_Rps_Candy"             as "cotton_india_shankar-6_guj_rps_candy",
"Cotton_India_Shankar-6_Guj_USS_per_Kilo"          as "cotton_india_shankar-6_guj_uss_per_kilo",
"Cotton_India_Shankar-6_Guj_US_Cents_Lb"           as "cotton_india_shankar-6_guj_us_cents_lb",
"Cotton_India_Shankar-6_Guj_Rps_Kilo"              as "cotton_india_shankar-6_guj_rps_kilo",
"Cotton_Pakistan_KCA_Rps_Maund"                    as "cotton_pakistan_kca_rps_maund",
"Cotton_Pakistan_KCA_USS_per_Kilo"                 as "cotton_pakistan_kca_uss_per_kilo",
"Cotton_Pakistan_KCA_US_Cents_Lb"                  as "cotton_pakistan_kca_us_cents_lb",
"Cotton_Brazil_CEPEA_ESALQ_Real_Lb"                as "cotton_brazil_cepea_esalq_real_lb",
"Cotton_Brazil_CEPEA_ESALQ_US_Cents_Lb"            as "cotton_brazil_cepea_esalq_us_cents_lb",
"Cotton_Brazil_CEPEA_ESALQ_USS_Kilo"               as "cotton_brazil_cepea_esalq_uss_kilo",
"Cotton_Turkey_41_Colour_Grade_TRY_Kg"             as "cotton_turkey_41_colour_grade_try_kg",
"Cotton_Turkey_41_Colour_Grade_US_Cents_Lb"        as "cotton_turkey_41_colour_grade_us_cents_lb",
"Crude_Oil_Spot_Brent"                             as "crude_oil_spot_brent",
"Crude_Oil_New_York_Nearby_USS_Barrel"             as "crude_oil_new_york_nearby_uss_barrel",
"PX_Spot_FOB_Korea_USS_MT"                         as "px_spot_fob_korea_uss_mt",
"PTA_Spot_CIF_China_USS_MT"                        as "pta_spot_cif_china_uss_mt",
"MEG_Spot_CIF_China_USS_MT"                        as "meg_spot_cif_china_uss_mt",
"PTA_Spot_China_Yuan_MT"                           as "pta_spot_china_yuan_mt",
"MEG_Spot_China_Yuan_MT"                           as "meg_spot_china_yuan_mt",
"Polyester_Chip_China_Yuan_MT"                     as "polyester_chip_china_yuan_mt",
"Polyester_Chip_China_USS_Kilo"                    as "polyester_chip_china_uss_kilo",
"Polyester_China_PSF_1.4D-38mm_Yuan_MT"            as "polyester_china_psf_1.4d-38mm_yuan_mt",
"Polyester_China_PSF_1.4D-38mm_USS_Kilo"           as "polyester_china_psf_1.4d-38mm_uss_kilo",
"Polyester_China_PSF_1.4D-38mm_US_Cents_Lb"        as "polyester_china_psf_1.4d-38mm_us_cents_lb",
"PTA_India_CNF_USS_MT"                             as "pta_india_cnf_uss_mt",
"MEG_India_CNF_USS_MT"                             as "meg_india_cnf_uss_mt",
"PTA_India_Domestic_Rps_Kilo"                      as "pta_india_domestic_rps_kilo",
"MEG_India_Domestic_Rps_Kilo"                      as "meg_india_domestic_rps_kilo",
"Polyester_India_PSF_1.4D-38mm_Rupees_kilo"        as "polyester_india_psf_1.4d-38mm_rupees_kilo",
"Polyester_India_PSF_1.4D-38mm_USS_Kilo"           as "polyester_india_psf_1.4d-38mm_uss_kilo",
"Polyester_India_PSF_1.4D-38mm_US_Cents_Lb"        as "polyester_india_psf_1.4d-38mm_us_cents_lb",
"Polyester_Pakistan_PSF_1.4D-38mm_Rupees_kilo"     as "polyester_pakistan_psf_1.4d-38mm_rupees_kilo",
"Polyester_Pakistan_PSF_1.4D-38mm_USS_Kilo"        as "polyester_pakistan_psf_1.4d-38mm_uss_kilo",
"Polyester_Pakistan_PSF_1.4D-38mm_US_Cents_Lb"     as "polyester_pakistan_psf_1.4d-38mm_us_cents_lb",
"POY_China_Index_150D_48F_Yuan_MT"                 as "poy_china_index_150d_48f_yuan_mt",
"DTY_China_Index_150D_48F_Yuan_MT"                 as "dty_china_index_150d_48f_yuan_mt",
"FDY_China_Index_68D_24F_Yuan_MT"                  as "fdy_china_index_68d_24f_yuan_mt",
"POY_China_Index_150D_48F_USS_Kg"                  as "poy_china_index_150d_48f_uss_kg",
"DTY_China_Index_150D_48F_USS_Kg"                  as "dty_china_index_150d_48f_uss_kg",
"FDY_China_Index_68D_24F_USS_Kg"                   as "fdy_china_index_68d_24f_uss_kg",
"POY_Qianqing_-_China_150D_96f_Yuan_MT"            as "poy_qianqing_-_china_150d_96f_yuan_mt",
"DTY_Qianqing_-_China_150D_96f_Yuan_MT"            as "dty_qianqing_-_china_150d_96f_yuan_mt",
"FDY_Qianqing_-_China_150D_48f_Yuan_MT"            as "fdy_qianqing_-_china_150d_48f_yuan_mt",
"POY_Qianqing_-_China_150D_96f_USS_Kilo"           as "poy_qianqing_-_china_150d_96f_uss_kilo",
"DTY_Qianqing_-_China_150D_96f_USS_Kilo"           as "dty_qianqing_-_china_150d_96f_uss_kilo",
"FDY_Qianqing_-_China_150D_48f_USS_Kilo"           as "fdy_qianqing_-_china_150d_48f_uss_kilo",
"POY_Qianqing_-_China_150D_96f_US_Cents_Lb"        as "poy_qianqing_-_china_150d_96f_us_cents_lb",
"DTY_Qianqing_-_China_150D_96f_US_Cents_Lb"        as "dty_qianqing_-_china_150d_96f_us_cents_lb",
"FDY_Qianqing_-_China_150D_48f_US_Cents_Lb"        as "fdy_qianqing_-_china_150d_48f_us_cents_lb",
"POY_India_126D_34f_Rps_Kilo"                      as "poy_india_126d_34f_rps_kilo",
"POY_India_150D_34f_Rps_Kilo"                      as "poy_india_150d_34f_rps_kilo",
"POY_India_250D_48f_Rps_Kilo"                      as "poy_india_250d_48f_rps_kilo",
"DTY_India_75D_34f_Rps_Kilo"                       as "dty_india_75d_34f_rps_kilo",
"DTY_India_100D_34f_Rps_Kilo"                      as "dty_india_100d_34f_rps_kilo",
"DTY_India_150D_48f_Rps_Kilo"                      as "dty_india_150d_48f_rps_kilo",
"FDY_India_50D_24f_Rps_Kilo"                       as "fdy_india_50d_24f_rps_kilo",
"FDY_India_75D_34f_Rps_Kilo"                       as "fdy_india_75d_34f_rps_kilo",
"POY_Pakistan_150D_48f_Rps_Kilo"                   as "poy_pakistan_150d_48f_rps_kilo",
"DTY_Pakistan_150D_48f_Rps_Kilo"                   as "dty_pakistan_150d_48f_rps_kilo",
"FDY_Pakistan_50D_24f_Rps_Kilo"                    as "fdy_pakistan_50d_24f_rps_kilo",
"Viscose_China_VSF_1.5D-38mm_Yuan_MT"              as "viscose_china_vsf_1.5d-38mm_yuan_mt",
"Viscose_China_VSF_1.5D-38mm_USS_kilo"             as "viscose_china_vsf_1.5d-38mm_uss_kilo",
"Viscose_China_VSF_1.5D-38mm_US_Cents_Lb"          as "viscose_china_vsf_1.5d-38mm_us_cents_lb",
"Wood_Pulp_China_Domestic_Yuan_MT"                 as "wood_pulp_china_domestic_yuan_mt",
"Wood_Pulp_China_Import_USS_MT"                    as "wood_pulp_china_import_uss_mt",
"Viscose_Filament_China_133.3dtex_Yuan_MT"         as "viscose_filament_china_133.3dtex_yuan_mt",
"Viscose_Filament_China_133.3dtex_USS_Kilo"        as "viscose_filament_china_133.3dtex_uss_kilo",
"Viscose_Filament_China_133.3dtex_1_Year_=_100"    as "viscose_filament_china_133.3dtex_1_year_=_100",
"Rayon___Viscose_1.5D_VSF_India_Rupees_Kg"         as "rayon___viscose_1.5d_vsf_india_rupees_kg",
"Rayon___Viscose_1.5D_VSF_India_USS_Kg"            as "rayon___viscose_1.5d_vsf_india_uss_kg",
"Rayon___Viscose_1.5D_VSF_India_US_Cents_lb"       as "rayon___viscose_1.5d_vsf_india_us_cents_lb",
"VSF_1.4D_India_Rps_Kilo"                          as "vsf_1.4d_india_rps_kilo",
"Rayon___Viscose_1.5D_VSF_Pakistan_Rupees_Kg"      as "rayon___viscose_1.5d_vsf_pakistan_rupees_kg",
"Rayon___Viscose_1.5D_VSF_Pakistan_USS_Kg"         as "rayon___viscose_1.5d_vsf_pakistan_uss_kg",
"Rayon___Viscose_1.5D_VSF_Pakistan_US_Cents_lb"    as "rayon___viscose_1.5d_vsf_pakistan_us_cents_lb",
"Benzene_China_Domestic_Yuan_MT"                   as "benzene_china_domestic_yuan_mt",
"Benzene_FOB_Korea_International_USS_MT"           as "benzene_fob_korea_international_uss_mt",
"Caprolactam_CFR_Far_East_International_USS_MT"    as "caprolactam_cfr_far_east_international_uss_mt",
"Caprolactam_China_(CPL)_Yuan_MT"                  as "caprolactam_china_(cpl)_yuan_mt",
"Caprolactam_China_(CPL)_USS_kilo"                 as "caprolactam_china_(cpl)_uss_kilo",
"Nylon_Chips_China_Yuan_MT"                        as "nylon_chips_china_yuan_mt",
"Nylon_6_POY_(86D_24f)_China_Yuan_MT"              as "nylon_6_poy_(86d_24f)_china_yuan_mt",
"Nylon_6_FDY_(70D_24f)_China_Yuan_MT"              as "nylon_6_fdy_(70d_24f)_china_yuan_mt",
"Nylon_6_China_DTY_70D_24f_Yuan_MT"                as "nylon_6_china_dty_70d_24f_yuan_mt",
"Nylon_6_China_DTY_70D_24f_USS_Kilo"               as "nylon_6_china_dty_70d_24f_uss_kilo",
"Nylon_6_China_DTY_70D_24f_US_Cents_Lb"            as "nylon_6_china_dty_70d_24f_us_cents_lb",
"BDO_China_Ex-Works_Yuan_MT"                       as "bdo_china_ex-works_yuan_mt",
"MDI_China_Ex-Works_Yuan_MT"                       as "mdi_china_ex-works_yuan_mt",
"PTMEG_China_ex-works_Yuan_MT"                     as "ptmeg_china_ex-works_yuan_mt",
"Spandex_20D_China_Ex-Works_Yuan__MT"              as "spandex_20d_china_ex-works_yuan__mt",
"Spandex_30D_China_Ex-Works_Yuan__MT"              as "spandex_30d_china_ex-works_yuan__mt",
"Spandex_40D_China_Ex-Workd_Yuan_MT"               as "spandex_40d_china_ex-workd_yuan_mt",
"Spandex_70D_China_Ex-Works_Yuan__MT"              as "spandex_70d_china_ex-works_yuan__mt",
"BDO_China_Ex-Works_USS_Kilo"                      as "bdo_china_ex-works_uss_kilo",
"MDI_China_Ex-Works_USS_Kilo"                      as "mdi_china_ex-works_uss_kilo",
"PTMEG_China_Ex-Works_USS_kilo"                    as "ptmeg_china_ex-works_uss_kilo",
"Spandex_20D_China_Ex-Works_USS_Kilo"              as "spandex_20d_china_ex-works_uss_kilo",
"Spandex_30D_China_Ex-Works_USS_Kilo"              as "spandex_30d_china_ex-works_uss_kilo",
"Spandex_40D_China_Ex-Works_USS_Kilo"              as "spandex_40d_china_ex-works_uss_kilo",
"Spandex_70D_China_Ex-Works_USS_Kilo"              as "spandex_70d_china_ex-works_uss_kilo",
"Spandex_China_40D_bright_US_Cents_Lb"             as "spandex_china_40d_bright_us_cents_lb",
"Acrylic_China_ASF_1.5D-38mm_Yuan_MT"              as "acrylic_china_asf_1.5d-38mm_yuan_mt",
"Acrylic_China_ASF_1.5D-38mm_USS_Kilo"             as "acrylic_china_asf_1.5d-38mm_uss_kilo",
"Acrylic_China_ASF_1.5D-38mm_US_Cents_Lb"          as "acrylic_china_asf_1.5d-38mm_us_cents_lb",
"Acrylonitrile_China_(ACN)_Yuan_MT"                as "acrylonitrile_china_(acn)_yuan_mt",
"Acrylonitrile_China_(ACN)_USS_kilo"               as "acrylonitrile_china_(acn)_uss_kilo",
"Wool_Australia_19_Micron_AS_Cents_Kilo"           as "wool_australia_19_micron_as_cents_kilo",
"Wool_Australia_19_Micron_US_Cents_Kilo"           as "wool_australia_19_micron_us_cents_kilo",
"Wool_Australia_19_Micron_US_Cents_Lb"             as "wool_australia_19_micron_us_cents_lb",
"Dried_Cocoons_China_Domestic_Spot_Yuan_MT"        as "dried_cocoons_china_domestic_spot_yuan_mt",
"Dried_Cocoons_China_Domestic_Spot_USS_Kilo"       as "dried_cocoons_china_domestic_spot_uss_kilo",
"Raw_Silk_China_Domestic_Spot_Yuan_MT"             as "raw_silk_china_domestic_spot_yuan_mt",
"Raw_Silk_China_Domestic_Spot_USS_Kilo"            as "raw_silk_china_domestic_spot_uss_kilo",
"Raw_Silk_China_Domestic_Spot_US_Cents_Lb"         as "raw_silk_china_domestic_spot_us_cents_lb",
to_date(file_year || '-' || file_month || '-' || file_day) AS fibers_extraction_date
FROM dedup
WHERE rn = 1