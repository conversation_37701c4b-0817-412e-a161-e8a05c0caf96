import sys
from airflow import DAG
from airflow.providers.ssh.operators.ssh import SSHOperator
from airflow.providers.ssh.hooks.ssh import SSHHook
from datetime import datetime, timedelta
from airflow.timetables.trigger import CronTriggerTimetable
import os
import pendulum
from util.jg_ssh_dbt_operator import JainGlobalSSHDBTOperator
from airflow.hooks.base import BaseHook
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
import logging
import subprocess
from strunner import *
from airflow.operators.python import PythonOperator

setupEnvironment()

dag_id = os.path.basename(__file__).replace(".py", "")
ny_time = pendulum.now("America/New_York")
CURRENT_DATE = ny_time.strftime("%Y%m%d")

def get_jg_env():
    env_value = os.getenv('jg_env', 'prod').lower()
    if env_value not in ['prod', 'uat']:
        raise ValueError(f"Invalid jg_env value: {env_value}. Allowed values are 'prod' or 'uat'.")
    return env_value

env = get_jg_env()

source='spacinsider'
jsvc_path = '/opt/jsvc-datait'
source_path = f'{jsvc_path}/prod/JG-DATA-PLATFORM/source'
python_file_path = f'{source_path}/bin'
command_pipeline_rawdata = f'--dataset {source} --date {CURRENT_DATE}'
command_copy = f'--dataset {source} --date {CURRENT_DATE} --keypair'

conn = BaseHook.get_connection("dbt_conn_id")
key_file = conn.extra_dejson.get("path")

def download_spacinsider_data(**context):
        jgdata = os.environ.get('JGDATA_PATH')
        script = os.path.join(jgdata, 'bin', 'spacinsider_data_download.py')
        output_path = f'/jfs/tech1/apps/rawdata/{source}/'

        cmd = [
            'python3', script,
            '--date', CURRENT_DATE,
            '--output_path', output_path
        ]
        cmd_str = ' '.join(cmd)
        logging.info(f'Executing command: {cmd_str}')

        try:
            result = subprocess.run(
                cmd_str,
                shell=True,
                check=True,
                capture_output=True,
                text=True
            )
            logging.info("---LOGS---")
            logging.info("Command output: %s", result.stdout)
            logging.info("Command error output: %s", result.stderr)
        except subprocess.CalledProcessError as e:
            logging.error("Command failed with exit status %d", e.returncode)
            logging.error("STDOUT: %s", e.stdout)
            logging.error("STDERR: %s", e.stderr)
            raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.yesterday(tz="America/New_York"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

doc_md_DAG=f'Create dbt run command for {source}'
with DAG(
    dag_id=dag_id,
    default_args=default_args,
    description=f'This DAG runs ETL in DBT for {source}',
    schedule=CronTriggerTimetable('30 15 * * *',timezone="America/New_York"),
    tags=["phdata", source, "dbt"],
    catchup=False,
    doc_md=doc_md_DAG
) as dag:


    ssh_hook = SSHHook(
        remote_host='*************',
        username='jsvc-datait',
        key_file=key_file,
        port=22
    )

    download_spacinsider_data_task = PythonOperator(
        task_id="download_spacinsider_data",
        python_callable=download_spacinsider_data
    )

    run_spacinsider_rawdata_command = SSHOperator(
        task_id='run_raw_data_command',
        ssh_hook=ssh_hook,
        command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_rawdata.py {command_pipeline_rawdata}',
        do_xcom_push=True,
        conn_timeout=None, # Set to None to disable timeout,
        cmd_timeout=None, # Set to None to disable timeout
    )


    run_spacinsider_sf_copy_command = SSHOperator(
        task_id='run_pipeline_sf_copy_command',
        ssh_hook=ssh_hook,
        command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_sf_copy.py {command_copy}',
        do_xcom_push=True,
        conn_timeout=None, # Set to None to disable timeout,
        cmd_timeout=None, # Set to None to disable timeout
    )

    run_dbt_command = JainGlobalSSHDBTOperator(
        task_id='run_dbt_command',
        ssh_hook=ssh_hook,
        base_command = 'run',
        profile = 'dbt_data_platform',
        select_args = [f"tag:{source}"],
        target = env,
        env = env,
        timeout=None # Set to None to disable timeout
    )

    download_spacinsider_data_task >> run_spacinsider_rawdata_command >> run_spacinsider_sf_copy_command >> run_dbt_command