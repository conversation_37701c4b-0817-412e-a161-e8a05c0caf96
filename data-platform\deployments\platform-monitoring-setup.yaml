apiVersion: apps/v1
kind: Deployment
metadata:
  name: monitoring-platform
  namespace: default
  labels:
    app: monitoring-platform
spec:
  replicas: 1
  selector:
    matchLabels:
      app: monitoring-platform
  template:
    metadata:
      labels:
        app: monitoring-platform
    spec:
      containers:
        - name: monitoring-platform
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-monitoring-platform-ubuntu1-${ENV}:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 9100
              name: http-metrics
              protocol: TCP
            - containerPort: 22
              name: ssh
              protocol: TCP
          securityContext:
            capabilities:
              add: ["SYS_ADMIN"]
            privileged: true
            runAsUser: 0
          volumeMounts:
            - mountPath: /jfs
              name: jfs-mount
            - mountPath: /config
              name: config-volume
            - mountPath: /etc/node_exporter
              name: node-exporter-metrics
            - name: ssh-key-volume
              mountPath: /tmp/ssh-key
              readOnly: true
          command:
            - /bin/bash
            - -c
            - |
              echo "Starting Streaming Monitoring Deployment..."

              /etc/cwiqfs/run-pod-with-mount.sh

              timeout=60
              elapsed=0
              while [ ! -d "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/monitoring" ]; do
                  echo "Waiting for JFS mount..."
                  sleep 2
                  elapsed=$((elapsed + 2))
                  if [ $elapsed -ge $timeout ]; then
                      echo "Error: JFS did not mount within $timeout seconds."
                      exit 1
                  fi
              done
              echo "JFS Mounted successfully!"

              CONFIG_FILE="/config/config.yaml"
              if [ ! -f "$CONFIG_FILE" ]; then
                echo "Config file not found!"
                exit 1
              fi

              # Ensure SSH directory exists
              mkdir -p /root/.ssh
              chmod 700 /root/.ssh

              # Copy private key from secret mount path to .ssh directory
              cp /tmp/ssh-key/id_rsa /root/.ssh/id_rsa
              chmod 600 /root/.ssh/id_rsa

              echo "Adding servers to known_hosts..."
              server_count=$(yq eval '.server_ip | length' "$CONFIG_FILE")
              for ((i = 0; i < server_count; i++)); do
                  server_ip=$(yq eval ".server_ip[$i]" "$CONFIG_FILE")
                  server_ip=${server_ip#*@}
                  if ssh-keyscan -H "$server_ip" >> ~/.ssh/known_hosts 2>/dev/null; then
                      echo "✔ Added $server_ip to known_hosts"
                  else
                      echo "❌ Failed to add $server_ip to known_hosts"
                  fi
              done
              chmod 600 ~/.ssh/known_hosts

              echo "Checking connectivity to all servers..."
              for ((i = 0; i < server_count; i++)); do
                  server_ip=$(yq eval ".server_ip[$i]" "$CONFIG_FILE")
                  echo "🔄 Testing SSH access to $server_ip..."
                  ssh -i ~/.ssh/id_rsa \
                      -o StrictHostKeyChecking=no \
                      -o UserKnownHostsFile=/dev/null \
                      -o ConnectTimeout=5 \
                      "$server_ip" "echo '✅ Connection Successful to $server_ip'" \
                      && echo "✔ Success: $server_ip" \
                      || echo "❌ Failed: $server_ip"
              done

              echo "Executing monitoring commands..."
              yq eval '.commands[]' "$CONFIG_FILE" | while IFS= read -r cmd; do
                  echo "Running command: $cmd"
                  
                  word_count=$(echo "$cmd" | wc -w)
                  if [ "$word_count" -ge 2 ]; then
                      script_path=$(echo "$cmd" | awk '{print $2}')
                  else
                      script_path=$(echo "$cmd" | awk '{print $1}')
                  fi

                  echo "Checking file existence: $script_path"
                  
                  retries=10
                  while [ ! -f "$script_path" ] && [ $retries -gt 0 ]; do
                      echo "Waiting for script $script_path to become available..."
                      sleep 2
                      retries=$((retries - 1))
                  done

                  if [ -f "$script_path" ]; then
                      echo "✅ Found script: $script_path"
                      chmod +x "$script_path"
                      
                      log_name="$(basename "$cmd")"
                      log_name="${log_name%.*}.log"
                      log_folder="/var/tmp"
                      log_file="$log_folder/$log_name"
                      
                      mkdir -p "$log_folder"
                      echo "Executing command: bash -c \"$cmd\" > \"$log_file\" 2>&1 &"
                      
                      bash -c "$cmd" > "$log_file" 2>&1 &
                      
                      if [ $? -ne 0 ]; then
                          echo "❌ Error executing command: $cmd"
                          continue  # Skip to the next command if there was an error
                      fi
                  else
                      echo "❌ Error: $script_path not found after waiting."
                      continue  # Skip to the next command if script is not found
                  fi
              done

              /usr/local/bin/node_exporter --collector.textfile.directory=/etc/node_exporter/ &

              exec tail -f /dev/null
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 4800
      volumes:
        - name: jfs-mount
          emptyDir: {}
        - name: config-volume
          configMap:
            name: platform-process-monitoring-config
        - name: node-exporter-metrics
          emptyDir: {}
        - name: ssh-key-volume
          secret:
            secretName: ssh-key-secret
            defaultMode: 0600

---
apiVersion: v1
kind: Service
metadata:
  name: platform-monitoring-service
  namespace: default
spec:
  selector:
    app: monitoring-platform
  ports:
    - protocol: TCP
      port: 9100
      targetPort: 9100
  type: LoadBalancer
