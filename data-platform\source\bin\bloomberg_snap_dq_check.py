import numpy as np
import pandas as pd
from datetime import date
from loguru import logger as log
from datetime import datetime
import pytz

from strunner import *
setupEnvironment()

from stcommon.infra.rds.snowflake_operation import *

try:
    obj_sf = SnowflakeDML("BLOOMBERG")
    sf_query=f"""WITH base AS (
     SELECT availdate::DATE dt, DATE_PART('hh',availdate) AS interval_start, COUNT(*) cnt
     FROM   BLOOMBERG.SNAP.DATA_FEED
     WHERE  availdate::DATE BETWEEN DATEADD(day,-7,CURRENT_DATE) and DATEADD(day,-1,CURRENT_DATE)
     GROUP BY 1,2
),
moving_avg as (
     SELECT interval_start, AVG(cnt)::INTEGER moving_avg
     FROM   base
     GROUP BY 1
),
current_day_hours AS (
     SELECT 0 hh
     UNION ALL
     SELECT hh+1 from current_day_hours
     WHERE  hh< DATE_PART('hh',CURRENT_TIMESTAMP)
),
today_counts AS (
     SELECT hh AS interval_start, COUNT(*) cnt
     FROM   current_day_hours c
     LEFT   JOIN BLOOMBERG.SNAP.DATA_FEED df
     ON     c.hh = DATE_PART('hh',availdate)
     AND    availdate::DATE = CURRENT_DATE
     GROUP BY 1
)
-- Final join and percent diff
SELECT c.interval_start, c.cnt todays_count, m.moving_avg, ROUND(CASE WHEN m.moving_avg = 0 THEN NULL ELSE (c.cnt - m.moving_avg) / m.moving_avg END*100,2) percent_diff
FROM today_counts c
LEFT JOIN moving_avg m
ON c.interval_start = m.interval_start
WHERE c.interval_start < CASE WHEN DATE_PART('mi',CURRENT_TIME) > 30 THEN DATE_PART('hh',CURRENT_TIME) ELSE DATE_PART('hh',CURRENT_TIME) -1 END
ORDER BY c.interval_start desc
;
"""
    df = obj_sf.fetch_query(sf_query)
    

except Exception as e:
    log.error("Unexpected error on running the DQ check: {}".format(str(e)))
    raise e  
