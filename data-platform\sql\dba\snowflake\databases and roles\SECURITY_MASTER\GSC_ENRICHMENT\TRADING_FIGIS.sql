-- TODO: Replace SECURITY_MASTER_UAT.ST_CHECK with SECURITY_MASTER.ST_CHECK once the latter has been created

CREATE TABLE SECURITY_MASTER.GSC_ENRICHMENT.TRADING_FIGIS(
	MARKET_INSTRUMENT_CHARACTERISTICS_SOK VARCHAR,
	TRADING_FIGI VARCHAR(12),
  REASON VARCHAR
)
COMMENT = 'Temporary table to maintain TRADING_FIGI per GoldenSource Equity listing in the REFINED.GSC_MARKET_INSTRUMENT_CHARACTERISTICS table with primary key MARKET_INSTRUMENT_CHARACTERISTICS_SOK. Once these ids are maintained in the GoldenSource ODS DB, this table will be dropped.';

GRANT SELECT ON TABLE SECURITY_MASTER.GSC_ENRICHMENT.TRADING_FIGIS TO DR_SECURITY_MASTER_READER;

INSERT INTO SECURITY_MASTER.GSC_ENRICHMENT.TRADING_FIGIS
WITH 
ranked_mkch AS (
  SELECT
    mkch.*,
    ROW_NUMBER() OVER (
      PARTITION BY mkch.market_instrument_characteristics_sok
      ORDER BY
        mkch.LAST_CHANGE_DATETIME DESC
    ) AS rnk
  FROM
    GOLDENSOURCE.refined.gsc_market_instrument_characteristics AS mkch
    -- repetetive inline scalar subquery is much faster than easier to read CTE :(
  WHERE mkch.version_start_timestamp <= (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN) 
  AND mkch.version_end_timestamp > (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN)
)
SELECT 
    -- debug columns, uncomment as needed
--    mkch.LAST_CHANGE_DATETIME,
--    mkch.version_start_timestamp,
--    mkch.version_end_timestamp,
--    mkch.BB_EXCHANGE_TICKER_ID,
--    mkch.BB_COMPOSITE_TICKER_ID,
--    mkch.ADDITIONAL_LISTING_QUALIFICATION_TYPE,
--    mkch.BB_GLOBAL_ID,
--    mkch.BB_COMPOSITE_GLOBAL_ID,
--    st.TRADINGFIGI,
--    st.PRIMARYFIGI,
--    st.COMPOSITEFIGI,
    
    mkch.MARKET_INSTRUMENT_CHARACTERISTICS_SOK,
    CASE
        -- ADDITIONAL_LISTING_QUALIFICATION_TYPE seems to contain the earlier exchange-level figi when BBG changed the figi, e.g. due to a listing moving from an exhange to another (or the exchange being renamed)
        -- In such cases, it is more likely that this matches the ST choice of "primary"/"trading" figi.
        WHEN ADDITIONAL_LISTING_QUALIFICATION_TYPE <> BB_COMPOSITE_GLOBAL_ID
            and ADDITIONAL_LISTING_QUALIFICATION_TYPE <> BB_GLOBAL_ID
            and ADDITIONAL_LISTING_QUALIFICATION_TYPE is not null THEN ADDITIONAL_LISTING_QUALIFICATION_TYPE
        -- ST rule: If exchange ticker matches composite ticker, use exchange-level figi
        WHEN BB_EXCHANGE_TICKER_ID = BB_COMPOSITE_TICKER_ID THEN BB_GLOBAL_ID
        -- ST rule: If exchange ticker doesn't match composite ticker, use composite figi
        ELSE BB_COMPOSITE_GLOBAL_ID
    END AS TRADING_FIGI,
    CASE
        WHEN TRADING_FIGI = BB_GLOBAL_ID THEN CONCAT('TradingFigi ', TRADING_FIGI, ' from BB_GLOBAL_ID on ', (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN))
        WHEN TRADING_FIGI = BB_COMPOSITE_GLOBAL_ID THEN CONCAT('TradingFigi ', TRADING_FIGI, ' from BB_COMPOSITE_GLOBAL_ID on ', (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN))
        WHEN TRADING_FIGI = ADDITIONAL_LISTING_QUALIFICATION_TYPE THEN CONCAT('TradingFigi ', TRADING_FIGI, ' from ADDITIONAL_LISTING_QUALIFICATION_TYPE on ', (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN))
        ELSE CONCAT('Unknown TradingFigi ', TRADING_FIGI, ' on ', (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN))
    END AS REASON
    
    
from SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN st, ranked_mkch mkch
    -- choose latest version of GoldenSource listing record for the effective date
    where mkch.rnk = 1
    and st.DATE = (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN)
    -- only pick records that appear in the ST_LISTINGS and whose exchange-level ticker matches
    and st.tickerAndExchCode = mkch.bb_exchange_ticker_id
    -- only pick records where our rule of determining TRADING_FIGI matches ST's TRADINGFIGI - others need to be reviewed manually
    and st.TRADINGFIGI = TRADING_FIGI
    -- only pick records that have a unique match in the GoldenSource table when matching on exchange ticker
    and mkch.bb_exchange_ticker_id not in (SELECT bb_exchange_ticker_id 
                                        FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN st, ranked_mkch mkch
                                        WHERE mkch.rnk = 1
                                        AND st.DATE = (SELECT max(DATE) FROM SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN)
                                        AND st.tickerAndExchCode = mkch.bb_exchange_ticker_id 
                                        GROUP BY bb_exchange_ticker_id HAVING count(*) > 1)
    -- only pick records whose MARKET_INSTRUMENT_CHARACTERISTICS_SOK has not yet been added to the TRADING_FIGIS table (manually review & update those if needed)
    and mkch.MARKET_INSTRUMENT_CHARACTERISTICS_SOK not in (SELECT MARKET_INSTRUMENT_CHARACTERISTICS_SOK FROM SECURITY_MASTER.GSC_ENRICHMENT.TRADING_FIGIS);


