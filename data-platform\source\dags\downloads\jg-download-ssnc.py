from airflow import DAG
from airflow.operators.python import Python<PERSON>perator
from datetime import datetime, timedelta, date
import os, sys, pendulum, subprocess, logging, pytz
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from airflow.timetables.trigger import CronTriggerTimetable
from strunner import *
setupEnvironment()

today = datetime.now(tz=pytz.timezone('America/New_York'))
date_pattern = today.strftime('%Y%m%d')
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)

def download():
    command = (
        f"python3 {DATA_PIPELINE_PATH}/ssnc/ssnc_rawdata_sync.py"
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 8, 3, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id='jg-sync-ssnc-rawdata',
    default_args=default_args,
    description='This DAG downloads the daily files for SS&C',
    schedule=CronTriggerTimetable('*/10 4-12 * * 1-5', timezone="America/New_York"),
    tags=["jgdata", "ssnc"],
    max_active_runs = 1,
    catchup=False,
)

download_op = PythonOperator(
    task_id="jg-sync-ssnc-rawdata",
    python_callable=download,
    dag=dag
)

download_op