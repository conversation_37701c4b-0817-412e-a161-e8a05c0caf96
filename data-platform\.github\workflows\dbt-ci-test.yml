name: DBT CI Tests

on:
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Optional DBT tag to run and test'
        required: false
        default: ''

jobs:
  dbt-ci-tests:
    runs-on:
      group: core

    env:
      SNOWFLAKE_ACCOUNT: ${{ secrets.SNOWFLAKE_ACCOUNT }}
      SNOWFLAKE_USER: ${{ secrets.SN<PERSON><PERSON>AKE_USER }}
      SNOWFLAKE_PASSWORD: ${{ secrets.SNOWFLAKE_PASSWORD }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Conda Setup in Runner
        run: |
          export MAMBA_EXE="/jfs/tools/conda/exec/micromamba"
          export MAMBA_ROOT_PREFIX="/jfs/tech1/conda/"
          eval "$($MAMBA_EXE shell hook --shell bash --root-prefix $MAMBA_ROOT_PREFIX)"
          micromamba activate tech1-datait
          echo "Micromamba environment activated."

      - name: Write DBT profiles.yml
        run: |
          cat <<EOF > source/dbt/dbt_data_platform/profiles.yml
          ${{ secrets.DBT_PROFILES_YML }}
          EOF

      - name: Run DBT (Full or Tag-based)
        run: |
          export MAMBA_EXE="/jfs/tools/conda/exec/micromamba"
          export MAMBA_ROOT_PREFIX="/jfs/tech1/conda/"
          eval "$($MAMBA_EXE shell hook --shell bash --root-prefix $MAMBA_ROOT_PREFIX)"
          micromamba activate tech1-datait
          cd source/dbt/dbt_data_platform
          export DBT_PROFILES_DIR=$(pwd)

          if [[ -z "${{ github.event.inputs.tag_name }}" ]]; then
            echo "Running all DBT models..."
            dbt run
            dbt test
          else
            echo "Running DBT models with tag: ${{ github.event.inputs.tag_name }}"
            dbt run -s tag:${{ github.event.inputs.tag_name }}
            dbt test -s tag:${{ github.event.inputs.tag_name }}
          fi
