{{ 
    config(alias="options", materialized="view", tags=["ice","mft","options"]
    ) }}

select
    'EOD' AS source,
    TRADE_DATE as trade_date,
    HUB as hub,
    '' AS market_id,
    LONG_NAME as long_name,
    COMMODITY as commodity,
    STRIP as strip,
    '' AS relative_period,
    PUT_CALL as put_call,
    STRIKE as strike,
    SETTLEMENT_PRICE as settlement_price,
    PRODUCT as product,
    null as net_change,
    OPTION_VOLATILITY as option_volatility, 
    null as expiration_date,
    DELTA_FACTOR as delta_factor,
    LOW_PRICE as low_price,
    HIGH_PRICE as high_price,
    OPEN_PRICE as open_price,
    CLOSE_PRICE as close_price,
    TOTAL_VOLUME as total_volume,
    OPEN_INTEREST as open_interest,
    EFP_VOLUME as efp_volume,
    EFS_VOLUME as efs_volume,
    BLOCK_VOLUME as block_volume,
    SPREAD_VOLUME as spread_volume,
    null as strike_price,
    MIC as mic,
    FILE_FLAG as file_flag,
    FNAME_SHORT as fnme_short,
    FIL<PERSON>AM<PERSON> as filename,
    START_SCAN_TIME as start_scan_time
from {{ ref("int_ice_mft_options")}}

UNION ALL

select
    'UTIL MKT' AS source,
    SETTLEMENT_PRICE_DATE as trade_date,
    HUB as hub,
    MARKET_ID as market_id,
    PRODUCT as long_name,
    COMMODITY_CODE as commodity,
    STRIP as strip,
    RELATIVE_PERIOD as relative_period,
    CONTRACT_TYPE as put_call,
    STRIKE_PRICE as strike,
    SETTLEMENT_PRICE as settlement_price,
    PRODUCT_ID as product,
    null as net_change,
    OPTION_VOLATILITY as option_volatility,
    null as expiration_date,
    DELTA_FACTOR as delta_factor,
    null as low_price,
    null as high_price,
    null as open_price,
    null as close_price,
    null as total_volume,
    null as open_interest,
    null as efp_volume,
    null as efs_volume,
    null as block_volume,
    null as spread_volume,
    strike_price,
    MIC as mic,
    FILE_FLAG as file_flag,
    FNAME_SHORT as fnme_short,
    FILENAME as filename,
    START_SCAN_TIME as start_scan_time
from {{ ref("int_ice_mft_utilmkt_options")}}