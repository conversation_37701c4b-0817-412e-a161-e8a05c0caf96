from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor

from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from bloomberg.per_security.parser import BloombergParser
import pandas as pd
import tempfile
import shutil
import gzip
import os

from utils.date_utils import get_now

if __name__ == "__main__":

    securities = ["FDTR Index", "INJCJC Index", "NFP TCH Index", "CPI YOY Index", "CPI CHNG Index", "NAPMPMI Index", "CONSSENT Index", "RSTAMOM Index", "FDIDFDMO Index", "ADP CHNG Index", "FEDMMINU Index", "PCE DEFM Index", "USN20YTA Index", "USN10YTA Index", "USBD30YT Index", "TII1HYLD Index"]
    per_sec_req_type = PerSecurityRequestType.getdata
    batch_name = "dp_erd1"

    request_dict = {
        "firm_name": "dl47544",
        "program_flag": "adhoc",
        "sec_id": "TICKER",
        "output_format": "bulklist",
        "fields": ["ECO_FUTURE_RELEASE_DATE_LIST"],
        "securities": securities,
    }

    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/eco_rel_dates"
    eco_rel_dates = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)
    
    print(eco_rel_dates)

    parser = BloombergParser(eco_rel_dates, sep='|', skipinitialspace=True, on_bad_lines='error') 
    df_data = parser.parse_data()
    df_data["ECO_RELEASE_DATETIME"] = pd.to_datetime(df_data["ECO_RELEASE_DATETIME"], errors="coerce")
    df_data["WHEN_UPDATED_UTC"] = get_now("UTC").replace(second=0, microsecond=0).strftime('%Y-%m-%d %H:%M:%S')
    df_data["SECURITY"] = df_data["SECURITY"].str.strip()
    df_data['ECO_RELEASE_DATETIME'] = df_data['ECO_RELEASE_DATETIME'].dt.strftime('%Y-%m-%d %H:%M:%S')
    df_data.rename(columns={"SECURITY": "BBG_TICKER"}, inplace=True)

    target_schema = 'BBGH_ONDEMAND'
    target_table = 'BBG_MACRO_EVENT_DATES'
    
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", 
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER")
    
    sf_adaptor.write_pandas_dataframe(target_schema, df_data, target_table)