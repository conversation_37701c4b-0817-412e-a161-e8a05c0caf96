use role accountadmin; 

CREATE ROLE IF NOT EXISTS DR_EXT_FACTSET_OWNER;
CREATE ROLE IF NOT EXISTS DR_EXT_FACTSET_READER;
CREATE ROLE IF NOT EXISTS DR_EXT_FACTSET_WRITER;

GRANT ROLE DR_EXT_FACTSET_READER to ROLE FR_DATA_PLATFORM;

CREATE DATABASE IF NOT EXISTS EXT_FACTSET;
GRANT OWNERSHIP ON DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_OWNER;

GRANT ROLE DR_EXT_FACTSET_OWNER TO ROLE FR_DATA_PLATFORM_LEADS;
GRANT ROLE DR_EXT_FACTSET_OWNER TO ROLE ACCOUNTADMIN;
GRANT ROLE DR_EXT_FACTSET_WRITER TO USER DATA_PLATFORM_USER;
GRANT ROLE DR_EXT_FACTSET_READER TO ROLE REBAL_READONLY;

USE ROLE DR_EXT_FACTSET_OWNER;

GRANT USAGE ON DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_READER;
GRANT USAGE ON DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_WRITER;

USE DATABASE EXT_FACTSET;

USE ROLE AR_GRANT_ADMIN;

GRANT OWNERSHIP ON FUTURE SCHEMAS IN DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES  IN DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS   IN DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_OWNER;

GRANT USAGE ON FUTURE SCHEMAS IN DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_READER;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_WRITER;

GRANT SELECT ON FUTURE TABLES IN DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_READER;
GRANT SELECT ON FUTURE TABLES IN DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_WRITER;

GRANT SELECT ON FUTURE VIEWS IN DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_READER;
GRANT SELECT ON FUTURE VIEWS IN DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_WRITER;


GRANT INSERT, UPDATE, DELETE ON FUTURE TABLES IN DATABASE EXT_FACTSET TO ROLE DR_EXT_FACTSET_WRITER;
