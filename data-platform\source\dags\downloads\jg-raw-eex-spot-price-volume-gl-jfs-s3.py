from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.timetables.trigger import CronTriggerTimetable
from datetime import timedel<PERSON>, datetime
import pendulum
from util.dataset import to_s3
from airflow.providers.http.operators.http import HttpOperator
from util.pre_process_early_final_operator import PreProcessEarlyFinalOperator
from util.validate import check_for_anomalies
import os

dag_id = os.path.basename(__file__).replace(".py","")

def get_jg_env():
    env_value = os.getenv('jg_env', 'prod').lower()
    if env_value not in ['prod', 'uat']:
        raise ValueError(f"Invalid jg_env value: {env_value}. Allowed values are 'prod' or 'uat'.")
    return env_value

env = get_jg_env()

BASE_PATH="/jfs/tech1/apps/rawdata/eex/spot/1.0/"
PREPROCESS_FOLDERS_PRICE_VOLUMES = [
    # Prices and Volumes
    'finland/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'austria/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'belgium/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'denmark 1/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'denmark 2/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'france/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'germany/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'netherlands/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'norway 1/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'norway 2/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'norway 3/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'norway 4/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'norway 5/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'poland/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'sweden 1/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'sweden 2/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'sweden 3/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
    'sweden 4/Day-Ahead Auction/Hourly/Current/Prices_Volumes/',
]

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 11, 4, tz="Europe/Berlin"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 3,
    "retry_delay": timedelta(minutes=3),
}


doc_md_DAG=f'This DAG downloads the data from EEX Spot from SFTP to JSF and to S3'
dag = DAG(
    dag_id=dag_id,
    default_args=default_args,
    description='This DAG downloads the data from EEX Spot from SFTP to JSF and to S3',
    schedule=CronTriggerTimetable('0 13 * * *',timezone="Europe/Berlin"),
    tags=["phdata","eex","spot"],
    catchup=False,
    doc_md=doc_md_DAG,
)

op_kwargs = {
    "dataset": "eex.spot.gl.price.volume",
}

raw = PythonOperator(
    task_id="jg-raw-eex-spot-gl-jfs-s3",
    python_callable=to_s3,
    dag=dag,
    op_kwargs=op_kwargs,
)

preprocess_price_volumes = PreProcessEarlyFinalOperator(
    task_id="preprocess_rename_price_volumes",
    base_path=BASE_PATH,
    preprocess_folders=PREPROCESS_FOLDERS_PRICE_VOLUMES,
    env=env,
    use_dynamic_date_suffix=True,
    date_format='%Y%m%d',
    file_operation = "rename",
    dag=dag,
)

validation_job = HttpOperator(
    task_id="call-daily-data-validation-api",
    http_conn_id="http_default", 
    endpoint=f"getJFSFeedAvailabilitySatusDailyDownload/{dag_id}",
    method="GET",
    headers={"Content-Type": "application/json"},
    response_check=check_for_anomalies,
    extra_options={"check_response": True},
    log_response=True,
    dag=dag
)

raw >> preprocess_price_volumes >> validation_job