import sys
import os

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from jglib.logger import *
from jglib.infra.iceberg import *
log.info("Main Program Imported Logger.. Invoking jglib.datasets.snp.holdings")
import jglib.datasets.snp.feeds
log.info("Main Program imported jglib.datasets.snp.feeds.. Calling jglib.infra.iceberg.DataCache()")
obj = jglib.infra.iceberg.DataCache()
log.info("Main Program Invoked jglib.infra.iceberg.DataCache().. InitiDataSet")

obj.initDataset('snp.feeds')
obj.readTable('snp.feeds.Constituent')
obj.run_query('snp.feeds.Constituent.mk1', "UPDATE snp.feeds.Constituent.mk1 SET securityID = securityID || '.0' WHERE securityID NOT LIKE '%.%';")
