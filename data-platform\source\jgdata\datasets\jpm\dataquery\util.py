from calendar import calendar
import datetime
from urllib.parse import (
    parse_qs,
    quote,
    urlencode,
)

import os
import pandas as pd
import requests
from loguru import logger as log
from datetime import timed<PERSON><PERSON>

from jgdata import datalake, read_toml
from stcommon.infra.rds.snowflake_operation import *
from jglib.paths import jg_data_path

    
fpath = os.environ.get('JGDATA_PATH')
config_secret = read_toml(f'{fpath}/conf/sources/.secret.toml')

key_to_check =  'jpm.dataquery.kwargs'

dataquery_config = {
    "client_id": config_secret[key_to_check]['client_id'],
    "client_secret": config_secret[key_to_check]['client_secret']
}

# Utility functions and variables for the notebook

_dq_stored_token = None  # singleton-like variable


def _get_token(dataquery_config: dict) -> str:
    """
    Generating an access token for DataQuery APIs access via OAuth using Client ID and Secret. The function also handles token refresh when the access token is nearly expiring.

    Parameters
    ----------
    `dataquery_config` : dict
        The dictionary with the properties needed for the connection

    Raises
    ----------
    Exception:
        When unable to obtain an access token

    Returns
    -------
    `str`
        The access token for API connection
    """
    global _dq_stored_token  # reference to module variable
    dq_token_provider_url = (
        "https://authe.jpmchase.com/as/token.oauth2"  # JP Morgan OAuth server
    )
    dq_api_resource_id = (
        "JPMC:URI:RS-06785-DataQueryExternalApi-PROD"  # DataQuery APIs resource ID
    )

    # only generate an access token when
    # 1) there's no existing access token stored in `_dq_stored_token`
    # 2) the existing access token reaches 85% of its expiration duration
    if _dq_stored_token is None or (
        ((datetime.datetime.now() - _dq_stored_token["created_at"]).total_seconds())
        >= (_dq_stored_token["expires_in"] * 0.85)
    ):
        json = requests.post(
            url=dq_token_provider_url,
            proxies={"https": dataquery_config["https_proxy"]}
            if "https_proxy" in dataquery_config
            else {},
            data={
                "grant_type": "client_credentials",
                "client_id": dataquery_config["client_id"],
                "client_secret": dataquery_config["client_secret"],
                "aud": dq_api_resource_id,
            },
        ).json()
        # stores the token in the singleton-like variable together with `created_at` (=system date-time) and `expires_in` (=expiration time in seconds, from the response)
        if "access_token" in json:
            _dq_stored_token = {
                "created_at": datetime.datetime.now(),
                "access_token": json["access_token"],
                "expires_in": json["expires_in"],
            }
        else:
            raise Exception("Unable to obtain an OAuth token: {0}".format(json))

    return _dq_stored_token["access_token"]


def get_dq_api_result(path: str, params: dict, page_count: int = 1) -> dict:
    """Getting data from DataQuery APIs

    Args:
        path (str): The DataQuery APIs endpoint for the request

        params (dict): The parameters for the API request. See [API specification](https://developer.jpmorgan.com/products/dataquery_api/specification) for all supported parameters.

        page_count (int, optional): The count of pages to request when results are paginated. Defaults to 1. `0` = all pages.

    Returns:
        dict: The API response converted to dictionary
    """

    if page_count < 0:
        raise Exception("page_count must be equal to or greater than 0")

    dq_api_url = "https://api-developer.jpmorgan.com/research/dataquery-authe/api/v2"  # DataQuery APIs base url

    # concatenates path to base path from config, and issues a GET call using the params and including the token from function above
    token = _get_token(dataquery_config)
    # returns the JSON response as dict if successful or raises an exception if error

    response = requests.get(
        url=dq_api_url + path,
        params=urlencode(params, True, quote_via=quote),
        headers={"Authorization": "Bearer " + token},
        proxies={"https": dataquery_config["https_proxy"]}
        if "https_proxy" in dataquery_config
        else {},
    )
    # print(response.request.url) # uncomment this line if you want to see the constructed URL for API call
    response_dict = response.json()

    if "errors" in response_dict:
        raise Exception("Unable to obtain response: {0}".format(response_dict))

    if "info" in response_dict:
        raise Exception("{0}".format(response_dict))

    if "/grid-data" in path:
        return response_dict

    if page_count == 1:
        if (
            response_dict.get("page-size") < response_dict.get("items")
            and response_dict["links"][1]["next"] is not None
        ):
            print(
                "{CBEG}Info: Current request retrieves {current} items from 1 page. DataQuery has {total} items related to this request. Add `page_count=0` to `get_dq_api_result()` request to retrieve all pages.{CEND}".format(
                    CBEG="\033[93m",
                    current=response_dict.get("page-size"),
                    total=response_dict["items"],
                    CEND="\033[00m",
                )
            )
            try:
                next_page_token = parse_qs(response_dict["links"][1]["next"]).get(
                    "page"
                )[0]
                print(
                    "{CBEG}Token for next page that can be passed as `page` in `params` for the `get_dq_api_result()` request: {BBEG}{token}{CEND}".format(
                        CBEG="\033[93m",
                        BBEG="\033[44m",
                        token=next_page_token,
                        CEND="\033[00m",
                    )
                )
            except Exception:
                print(
                    "{CBEG}This is the last page.{CEND}".format(
                        CBEG="\033[93m", CEND="\033[00m"
                    )
                )
        return response_dict

    if page_count == 0 or page_count >= 2:
        try:
            next_path = response_dict["links"][1]["next"]
            paginated_response = []
            paginated_response.append(response_dict)
            get_next = True
            retrieved_page_count = 1
            retrieved_item_count = response_dict.get("page-size")
            print(
                f'{min(page_count, (-(-response_dict["items"] // 20)))} pages requested{" instead" if (-(-response_dict["items"] // 20)) < page_count else ""}. DataQuery total: {(-(-response_dict["items"] // 20))} pages for {response_dict["items"]} items.\nReceived total: {retrieved_page_count} pages for {retrieved_item_count} items',
                end="\r",
                flush=True,
            )
            while next_path is not None and get_next is True:
                next_page_response = requests.get(
                    url=dq_api_url + next_path,
                    headers={"Authorization": "Bearer " + token},
                    proxies={"https": dataquery_config["https_proxy"]}
                    if "https_proxy" in dataquery_config
                    else {},
                )
                next_page_response_dict = next_page_response.json()
                if "errors" in next_page_response_dict:
                    raise Exception(
                        "Unable to obtain response: {0}".format(next_page_response_dict)
                    )
                paginated_response.append(next_page_response_dict)
                retrieved_item_count += next_page_response_dict.get("page-size")
                retrieved_page_count += 1
                print(
                    "Page {} retrieved: {} items retrieved".format(
                        retrieved_page_count, retrieved_item_count
                    ),
                    end="\r",
                )
                get_next = (
                    False
                    if retrieved_page_count == page_count and page_count != 0
                    else True
                )
                next_path = next_page_response_dict["links"][1]["next"]
        except Exception:
            pass

        if retrieved_item_count < response_dict.get("items"):
            print(
                "{CBEG}Info: Current request retrieves {current} items from {pages} pages. DataQuery has {total} items related to this request. Add `page_count=0` to `get_dq_api_result()` request to retrieve all pages.{CEND}".format(
                    CBEG="\033[93m",
                    current=retrieved_item_count,
                    pages=retrieved_page_count,
                    total=response_dict["items"],
                    CEND="\033[00m",
                )
            )
            try:
                next_page_token = parse_qs(next_path).get("page")[0]
                print(
                    "{CBEG}Token for next page that can be passed as `page` in `params` for the `get_dq_api_result()` request: {BBEG}{token}{CEND}".format(
                        CBEG="\033[93m",
                        BBEG="\033[44m",
                        token=next_page_token,
                        CEND="\033[00m",
                    )
                )
            except Exception:
                print(
                    "{CBEG}This is the last page.{CEND}".format(
                        CBEG="\033[93m", CEND="\033[00m"
                    )
                )
        else:
            print("\nAll {} items received".format(retrieved_item_count))

        return paginated_response
    
def getAPIData(df, batch, start_date_input, end_date_input, rawdata_file, failed_expressions_list):
    expression_results = get_dq_api_result(
    path="/expressions/time-series",  # the endpoint (path) in the DataQuery APIs V2 spec
    params={
        "expressions": batch,  # up to 20 expressions are supported - see API spec
        "start-date": start_date_input,  # Default = `TODAY-1D` if not `provided`. Format: `YYYYMMDD` or `TODAY-nX` where `n` is integer and `X` is one of ['D', 'W', 'M', 'Y']. Max one-year data per API request is recommended for best response time.
        "end-date": end_date_input,  # Default = `TODAY` if not provided. Max one-year data per API request is recommended for best response time.
        "data": "ALL",  # Recommend to set as`ALL` if you expect market data. Default = `REFERENCE_DATA`
        "calendar": "CAL_ALLDAYS",  # Recommended to use 'CAL_WEEKDAYS' to ensure international coverage
        "non-treatment": "NA_NOTHING",  # Default = `NA_NOTHING` if not provided
        # "frequency": frequency
        # "page": "",  # the token for a specific page
        },
    )  # Refer to API Spec https://developer.jpmorgan.com/products/dataquery_api/specification/get_expressions_time-series for parameters supported
  
    rawdata_file.write(str(expression_results))
    # 
    for instrument in expression_results.get('instruments'):
        for attribute in instrument.get('attributes'):
            if attribute.get('message'):
                failed_expressions_list.append(attribute.get('expression'))
            
    # normalize the JSON response at date level
    df_time_series = pd.json_normalize(
        expression_results,
        record_path=["instruments", "attributes", "time-series"],
        meta=[["instruments", "attributes", "expression"]],
    )
    df_time_series.columns = ["date", "value", "expression"]
    if df.empty:
        df = df_time_series.copy()
    else:
        df = pd.concat([df, df_time_series], ignore_index=True)
        df = df[["date", "expression", "value"]]
    return df, failed_expressions_list
    
def getData(end_date_input, daterange, expressionList):
    try:
        df = pd.DataFrame(columns=["date", "value", "expression"])
        batch_size = 20
        
        rawdata_file = open(f"{jg_data_path()}/jpm/dataquery/jpm_api_data_{end_date_input}",'a+') 
        rawdata_file.write(f"\n\n###############################################\nAPI data download triggered at {pd.Timestamp.utcnow()}\nData for DateRange '{daterange}'\n")
        failed_expressions_list = []
        for i in range(0, len(expressionList), batch_size):
            if daterange == '10D':
                start_date_input = 'TODAY-9D'
                end_date_input = end_date_input
            elif daterange == '10W':
                start_date_input = 'TODAY-10W'
                end_date_input = 'TODAY' 
            elif daterange == '1Y':
                start_date_input = 'TODAY-1Y'
                end_date_input = 'TODAY'
            else:
                raise ValueError("Unsupported range unit. Use 'W', 'D', or 'Y'.")

            batch = expressionList[i:i + batch_size]
            df, failed_expressions_list = getAPIData(df, batch, start_date_input, end_date_input, rawdata_file, failed_expressions_list)
            
        df = df.dropna(axis=0, how='any')
        df = df.reset_index(drop=True)
        if failed_expressions_list:
            print(f"{daterange} expressions with failed message: {failed_expressions_list}\n")
            log.error(f"{daterange} expressions with failed message: {failed_expressions_list}")
        return df
    except Exception as e:
        log.error("Unexpected error on fetching data from API: {}".format(str(e)))
        raise e
    
def get_availdate(ref_date):
    next_day = ref_date + timedelta(days=1)
    if next_day.weekday() == 5:  # Saturday
        availdate = next_day + timedelta(days=2)
    elif next_day.weekday() == 6:  # Sunday
        availdate = next_day + timedelta(days=1)
    else:
        availdate = next_day    # Mon-Fri
    # Set the availdate to 10:00 AM UTC
    availdate = availdate.replace(hour=10, minute=0, second=0, microsecond=0)
    return availdate

    
def getHistoricalData(end_date_input, obj_sf, config_table, output_table):
    try:
        df = pd.DataFrame(columns=["date", "value", "expression"])
        batch_size = 20
        config_expressionList = obj_sf.get_unique_column_values(config_table, 'EXPRESSION')
        data_feeds_expression_list = obj_sf.get_unique_column_values(output_table, 'EXPRESSION')

        new_expressions_added = list(set(config_expressionList) - set(data_feeds_expression_list))
        
        failed_expressions_list = []
        if(len(new_expressions_added) > 0):
            print("Historical one-time dump for new tickers added in progress")
            rawdata_file = open(f"{jg_data_path()}/jpm/dataquery/jpm_api_data_{end_date_input}",'a+')
            rawdata_file.write(f"\n\n###############################################\nAPI data download triggered for Historical load at {pd.Timestamp.utcnow()}\n")
            
            for i in range(0, len(new_expressions_added), batch_size):
                batch = new_expressions_added[i:i + batch_size]
                start_date_input = 20000101
                df, failed_expressions_list = getAPIData(df, batch, start_date_input, end_date_input, rawdata_file, failed_expressions_list)
        
        df = df.dropna(axis=0, how='any')
        df = df.reset_index(drop=True)
        
        df.rename(columns = {'date':'REF_DATE'}, inplace=True)
        df.rename(columns = {'expression':'EXPRESSION'}, inplace=True)
        df.rename(columns = {'value':'VALUE'}, inplace=True)
        df['REF_DATE'] = pd.to_datetime(df['REF_DATE'])

        df['AVAILDATE'] = df['REF_DATE'].apply(get_availdate)
        df.rename(columns = {'availDate':'AVAILDATE'}, inplace=True)
        df['AVAILDATE'] = df['AVAILDATE'].dt.strftime('%Y-%m-%d %H:%M:%S')
        df['REF_DATE'] = pd.to_datetime(df['REF_DATE']).dt.strftime('%Y-%m-%d %H:%M:%S')
        df = df[['REF_DATE', 'EXPRESSION', 'VALUE', 'AVAILDATE']]
        df = df.drop_duplicates()
        
        if failed_expressions_list:
            print(f"Expressions with failed message: {failed_expressions_list}\n")
            log.error(f"Expressions with failed message: {failed_expressions_list}")
        return df
    except Exception as e:
        log.error("Unexpected error on fetching data from API: {}".format(str(e)))
        raise e
