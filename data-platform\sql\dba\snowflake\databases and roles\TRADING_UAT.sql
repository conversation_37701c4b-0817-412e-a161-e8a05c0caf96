use role ACCOUNTADMIN;

CREATE DATABASE TRADING_UAT;

USE DATABASE TRADING_UAT;


CREATE ROLE IF NOT EXISTS DR_TRADING_UAT_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_TRADING_UAT_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_TRADING_UAT_DB_OWNER;

GRANT USAGE ON WAREHOUSE TRADING_NONPROD_WH TO ROLE DR_TRADING_UAT_READ_ONLY;
GRANT USAGE ON WAREHOUSE TRADING_NONPROD_WH TO ROLE DR_TRADING_UAT_READ_WRITE;
GRANT USAGE ON WAREHOUSE TRADING_NONPROD_WH TO ROLE DR_TRADING_UAT_DB_OWNER;


GRANT ROLE DR_TRADING_UAT_DB_OWNER TO ROLE FR_DATA_PLATFORM ;
GRANT OWNERSHIP ON DATABASE TRADING_UAT TO ROLE DR_TRADING_UAT_DB_OWNER;


GRANT USAGE ON DATABASE TRADING_UAT TO ROLE DR_TRADING_UAT_READ_ONLY;
GRANT USAGE ON DATABASE TRADING_UAT TO ROLE DR_TRADING_UAT_READ_WRITE;
GRANT USAGE ON DATABASE TRADING_UAT TO ROLE DR_TRADING_UAT_DB_OWNER;


GRANT ROLE DR_TRADING_UAT_DB_OWNER TO USER VIJAYVASUDEVAN;
GRANT ROLE DR_TRADING_UAT_DB_OWNER TO USER SVC_TRADING_NONPROD;

GRANT ROLE DR_TRADING_UAT_READ_WRITE TO USER JAMESOCALLAGHAN ;

GRANT ROLE DR_TRADING_UAT_READ_ONLY TO ROLE FR_TRADING_PROD ;





CREATE SCHEMA TRADING_UAT.TRD_CONFIG ;
GRANT ALL ON SCHEMA TRADING_UAT.TRD_CONFIG TO ROLE DR_TRADING_UAT_DB_OWNER;

GRANT USAGE ON SCHEMA TRADING_UAT.TRD_CONFIG TO ROLE DR_TRADING_UAT_READ_WRITE;
GRANT USAGE ON SCHEMA TRADING_UAT.TRD_CONFIG TO ROLE DR_TRADING_UAT_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA TRADING_UAT.TRD_CONFIG TO DR_TRADING_UAT_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA TRADING_UAT.TRD_CONFIG TO DR_TRADING_UAT_READ_ONLY;

GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA TRADING_UAT.TRD_CONFIG TO DR_TRADING_UAT_READ_WRITE;
GRANT SELECT  							   ON FUTURE VIEWS  IN SCHEMA TRADING_UAT.TRD_CONFIG TO DR_TRADING_UAT_READ_WRITE;




CREATE SCHEMA TRADING_UAT.TRD_POSITIONS ;
GRANT ALL ON SCHEMA TRADING_UAT.TRD_POSITIONS TO ROLE DR_TRADING_UAT_DB_OWNER;

GRANT USAGE ON SCHEMA TRADING_UAT.TRD_POSITIONS TO ROLE DR_TRADING_UAT_READ_WRITE;
GRANT USAGE ON SCHEMA TRADING_UAT.TRD_POSITIONS TO ROLE DR_TRADING_UAT_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA TRADING_UAT.TRD_POSITIONS TO DR_TRADING_UAT_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA TRADING_UAT.TRD_POSITIONS TO DR_TRADING_UAT_READ_ONLY;

GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA TRADING_UAT.TRD_POSITIONS TO DR_TRADING_UAT_READ_WRITE;
GRANT SELECT  							   ON FUTURE VIEWS  IN SCHEMA TRADING_UAT.TRD_POSITIONS TO DR_TRADING_UAT_READ_WRITE;



CREATE SCHEMA TRADING_UAT.TRD_OMS ;
GRANT ALL ON SCHEMA TRADING_UAT.TRD_OMS TO ROLE DR_TRADING_UAT_DB_OWNER;

GRANT USAGE ON SCHEMA TRADING_UAT.TRD_OMS TO ROLE DR_TRADING_UAT_READ_WRITE;
GRANT USAGE ON SCHEMA TRADING_UAT.TRD_OMS TO ROLE DR_TRADING_UAT_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA TRADING_UAT.TRD_OMS TO DR_TRADING_UAT_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA TRADING_UAT.TRD_OMS TO DR_TRADING_UAT_READ_ONLY;

GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA TRADING_UAT.TRD_OMS TO DR_TRADING_UAT_READ_WRITE;
GRANT SELECT  							   ON FUTURE VIEWS  IN SCHEMA TRADING_UAT.TRD_OMS TO DR_TRADING_UAT_READ_WRITE;



CREATE SCHEMA TRADING_UAT.TRD_SIGNALS ;
GRANT ALL ON SCHEMA TRADING_UAT.TRD_SIGNALS TO ROLE DR_TRADING_UAT_DB_OWNER;

GRANT USAGE ON SCHEMA TRADING_UAT.TRD_SIGNALS TO ROLE DR_TRADING_UAT_READ_WRITE;
GRANT USAGE ON SCHEMA TRADING_UAT.TRD_SIGNALS TO ROLE DR_TRADING_UAT_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA TRADING_UAT.TRD_SIGNALS TO DR_TRADING_UAT_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA TRADING_UAT.TRD_SIGNALS TO DR_TRADING_UAT_READ_ONLY;

GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA TRADING_UAT.TRD_SIGNALS TO DR_TRADING_UAT_READ_WRITE;
GRANT SELECT  							   ON FUTURE VIEWS  IN SCHEMA TRADING_UAT.TRD_SIGNALS TO DR_TRADING_UAT_READ_WRITE;



CREATE SCHEMA TRADING_UAT.TRD_DATA ;
GRANT ALL ON SCHEMA TRADING_UAT.TRD_DATA TO ROLE DR_TRADING_UAT_DB_OWNER;

GRANT USAGE ON SCHEMA TRADING_UAT.TRD_DATA TO ROLE DR_TRADING_UAT_READ_WRITE;
GRANT USAGE ON SCHEMA TRADING_UAT.TRD_DATA TO ROLE DR_TRADING_UAT_READ_ONLY;

GRANT SELECT ON FUTURE TABLES IN SCHEMA TRADING_UAT.TRD_DATA TO DR_TRADING_UAT_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA TRADING_UAT.TRD_DATA TO DR_TRADING_UAT_READ_ONLY;

GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA TRADING_UAT.TRD_DATA TO DR_TRADING_UAT_READ_WRITE;
GRANT SELECT  							   ON FUTURE VIEWS  IN SCHEMA TRADING_UAT.TRD_DATA TO DR_TRADING_UAT_READ_WRITE;

