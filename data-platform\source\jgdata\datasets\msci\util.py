from jgdata.datasets.msci import *
import stcommon.tools.sftp as sftp

# SFTP connection
# --------------------------------------------------------------------------------------------------

@lru_cache(maxsize=1)
def msciConn():
  return sftp.conn('sftp.barra.com',username='pwdmqvmz',password='h5LtKjDZ+s,9nryO!vX2')

# Secmaster: shared with pricing
# --------------------------------------------------------------------------------------------------

def getRawPath():
  if os.environ.get('USER') == 'quant_admin':
    return '/home/<USER>/MSCI/secmaster/'
  return f'{getRawStoreMountPath()}MSCI/secmaster/1.0/'

MSCI_SECMASTER_RAW = getRawPath() # raw path data path
## JG-DATA-PLATFORM: HACK
## Data missing in FTP and S3 from 03/01 to 03/21. Hence we decided to go with a new FULL File
## END-DATE FIX was added so that it can stop at 05/08, since INC files not available after that date
MSCI_SECMASTER_DATE = date_t(20240531) # date we anchor download ##FOR  EQUITIES
MSCI_SECMASTER_END_DATE = None
# MSCI_SECMASTER_END_DATE = date_t(20240620) # date we stop.. HACK FOR EQUITIES
## SEPARATE FIX FOR REFDATA.. WE ONLY LOADED DATA FOR ONE MONTH.. SINCE NO FULL FILES AFER THAT DATE.. INC FILES MISSING TILL 03/21
## ValueError: Missing: /jfs/tech1/apps/rawdata/MSCI/secmaster/1.0//exchangeholidays/ExchangeHolidays_INC_EOD.20240301.zip
#MSCI_SECMASTER_DATE = date_t(20240614) # date we anchor download ##FOR  REFDATA
#MSCI_SECMASTER_END_DATE = date_t(202400620) # date we stop.. HACK FOR REFDATA

# File types|Latest release time(EST/EDT)
# INC_1 17:00
# INC_2 21:30
# INC_EOD/HC_EOD/DEL_EOD T+1 5:00
# FULL Monday 8:00
# HC_WE Monday 8:30

def getWeeklyDate():
    # TODO remove return dates.prior_day(getToday('na'),'Fri')
  return to_date('20240223')

def secmasterDates(full_load=False):
  dlist = calendar.getBusinessDates() # first region active
  ##JG-DATA-PLATFORM FIX: Hack for setting end date
  if MSCI_SECMASTER_END_DATE:
    dlist = dates.less_than(dates.greater_than(dlist,MSCI_SECMASTER_DATE),MSCI_SECMASTER_END_DATE) # always go back 10 days to rebuild
  else:
    dlist = dates.greater_than(dlist,MSCI_SECMASTER_DATE) # always go back 10 days to rebuild
  sdate = MSCI_SECMASTER_DATE if full_load else dates.sub_day(getToday(),10)
  dlist = dates.greater_eq(dlist,sdate)
  return dates.less_than(dlist,getToday('na'))

def secmasterMonth(date):
  dlist = calendar.getBusinessDates() # first region active
  dlist = dates.greater_than(dlist,MSCI_SECMASTER_DATE)
  return dates.within(dlist,dates.to_month_range(date)) # dates in month

def secmaster_read(filename,fdict=None):
  fname = f"{MSCI_SECMASTER_RAW}/{filename}"
  log.info(f"Reading: {fname}")
  df = read_csv(fname,sep='|',skiprows=1,comment='#TOTAL RECORDS')
  df.columns = [x.lower().replace('#','') for x in df.columns]
  df = df.rename({'barra id':'barra id','barra issuer id':'issuer id'},axis=1)
  df.columns = [camel(x) for x in df.columns]
  if not fdict is None:
    df = df.rename(fdict,axis=1)
  if 'startDate' in df.columns:
    df['startDate'] = to_datetime(df.startDate)
    df['endDate'] = to_datetime(df.endDate)
  return df

def getFileTypes(date,ftypes=['INC_1','INC_2','INC_EOD','HC_EOD'],corrections=False):
  flist = []
  if dates.less_eq(date,MSCI_SECMASTER_DATE):
    return flist

  avail_time = f"{dates.add_day(date,1)}-04:00:00" # try to figure out when available
  if 'INC_1' in ftypes and (avail_time < getTime('as')):
    flist.append(f"INC_1.{date}")
  if 'INC_2' in ftypes and (avail_time < getTime('eu')):
    flist.append(f"INC_2.{date}")
  if 'INC_EOD' in ftypes and (avail_time < getTime('na')):
    flist.append(f"INC_EOD.{date}")
  if 'HC_EOD' in ftypes and corrections and (avail_time < getTime('na')):
    flist.append(f"HC_EOD.{date}")
  return flist

# SFTP Helpers
# --------------------------------------------------------------------------------------------------

def secmasterSeed():
  sftp_sync('sftp.barra.com','/barradirect/',MSCI_SECMASTER_RAW,ffilter=f"_FULL_WE.{MSCI_SECMASTER_DATE}")
  log.info("Done")

@lru_cache(maxsize=1)
def msciConn():
  return sftp.conn('sftp.barra.com',username='pwdmqvmz',password='h5LtKjDZ+s,9nryO!vX2')

def msciSync(shortname):
  fname = msciFname(shortname)
  if file_exists(fname):
    return
  log.info(f"Syncing file: {fname}")
  sftp.file(msciConn(),f"barradirect/{shortname}",fname)
  if file_missing(fname):
    raise ValueError(f"Missing: {shortname}")

def msciFname(shortname):
  return f"{MSCI_SECMASTER_RAW}/{shortname}"

def parse_msci_issuer_file(file_path: str) -> pd.DataFrame:
    with open(file_path, 'r') as f:
        lines = f.readlines()

    delimiter = '|'
    expected_numrows = None
    columns = []
    data_lines = []

    in_column_section = False
    in_content_section = False
    in_footer_section = False

    for line in lines:
        line = line.strip()

        if not line or line.startswith('#'):
            continue

        # Parse header metadata
        if line.startswith('DELIMITER ='):
            delimiter = line.split('=', 1)[1].strip().strip('"').strip("'")

        # Capture column list
        elif line == '[START-COLUMN-LIST]':
            in_column_section = True
            continue
        elif line == '[END-COLUMN-LIST]':
            in_column_section = False
            continue
        elif in_column_section:
            columns.append(line)

        # Capture content lines
        elif line == '[START-CONTENT]':
            in_content_section = True
            continue
        elif line == '[END-CONTENT]':
            in_content_section = False
            break  # We can stop here
        elif in_content_section:
            data_lines.append(line)

        elif line == '[START-FOOTER]':
            in_footer_section = True
            continue
        elif line == '[END-FOOTER]':
            in_footer_section = False
            continue
        elif in_footer_section and line.startswith('NUMROWS'):
            try:
                expected_numrows = int(line.split('=', 1)[1].strip())
            except ValueError:
                logging.warning(f"NUMROWS format invalid in footer of {file_path}")

    if not data_lines or not columns:
        raise ValueError(f"Invalid file structure or missing metadata in {file_path}")

    df = pd.DataFrame([row.split(delimiter) for row in data_lines], columns=columns)

    df['CREATED_AT'] = datetime.now()

    # Validate row count
    actual_rows = len(df)
    if expected_numrows is not None and actual_rows != expected_numrows:
        logging.warning(f"Row count mismatch in {file_path}: expected {expected_numrows}, got {actual_rows}")

    return df