import argparse
import json
import os
import requests
import pandas as pd
import time
import logging
import base64
from urllib.parse import quote
# from strunner import *
from datetime import datetime, timedelta


# setupEnvironment()


BASE_URL = 'https://api.spacinsider.com/v2'




def read_config_secrets(path=None):
   secret_dir = path or '/jfs/tech1/apps/datait/jg-code/secure/prod'
   secret_file = os.path.join(secret_dir, 'config_secret.json')
   with open(secret_file, 'r') as f:
       return json.load(f)




class SPACInsiderClientBase:


   def __init__(self, args):
       self.args = args
       print("Initializing SPACInsiderClientBase")
       self.config = read_config_secrets()
       print("Loaded config secrets")
       self.client_id = self.config.get('spacinsider_client_id')
       self.client_secret = self.config.get('spacinsider_client_secret')
       self.token = self._get_auth_token()
       self.token_expires_at = getattr(self, 'token_expires_at', None)
       self.headers = {
           'Authorization': f'Bearer {self.token}',
           'Content-Type': 'application/json'
       }
       self.session = requests.Session()
       self.session.headers.update(self.headers)
       print("Initialized API session")


   def _get_auth_token(self):
       """Get OAuth 2.0 access token using client credentials."""
       auth_url = f"{BASE_URL}/auth/token"
      
       credentials = f"{self.client_id}:{self.client_secret}"
       encoded_credentials = base64.urlsafe_b64encode(credentials.encode()).decode()
      
       headers = {
           'Content-Type': 'application/x-www-form-urlencoded',
           'Authorization': f'Basic {encoded_credentials}'
       }
      
       data = {
           'grant_type': 'client_credentials',
           'client_id': self.client_id,
           'client_secret': self.client_secret
       }
      
       try:
           resp = requests.post(auth_url, headers=headers, data=data)
           resp.raise_for_status()
           token_data = resp.json()
          
           if 'expires_in' in token_data:
               self.token_expires_at = time.time() + token_data['expires_in'] - 60
               print(f"Token expires in {token_data['expires_in']} seconds")
          
           return token_data.get('access_token')
       except requests.exceptions.RequestException as e:
           logging.error(f"Failed to get auth token: {str(e)}")
           raise


   def fetch_json_post(self, url, data=None, max_retries=3):
       """Fetch JSON data using POST with retry logic and token refresh."""
       for attempt in range(max_retries):
           try:
               # Always get fresh token to prevent expiration issues
               self.token = self._get_auth_token()
               headers = {
                   'Accept': 'application/json',
                   'Authorization': f'Bearer {self.token}',
                   'Content-Type': 'application/json'
               }
              
               resp = self.session.post(url, headers=headers, json=data)
              
               if resp.status_code == 401:
                   logging.warning("Got 401 Unauthorized, refreshing token...")
                   self.token = self._get_auth_token()
                   headers['Authorization'] = f'Bearer {self.token}'
                   resp = self.session.post(url, headers=headers, json=data)
              
               resp.raise_for_status()
               return resp.json()
           except requests.exceptions.RequestException as e:
               logging.warning(f"Attempt {attempt + 1} failed: {str(e)}")
               if attempt < max_retries - 1:
                   time.sleep(2 ** attempt)
               else:
                   raise


   def save_data(self, data, filename, format='json'):
       os.makedirs(self.args.output_path, exist_ok=True)
      
       base_name, ext = os.path.splitext(filename)
       snapshot_filename = f"{base_name}_{self.args.date}{ext}"
       output_file = os.path.join(self.args.output_path, snapshot_filename)
      
       if format == 'csv':
           if isinstance(data, pd.DataFrame):
               df = data
           else:
               df = pd.DataFrame(data)
           df.to_csv(output_file, index=False)
       else:
           with open(output_file, 'w') as f:
               if isinstance(data, pd.DataFrame):
                   data = data.to_dict('records')
               json.dump(data, f, indent=2)
      
       print(f"Saved {output_file}")
       return output_file


   def check_file_exists(self, filename):
       base_name, ext = os.path.splitext(filename)
       snapshot_filename = f"{base_name}_{self.args.date}{ext}"
       file_path = os.path.join(self.args.output_path, snapshot_filename)
       if os.path.exists(file_path):
           print(f"File {file_path} already exists.")
           return True
       return False


   def get_date_range(self):
       end_date = datetime.strptime(self.args.date, '%Y%m%d').date()
      
       if self.args.backfill_start:
           start_date = datetime.strptime(self.args.backfill_start, '%Y%m%d').date()
       else:
           start_date = end_date - timedelta(days=365)
      
       return start_date, end_date




class SPACDataFetcher(SPACInsiderClientBase):
  
   def run(self):
       filename = 'spacinsider_all_spacs.json'
      
       if not self.args.force and self.check_file_exists(filename):
           print(f"Skipping SPAC data fetch - file exists for {self.args.date}")
           return
      
       print(f"Fetching all SPAC data for {self.args.date}")
      
       try:
           url = f"{BASE_URL}/spac/all"
          
           headers = {
               'Accept': 'application/json',
               'Authorization': f'Bearer {self.token}',
               'Content-Type': 'application/json'
           }
          
           all_spacs = []
           limit = 1000
           offset = 0
           total_fetched = 0
          
           while True:
               data = {
                   "limit": limit,
                   "offset": offset
               }
              
               print(f"Fetching SPACs with offset {offset}, limit {limit}")
              
               # Always get fresh token before making request
               self.token = self._get_auth_token()
               headers['Authorization'] = f'Bearer {self.token}'
              
               resp = self.session.post(url, headers=headers, json=data)
              
               if resp.status_code == 401:
                   logging.warning("Got 401 Unauthorized, refreshing token...")
                   self.token = self._get_auth_token()
                   headers['Authorization'] = f'Bearer {self.token}'
                   resp = self.session.post(url, headers=headers, json=data)
              
               resp.raise_for_status()
               response = resp.json()
              
               if isinstance(response, list):
                   batch_size = len(response)
                   all_spacs.extend(response)
                   total_fetched += batch_size
                   print(f"Fetched {batch_size} SPACs in this batch, total: {total_fetched}")
                  
                   if batch_size < limit:
                       break
                  
                   offset += limit
               elif isinstance(response, dict):
                   if 'data' in response:
                       batch_data = response['data']
                       batch_size = len(batch_data)
                       all_spacs.extend(batch_data)
                       total_fetched += batch_size
                       print(f"Fetched {batch_size} SPACs in this batch, total: {total_fetched}")
                      
                       if 'has_more' in response and not response['has_more']:
                           break
                       elif batch_size < limit:
                           break
                      
                       offset += limit
                   else:
                       all_spacs.append(response)
                       break
               else:
                   logging.warning(f"Unexpected response type: {type(response)}")
                   break
          
           self.save_data(all_spacs, filename)
           print(f"Successfully fetched {len(all_spacs)} SPACs")
          
       except Exception as e:
           logging.error(f"Failed to fetch SPAC data: {str(e)}")
           raise




class SPACRelationshipsFetcher(SPACInsiderClientBase):
   """Fetches relationship data for all SPACs."""
  
   def run(self):
       filename = 'spacinsider_relationships.json'
      
       if not self.args.force and self.check_file_exists(filename):
           print(f"Skipping relationships fetch - file exists for {self.args.date}")
           return
      
       print(f"Fetching SPAC relationships for {self.args.date}")
      
       try:
           print("Fetching all SPACs to extract CIK values...")
           spacs_url = f"{BASE_URL}/spac/all"
          
           all_spacs = []
           limit = 1000
           offset = 0
          
           while True:
               data = {
                   "limit": limit,
                   "offset": offset
               }
              
               response = self.fetch_json_post(spacs_url, data=data)
              
               if isinstance(response, list):
                   batch_size = len(response)
                   all_spacs.extend(response)
                  
                   if batch_size < limit:
                       break
                   offset += limit
               else:
                   break
          
           print(f"Found {len(all_spacs)} SPACs to process for relationships")
          
           relationship_types = [
               'left_lead',
               'underwriter',
               'sponsor',
               'advisor',
               'legal',
               'auditor'
           ]
          
           all_relationships = []
           failed_fetches = []
          
           for idx, spac in enumerate(all_spacs):
               cik = spac.get('cik')
               name = spac.get('name', '')
              
               if not cik:
                   logging.warning(f"No CIK found for SPAC: {name}")
                   continue
              
               print(f"Processing {idx + 1}/{len(all_spacs)}: {name} (CIK: {cik})")
              
               for rel_type in relationship_types:
                   try:
                       url = f"{BASE_URL}/spac/{cik}/underwriter?relationship_type={rel_type}"
                      
                       # Always get fresh token before making request
                       self.token = self._get_auth_token()
                       headers = {
                           'Accept': 'application/json',
                           'Authorization': f'Bearer {self.token}'
                       }
                      
                       resp = self.session.get(url, headers=headers)
                      
                       if resp.status_code == 404:
                           continue
                       elif resp.status_code == 401:
                           self.token = self._get_auth_token()
                           headers['Authorization'] = f'Bearer {self.token}'
                           resp = self.session.get(url, headers=headers)
                      
                       if resp.status_code == 200:
                           rel_data = resp.json()
                          
                           if isinstance(rel_data, list):
                               for item in rel_data:
                                   item['spac_cik'] = cik
                                   item['spac_name'] = name
                                   item['relationship_type'] = rel_type
                                   all_relationships.append(item)
                           elif isinstance(rel_data, dict):
                               rel_data['spac_cik'] = cik
                               rel_data['spac_name'] = name
                               rel_data['relationship_type'] = rel_type
                               all_relationships.append(rel_data)
                              
                   except Exception as e:
                       error_msg = f"Failed to fetch {rel_type} for {name} (CIK: {cik}): {str(e)}"
                       logging.warning(error_msg)
                       failed_fetches.append({'cik': cik, 'name': name, 'type': rel_type, 'error': str(e)})
              
               if idx % 100 == 0 and idx > 0:
                   time.sleep(1)
          
           self.save_data(all_relationships, filename)
           print(f"Successfully fetched {len(all_relationships)} relationships")
          
           if failed_fetches:
               logging.warning(f"Failed to fetch {len(failed_fetches)} relationships")
               self.save_data(failed_fetches, 'failed_relationship_fetches.json')
          
       except Exception as e:
           logging.error(f"Failed to fetch relationship data: {str(e)}")
           raise




class ParticipantsFetcher(SPACInsiderClientBase):
   """Fetches participant data by first getting unique participants and then their SPACs."""
  
   def run(self):
       filename = 'spacinsider_participants.json'
      
       if not self.args.force and self.check_file_exists(filename):
           print(f"Skipping participants fetch - file exists for {self.args.date}")
           return
      
       print(f"Fetching SPAC participants data")
      
       try:
           print("Fetching all SPACs to extract participant names...")
          
           spacs_url = f"{BASE_URL}/spac/all"
           all_spacs = []
           limit = 1000
           offset = 0
          
           while True:
               data = {"limit": limit, "offset": offset}
               response = self.fetch_json_post(spacs_url, data=data)
              
               if isinstance(response, list):
                   batch_size = len(response)
                   all_spacs.extend(response)
                   if batch_size < limit:
                       break
                   offset += limit
               else:
                   break
          
           participant_names = set()
          
           for spac in all_spacs:
               fields_to_check = [
                   'left_lead_uw', 'raw_left_lead_uw',
                   'underwriter_1', 'sponsor', 'sponsor_owner',
                   'ceo', 'raw_ceo', 'chairman', 'raw_chairman',
                   'fpa_investor'
               ]
              
               for field in fields_to_check:
                   value = spac.get(field)
                   if value and isinstance(value, str) and value.strip():
                       participant_names.add(value.strip())
              
               list_fields = [
                   'book_runners_and_lead_managers', 'issuers_counsel',
                   'underwriters_counsel', 'auditor', 'co_ceos', 'co_chairman'
               ]
              
               for field in list_fields:
                   values = spac.get(field, [])
                   if isinstance(values, list):
                       for value in values:
                           if value and isinstance(value, str) and value.strip():
                               participant_names.add(value.strip())
          
           print(f"Found {len(participant_names)} unique participant names")
          
           all_participant_data = []
           failed_participants = []
          
           for idx, participant_name in enumerate(participant_names):
               print(f"Processing participant {idx + 1}/{len(participant_names)}: {participant_name}")
              
               try:
                   # URL encode the participant name
                   encoded_participant_name = quote(participant_name)
                   standardized_url = f"{BASE_URL}/participant/{encoded_participant_name}"
                  
                   # Always get fresh token before making request
                   self.token = self._get_auth_token()
                   headers = {
                       'Accept': 'application/json',
                       'Authorization': f'Bearer {self.token}'
                   }
                  
                   resp = self.session.get(standardized_url, headers=headers)
                  
                   if resp.status_code == 404:
                       logging.warning(f"Participant not found: {participant_name}")
                       continue
                   elif resp.status_code == 401:
                       self.token = self._get_auth_token()
                       headers['Authorization'] = f'Bearer {self.token}'
                       resp = self.session.get(standardized_url, headers=headers)
                  
                   if resp.status_code != 200:
                       logging.warning(f"Failed to get standardized name for {participant_name}: {resp.status_code}")
                       continue
                  
                   standardized_data = resp.json()
                  
                   # Check if response is valid
                   if not standardized_data or not isinstance(standardized_data, dict):
                       logging.warning(f"Invalid response for participant {participant_name}: {standardized_data}")
                       continue
                  
                   standardized_name = standardized_data.get('name', participant_name)
                  
                   # URL encode the standardized name for the SPACs request
                   encoded_standardized_name = quote(standardized_name)
                   spacs_url = f"{BASE_URL}/participant/{encoded_standardized_name}/spacs/all"
                  
                   participant_spacs = []
                   limit = 10000
                   offset = 0
                  
                   while True:
                       data = {
                           "limit": limit,
                           "offset": offset
                       }
                      
                       # Always get fresh token before making request
                       self.token = self._get_auth_token()
                       headers['Authorization'] = f'Bearer {self.token}'
                      
                       resp = self.session.post(spacs_url, headers=headers, json=data)
                      
                       if resp.status_code == 401:
                           self.token = self._get_auth_token()
                           headers['Authorization'] = f'Bearer {self.token}'
                           resp = self.session.post(spacs_url, headers=headers, json=data)
                      
                       if resp.status_code != 200:
                           logging.warning(f"Failed to get SPACs for {standardized_name}: {resp.status_code}")
                           break
                      
                       response = resp.json()
                      
                       if isinstance(response, list):
                           batch_size = len(response)
                           for spac in response:
                               spac['participant_name'] = standardized_name
                               spac['participant_original_name'] = participant_name
                               participant_spacs.append(spac)
                          
                           if batch_size < limit:
                               break
                           offset += limit
                       else:
                           if response:
                               response['participant_name'] = standardized_name
                               response['participant_original_name'] = participant_name
                               participant_spacs.append(response)
                           break
                  
                   all_participant_data.extend(participant_spacs)
                   print(f"Fetched {len(participant_spacs)} SPACs for {standardized_name}")
                  
               except Exception as e:
                   error_msg = f"Failed to process participant {participant_name}: {str(e)}"
                   logging.warning(error_msg)
                   failed_participants.append({'name': participant_name, 'error': str(e)})
              
               if idx % 50 == 0 and idx > 0:
                   time.sleep(1)
          
           self.save_data(all_participant_data, filename)
           print(f"Successfully fetched data for {len(all_participant_data)} participant-SPAC relationships")
          
           if failed_participants:
               logging.warning(f"Failed to process {len(failed_participants)} participants")
               self.save_data(failed_participants, 'failed_participants.json')
          
       except Exception as e:
           logging.error(f"Failed to fetch participants data: {str(e)}")
           raise




class CorporateActionsFetcher(SPACInsiderClientBase):
   """Fetches corporate actions data."""
  
   def run(self):
       print(f"Fetching corporate actions data for {self.args.date}")
      
       try:
           # Use 'all' action type as default
           action_types = ['all']
          
           # Determine date range
           run_date = datetime.strptime(self.args.date, '%Y%m%d').date()
          
           if self.args.backfill:
               start_date, end_date = self.get_date_range()
           else:
               start_date = run_date - timedelta(days=1)
               end_date = run_date
          
           start_str = start_date.strftime('%Y-%m-%d')
           end_str = end_date.strftime('%Y-%m-%d')
          
           print(f"Fetching corporate actions from {start_str} to {end_str}")
          
           files_created = []
           failed_actions = []
          
           for action_type in action_types:
               print(f"Processing corporate action type: {action_type}")
              
               try:
                   filename = 'spacinsider_corporate_actions.json'
                  
                   if not self.args.force and self.check_file_exists(filename):
                       print(f"Skipping {action_type} - file exists")
                       continue
                  
                   url = f"{BASE_URL}/corporate-action/{action_type}"
                  
                   # Collect all data with pagination
                   all_actions = []
                   limit = 1000
                   offset = 0
                  
                   while True:
                       data = {
                           "limit": limit,
                           "offset": offset
                       }
                      
                       print(f"Fetching {action_type} with offset {offset}, limit {limit}")
                      
                       response = self.fetch_json_post(url, data=data)
                      
                       if isinstance(response, list):
                           batch_size = len(response)
                           for item in response:
                               item['action_type'] = action_type
                               item['date_range_start'] = start_str
                               item['date_range_end'] = end_str
                           all_actions.extend(response)
                          
                           print(f"Fetched {batch_size} actions for {action_type}, total: {len(all_actions)}")
                          
                           if batch_size < limit:
                               break
                           offset += limit
                       elif isinstance(response, dict):
                           if 'data' in response:
                               batch_data = response['data']
                               batch_size = len(batch_data)
                               for item in batch_data:
                                   item['action_type'] = action_type
                                   item['date_range_start'] = start_str
                                   item['date_range_end'] = end_str
                               all_actions.extend(batch_data)
                              
                               print(f"Fetched {batch_size} actions for {action_type}, total: {len(all_actions)}")
                              
                               if 'has_more' in response and not response['has_more']:
                                   break
                               elif batch_size < limit:
                                   break
                               offset += limit
                           else:
                               response['action_type'] = action_type
                               response['date_range_start'] = start_str
                               response['date_range_end'] = end_str
                               all_actions.append(response)
                               break
                       else:
                           logging.warning(f"Unexpected response type for {action_type}: {type(response)}")
                           break
                  
                   # Save individual file for each action type
                   self.save_data(all_actions, filename)
                   files_created.append(filename)
                   print(f"Successfully fetched {len(all_actions)} actions for {action_type}")
                  
               except Exception as e:
                   error_msg = f"Failed to fetch corporate actions for {action_type}: {str(e)}"
                   logging.warning(error_msg)
                   failed_actions.append({
                       'action_type': action_type,
                       'error': str(e)
                   })
          
           # Log summary
           print(f"Successfully created {len(files_created)} corporate action files")
           for file in files_created:
               print(f"  - {file}")
          
           # Save failed attempts for debugging
           if failed_actions:
               logging.warning(f"Failed to fetch {len(failed_actions)} corporate action types")
               self.save_data(failed_actions, 'failed_corporate_actions.json')
          
       except Exception as e:
           logging.error(f"Failed to fetch corporate actions data: {str(e)}")
           raise




class LeaguesFetcher(SPACInsiderClientBase):
   """Fetches leagues data and league tables."""
  
   def run(self):
      
       print(f"Fetching leagues data for {self.args.date}")
      
       try:
           # Step 1: Get list of available league types
           url = f"{BASE_URL}/leagues/"
          
           # Always get fresh token before making request
           self.token = self._get_auth_token()
           headers = {
               'Accept': 'application/json',
               'Authorization': f'Bearer {self.token}'
           }
          
           resp = self.session.get(url, headers=headers)
          
           if resp.status_code == 401:
               logging.warning("Got 401 Unauthorized, refreshing token...")
               self.token = self._get_auth_token()
               headers['Authorization'] = f'Bearer {self.token}'
               resp = self.session.get(url, headers=headers)
          
           resp.raise_for_status()
           league_types = resp.json()
          
           print(f"Found league types: {league_types}")
          
           # Step 2: For each league type, fetch the actual league data
           # Determine date range
           run_date = datetime.strptime(self.args.date, '%Y%m%d').date()
          
           if self.args.backfill:
               # For backfill, use the specified range
               start_date, end_date = self.get_date_range()
           else:
               # For daily snapshot: yesterday to today
               start_date = run_date - timedelta(days=1)
               end_date = run_date
          
           start_str = start_date.strftime('%Y-%m-%d')
           end_str = end_date.strftime('%Y-%m-%d')
          
           print(f"Fetching league data from {start_str} to {end_str}")
          
           failed_leagues = []
           files_created = []
          
           # Define endpoint patterns for each league type
           league_endpoint_patterns = {
               'participants': [
                   'underwriter',
                   'sponsor',
                   'legal',
                   'auditor'
               ],
               'despac': [],  # Direct endpoint
               'pipe-investor': [],  # Direct endpoint
               'private-placement': [],  # Direct endpoint
               'serial-sponsor': [],  # Direct endpoint
               '13f': []  # Special case with different params
           }
          
           for league_type in league_types:
               print(f"Processing league type: {league_type}")
              
               # Handle 13f special case
               if league_type == '13f':
                   # Determine quarter based on the run date
                   year = run_date.year
                   month = run_date.month
                  
                   if month <= 3:
                       quarter = 'Q1'
                   elif month <= 6:
                       quarter = 'Q2'
                   elif month <= 9:
                       quarter = 'Q3'
                   else:
                       quarter = 'Q4'
                  
                   reporting_quarter = f"{year}{quarter}"
                   print(f"Fetching 13f data for quarter: {reporting_quarter}")
                  
                   quarters = [reporting_quarter]  # Only fetch current quarter
                  
                   for quarter in quarters:
                       try:
                           filename = f'spacinsider_leagues_{league_type}_{quarter}.json'
                          
                           if not self.args.force and self.check_file_exists(filename):
                               print(f"Skipping {league_type}_{quarter} - file exists")
                               continue
                          
                           url = f"{BASE_URL}/leagues/{league_type}"
                           params = {
                               'limit': 1000,
                               'offset': 0,
                               'reporting_quarter': quarter
                           }
                          
                           print(f"Fetching: {url} for quarter {quarter}")
                          
                           # Always get fresh token before making request
                           self.token = self._get_auth_token()
                           headers['Authorization'] = f'Bearer {self.token}'
                          
                           resp = self.session.get(url, headers=headers, params=params)
                          
                           if resp.status_code == 404:
                               continue
                           elif resp.status_code == 401:
                               self.token = self._get_auth_token()
                               headers['Authorization'] = f'Bearer {self.token}'
                               resp = self.session.get(url, headers=headers, params=params)
                          
                           if resp.status_code == 200:
                               league_data = resp.json()
                              
                               # Enrich data
                               if isinstance(league_data, list):
                                   for item in league_data:
                                       item['league_type'] = league_type
                                       item['reporting_quarter'] = quarter
                               elif isinstance(league_data, dict):
                                   league_data['league_type'] = league_type
                                   league_data['reporting_quarter'] = quarter
                                   league_data = [league_data]  # Convert to list for consistency
                              
                               # Save individual file
                               self.save_data(league_data, filename)
                               files_created.append(filename)
                               print(f"Successfully fetched {league_type} for {quarter}")
                          
                       except Exception as e:
                           error_msg = f"Failed to fetch {league_type} for {quarter}: {str(e)}"
                           logging.warning(error_msg)
                           failed_leagues.append({
                               'league_type': league_type,
                               'quarter': quarter,
                               'error': str(e)
                           })
                   continue
              
               # Handle participants special case (deal_role as path parameter)
               if league_type == 'participants':
                   deal_roles = league_endpoint_patterns['participants']
                  
                   for deal_role in deal_roles:
                       try:
                           filename = f'spacinsider_leagues_{league_type}_{deal_role}.json'
                          
                           if not self.args.force and self.check_file_exists(filename):
                               print(f"Skipping {league_type}_{deal_role} - file exists")
                               continue
                          
                           url = f"{BASE_URL}/leagues/participants/{deal_role}"
                           params = {
                               'limit': 1000,
                               'offset': 0,
                               'start': start_str,
                               'end': end_str
                           }
                          
                           print(f"Fetching: {url}")
                          
                           # Always get fresh token before making request
                           self.token = self._get_auth_token()
                           headers['Authorization'] = f'Bearer {self.token}'
                          
                           resp = self.session.get(url, headers=headers, params=params)
                          
                           if resp.status_code == 404:
                               continue
                           elif resp.status_code == 401:
                               self.token = self._get_auth_token()
                               headers['Authorization'] = f'Bearer {self.token}'
                               resp = self.session.get(url, headers=headers, params=params)
                          
                           if resp.status_code == 200:
                               league_data = resp.json()
                              
                               # Enrich data with league type and deal role info
                               if isinstance(league_data, list):
                                   for item in league_data:
                                       item['league_type'] = league_type
                                       item['deal_role'] = deal_role
                                       item['date_range_start'] = start_str
                                       item['date_range_end'] = end_str
                               elif isinstance(league_data, dict):
                                   league_data['league_type'] = league_type
                                   league_data['deal_role'] = deal_role
                                   league_data['date_range_start'] = start_str
                                   league_data['date_range_end'] = end_str
                                   league_data = [league_data]  # Convert to list for consistency
                              
                               # Save individual file
                               self.save_data(league_data, filename)
                               files_created.append(filename)
                               print(f"Successfully fetched {league_type}/{deal_role}")
                           else:
                               logging.warning(f"Failed to fetch {league_type}/{deal_role}: {resp.status_code}")
                      
                       except Exception as e:
                           error_msg = f"Failed to fetch {league_type}/{deal_role}: {str(e)}"
                           logging.warning(error_msg)
                           failed_leagues.append({
                               'league_type': league_type,
                               'deal_role': deal_role,
                               'error': str(e)
                           })
                   continue
              
               # Get endpoints for other league types
               endpoints = league_endpoint_patterns.get(league_type, [])
              
               # If no specific endpoints, try direct call
               if not endpoints:
                   endpoints = ['']
              
               for endpoint in endpoints:
                   try:
                       endpoint_name = endpoint if endpoint else 'direct'
                       filename = f'spacinsider_leagues_{league_type}_{endpoint_name}.json'
                      
                       if not self.args.force and self.check_file_exists(filename):
                           print(f"Skipping {league_type}_{endpoint_name} - file exists")
                           continue
                      
                       if endpoint:
                           url = f"{BASE_URL}/leagues/{league_type}/{endpoint}"
                       else:
                           url = f"{BASE_URL}/leagues/{league_type}"
                      
                       params = {
                           'limit': 1000,
                           'offset': 0,
                           'start': start_str,
                           'end': end_str
                       }
                      
                       print(f"Fetching: {url}")
                      
                       resp = self.session.get(url, headers=headers, params=params)
                      
                       if resp.status_code == 404:
                           continue
                       elif resp.status_code == 401:
                           self.token = self._get_auth_token()
                           headers['Authorization'] = f'Bearer {self.token}'
                           resp = self.session.get(url, headers=headers, params=params)
                      
                       if resp.status_code == 200:
                           league_data = resp.json()
                          
                           # Enrich data with league type and endpoint info
                           if isinstance(league_data, list):
                               for item in league_data:
                                   item['league_type'] = league_type
                                   item['league_endpoint'] = endpoint_name
                                   item['date_range_start'] = start_str
                                   item['date_range_end'] = end_str
                           elif isinstance(league_data, dict):
                               league_data['league_type'] = league_type
                               league_data['league_endpoint'] = endpoint_name
                               league_data['date_range_start'] = start_str
                               league_data['date_range_end'] = end_str
                               league_data = [league_data]  # Convert to list for consistency
                          
                           # Save individual file
                           self.save_data(league_data, filename)
                           files_created.append(filename)
                           print(f"Successfully fetched {league_type}/{endpoint_name}")
                       else:
                           logging.warning(f"Failed to fetch {league_type}/{endpoint}: {resp.status_code}")
                  
                   except Exception as e:
                       error_msg = f"Failed to fetch {league_type}/{endpoint}: {str(e)}"
                       logging.warning(error_msg)
                       failed_leagues.append({
                           'league_type': league_type,
                           'endpoint': endpoint,
                           'error': str(e)
                       })
          
           # Log summary
           print(f"Successfully created {len(files_created)} league files")
           for file in files_created:
               print(f"  - {file}")
          
           # Save failed attempts for debugging
           if failed_leagues:
               logging.warning(f"Failed to fetch {len(failed_leagues)} league endpoints")
               self.save_data(failed_leagues, 'failed_leagues.json')
          
       except Exception as e:
           logging.error(f"Failed to fetch leagues data: {str(e)}")
           raise




class BackfillManager(SPACInsiderClientBase):
  
   def __init__(self, args, fetchers):
       super().__init__(args)
       self.fetchers = fetchers
       # Only these support date filtering and should run for each date in backfill
       self.date_aware_fetchers = {
           CorporateActionsFetcher,  # Corporate actions support date filtering
           LeaguesFetcher,  # League tables support date filtering
       }
  
   def run(self):
       start_date, end_date = self.get_date_range()
      
       print(f"Starting backfill from {start_date} to {end_date}")
      
       original_date = self.args.date
       current_date = start_date
       failed_dates = []
      
       # Get non-date-aware fetchers (should only run once at the end date)
       non_date_fetchers = [f for f in self.fetchers if f not in self.date_aware_fetchers]
       date_fetchers = [f for f in self.fetchers if f in self.date_aware_fetchers]
      
       # Run date-aware fetchers for each date in the range
       if date_fetchers:
           print("Processing date-aware fetchers for each date in range...")
           while current_date <= end_date:
               date_str = current_date.strftime('%Y%m%d')
               print(f"Processing date-aware fetchers for {date_str}")
              
               self.args.date = date_str
              
               try:
                   for fetcher_class in date_fetchers:
                       fetcher = fetcher_class(self.args)
                       fetcher.run()
                  
                   print(f"Successfully completed date-aware backfill for {date_str}")
              
               except Exception as e:
                   logging.error(f"Failed to backfill {date_str}: {str(e)}")
                   failed_dates.append({'date': date_str, 'error': str(e)})
              
               current_date += timedelta(days=1)
      
       # Run non-date-aware fetchers once at the end date
       if non_date_fetchers:
           print("Running non-date-aware fetchers once at end date...")
           self.args.date = end_date.strftime('%Y%m%d')
           for fetcher_class in non_date_fetchers:
               try:
                   fetcher = fetcher_class(self.args)
                   fetcher.run()
                   print(f"Successfully ran {fetcher_class.__name__}")
               except Exception as e:
                   logging.error(f"Failed to run {fetcher_class.__name__}: {str(e)}")
                   failed_dates.append({'fetcher': fetcher_class.__name__, 'error': str(e)})
      
       if failed_dates:
           logging.warning(f"Backfill completed with {len(failed_dates)} failures:")
           for failure in failed_dates:
               if 'date' in failure:
                   logging.warning(f"  - Date: {failure['date']}, Error: {failure['error']}")
               else:
                   logging.warning(f"  - Fetcher: {failure['fetcher']}, Error: {failure['error']}")
       else:
           print("Backfill completed successfully for all dates")
      
       self.args.date = original_date




def parse_args():
   parser = argparse.ArgumentParser(
       description="Download SPAC Insider data with daily snapshots"
   )
   parser.add_argument(
       '--date', required=True, help='Run date YYYYMMDD'
   )
   parser.add_argument(
       '--output_path', default='.', help='Directory for output files'
   )
   parser.add_argument(
       '--data_type', choices=['all', 'spacs', 'relationships', 'participants', 'leagues', 'corporate-actions'],
       default='all', help='Type of data to fetch'
   )
   parser.add_argument(
       '--backfill', action='store_true', help='Run backfill from start date to current date'
   )
   parser.add_argument(
       '--backfill_start', help='Start date for backfill YYYYMMDD (default: 1 year ago)'
   )
   parser.add_argument(
       '--force', action='store_true', help='Force re-download even if file exists'
   )
   return parser.parse_args()




def main():
   args = parse_args()
  
   fetcher_classes = {
       'spacs': [SPACDataFetcher],
       'relationships': [SPACRelationshipsFetcher],
       'participants': [ParticipantsFetcher],
       'leagues': [LeaguesFetcher],
       'corporate-actions': [CorporateActionsFetcher],
       'all': [SPACDataFetcher, SPACRelationshipsFetcher, ParticipantsFetcher, LeaguesFetcher, CorporateActionsFetcher]
   }
  
   selected_fetchers = fetcher_classes[args.data_type]
  
   if args.backfill:
       backfill_manager = BackfillManager(args, selected_fetchers)
       backfill_manager.run()
   else:
       for fetcher_class in selected_fetchers:
           fetcher = fetcher_class(args)
           fetcher.run()
  
   print(f"Completed SPAC Insider data download for {args.date}")




if __name__ == '__main__':
   main()




