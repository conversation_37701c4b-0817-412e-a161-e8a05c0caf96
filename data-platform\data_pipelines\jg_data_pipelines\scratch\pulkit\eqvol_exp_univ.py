import os
import pandas as pd
import logging
import time
from sqlalchemy import create_engine, text, event
from typing import Optional, Union
from urllib.parse import quote_plus

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PostgresAdaptor:
    def __init__(
        self,
        host: str,
        database: str,
        schema: str,
        user: str,
        password: str,
        port: int = 5432,
        chunksize: int = 10000
    ):
        """
        Initialize the loader with database connection parameters.
        
        Args:
            host: Database host
            database: Database name
            user: Database username
            password: Database password
            port: Database port (default: 5432)
            chunksize: Number of rows to insert in each batch (default: 10000)
        """
        self.schema = schema
        self.chunksize = chunksize
        self.engine = self._create_engine(host, database, user, password, port)
        
    def _create_engine(
        self,
        host: str,
        database: str,
        user: str,
        password: str,
        port: int,
    ) -> create_engine:
        """Create SQLAlchemy engine with connection pooling configuration."""
        encoded_password = quote_plus(password)
        
        connection_string = (
            f'postgresql://{user}:{encoded_password}@{host}:{port}/{database}'
        )
        
        engine = create_engine(
            connection_string,
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=3600
        )
    
        # Set timezone for each new connection
        @event.listens_for(engine, "connect")
        def set_timezone(dbapi_connection, connection_record):
            with dbapi_connection.cursor() as cursor:
                cursor.execute("SET TIME ZONE 'America/New_York';") 
        
        return engine
    
    def execute_query(self, query: Union[str, text], params: Optional[dict] = None) -> Optional[pd.DataFrame]:
        try:
            return pd.read_sql_query(query, self.engine, params=params)
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            return None

def timeit(func):
    def wrapper(*args, **kwargs):
        start = time.perf_counter()
        result = func(*args, **kwargs)
        end = time.perf_counter()
        logger.info(f"{func.__name__} executed in {end - start:.4f} seconds")
        return result
    return wrapper

def _get_opt_eq_quotes_timestamps():
    """
    Fetch the latest timestamps for each option quote from the database.
    
    Returns:
        pd.DataFrame: DataFrame with 'option_quote_id' and 'timestamp'
    """
    query = text("""
        select snap_timestamp from eqvol.get_opt_eq_quotes_timestamps()
    """)
    
    adaptor = PostgresAdaptor(
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )
    
    return adaptor.execute_query(query)


def _get_random_opt_attributes(nos_of_rows: int = 5000):
    query = text("""
        SELECT ticker, call_put, strike_price, expiration_date
        FROM (
            SELECT DISTINCT ticker, call_put, strike_price, TO_CHAR(expiration_date, 'YYYY-MM-DD') AS expiration_date
            FROM eqvol.sr_option_quotes_exp_latest
        ) opt_attrs
        ORDER BY random()
        LIMIT :limit;
    """)

    adaptor = PostgresAdaptor(
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    return adaptor.execute_query(query, params={"limit": nos_of_rows})


@timeit
def get_opt_quotes_for_security(ticker, timestamp_utc=None, tolerance_seconds=300):
    timestamp_utc_str = (
        timestamp_utc.strftime("%Y-%m-%d %H:%M:00")
        if pd.notnull(timestamp_utc)
        else None
    )

    query = text("""
        SELECT * FROM eqvol.get_opt_quotes_for_security(
            :ticker, 
            :timestamp_utc, 
            :tolerance_seconds
        )
    """)

    adaptor = PostgresAdaptor(
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    return adaptor.execute_query(query, params={
        "ticker": ticker,
        "timestamp_utc": timestamp_utc_str,
        "tolerance_seconds": tolerance_seconds,
    })


@timeit
def get_opt_quotes_for_opt_attrs(df_opt_attrs, timestamp_utc=None, tolerance_seconds=300):

    if df_opt_attrs.shape[0] < 1:
        raise ValueError("Input DataFrame must contain at least one row.")
    
    opt_attrs_json = df_opt_attrs.to_json(orient="records", date_format="iso")

    timestamp_utc_str = (
        timestamp_utc.strftime("%Y-%m-%d %H:%M:00")
        if pd.notnull(timestamp_utc)
        else None
    )

    query = text("""
        SELECT * FROM eqvol.get_opt_quotes_for_opt_attrs(
            :opt_attrs_json, 
            :timestamp_utc, 
            :tolerance_seconds
        )
    """)

    adaptor = PostgresAdaptor(
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    return adaptor.execute_query(query, params={
        "opt_attrs_json": opt_attrs_json,
        "timestamp_utc": timestamp_utc_str,
        "tolerance_seconds": tolerance_seconds,
    })

def get_sr_sec_mappings(configured_only=True):
    
    if configured_only:
        query = text("""
           select vs.sr_ticker, 
                vs.symbol,
                vs.assettype,
                vs.symboltype,
                vs.name,
                vs.bbg_comp_ticker,
                vs.bbg_comp_figi,
                vs.bbg_exch_ticker,
                vs.bbg_exch_figi
            from eqvol.v_securities vs join 
            eqvol.ticker_request_config trc on vs.sr_ticker = trc.ticker 
            where trc.is_sr_opt_quote_enabled = true
        """)
    else:
        query = text("""
            select sr_ticker, 
                symbol,
                assettype,
                symboltype,
                name,
                bbg_comp_ticker,
                bbg_comp_figi,
                bbg_exch_ticker,
                bbg_exch_figi
            from eqvol.v_securities vs 
        """)

    adaptor = PostgresAdaptor(
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    return adaptor.execute_query(query)

if __name__ == "__main__":
    # df_sec = get_sr_sec_mappings(configured_only=True)
    # print(df_sec.shape)
    # print(df_sec.head())
    
    # df_opt_surface = get_opt_quotes_for_security("SPXW")
    # print(df_opt_surface.shape)
    # print(df_opt_surface.head())

    df_opt_attrs = _get_random_opt_attributes(5000)

    df_opt_attrs = df_opt_attrs._append({
        "ticker": "ZVZZT",
        "call_put": "C",
        "strike_price": 4500,
        "expiration_date": "2024-01-19"
    }, ignore_index=True)
    
    df_opt_quotes = get_opt_quotes_for_opt_attrs(df_opt_attrs)
    print(df_opt_quotes.head())
    print(df_opt_quotes.shape)
    print(df_opt_quotes[df_opt_quotes["ticker"] == "ZVZZT"])
    

    

