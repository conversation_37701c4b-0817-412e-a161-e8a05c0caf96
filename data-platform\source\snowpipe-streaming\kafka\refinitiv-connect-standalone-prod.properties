bootstrap.servers=b-1.mskprodsecure.vskf0j.c14.kafka.us-east-1.amazonaws.com:9096
rest.port=8300
listeners=http://0.0.0.0:8300
key.converter=org.apache.kafka.connect.storage.StringConverter
value.converter=org.apache.kafka.connect.json.JsonConverter
key.converter.schemas.enable=true
value.converter.schemas.enable=true
offset.storage.file.filename=/tmp/connect.offsets
security.protocol=SASL_SSL
sasl.enabled.protocols=TLSv1.1,TLSv1.2
sasl.mechanism=SCRAM-SHA-512
sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="<USERNAME>" password="<PASSWORD>";
consumer.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required \
    username="svc_datait" \
    password="oULJJVVdD8uk2gXd6BMA";
producer.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required \
    username="svc_datait" \
    password="oULJJVVdD8uk2gXd6BMA";
consumer.security.protocol=SASL_SSL
consumer.sasl.mechanism=SCRAM-SHA-512
consumer.ssl.enabled.protocols=TLSv1.1,TLSv1.2
producer.security.protocol=SASL_SSL
producer.sasl.mechanism=SCRAM-SHA-512
producer.ssl.enabled.protocols=TLSv1.1,TLSv1.2
log4j.logger.org.apache.kafka.common.security=DEBUG