from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.timetables.trigger import CronTriggerTimetable
from airflow.hooks.base import BaseHook
from datetime import timedelta, datetime
import os, sys, pendulum, subprocess, logging, requests
from util.validate import check_for_anomalies

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()

def floor_time_to_nearest_15_minutes_interval(dt, interval_minutes=15):
    """Round a datetime object down to the nearest interval in minutes."""
    discard = timedelta(minutes=dt.minute % interval_minutes, seconds=dt.second, microseconds=dt.microsecond)
    dt -= discard
    return dt

JGDATA_PATH = os.environ.get("JGDATA_PATH")

# Get the current time and round it down to the nearest 15-minute interval
ny_tz = pendulum.timezone("America/New_York")
current_time = datetime.now(ny_tz)
rounded_time = floor_time_to_nearest_15_minutes_interval(current_time)
formatted_time = rounded_time.strftime("%Y-%m-%d %H%M")

def download_and_validate():
    run_spglobal_loans_script()  # try download

    # Fetch the Airflow connection by conn_id
    conn = BaseHook.get_connection("vendor_http")

    # Build full URL using connection details + endpoint
    endpoint = "getJFSFeedAvailabilitySatusDailyDownload/jg-download-spglobal-loans-intraday"
    base_url = f"{conn.host}"
    if conn.port:
        base_url += f":{conn.port}"
    full_url = f"{base_url}/{endpoint}"

    # Set headers (you can also pull these from conn.extra if stored there)
    headers = {"Content-Type": "application/json"}

    # Make the request
    response = requests.get(full_url, headers=headers)

    # Validate response
    try:
        success, message = check_for_anomalies(response)
        if success:
            logging.info(message)
    except Exception as e:
        logging.error("Validation failed: %s", str(e))
        raise e

    return "finished task"

def run_spglobal_loans_script():

    command = (
        f'python3 {JGDATA_PATH}/bin/download.py --dataset spglobal.loans.intraday --date_fmt "{formatted_time}"'
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.warning("Command failed with exit status %d", e.returncode)
        logging.warning("STDOUT: %s", e.stdout)
        logging.warning("STDERR: %s", e.stderr)
        raise

def run_spglobal_loans_postgres_load():

    command = f'PYTHONNOUSERSITE=1 /jfs/tech1/conda/envs/tech1-datait/bin/python3 {JGDATA_PATH}/bin/spglobal_loans_intraday_postgres_load.py --date "{formatted_time}"'
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.warning("Command failed with exit status %d", e.returncode)
        logging.warning("STDOUT: %s", e.stdout)
        logging.warning("STDERR: %s", e.stderr)
        raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2025, 4, 18, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 5,
    "retry_delay": timedelta(minutes=2)
}

dag = DAG(
    dag_id="jg-download-spglobal-loans-intraday",
    description="This DAG runs file download for S&P Loans IntraDay",
    default_args=default_args,
    schedule=CronTriggerTimetable('*/15 * * * 1-5', timezone='America/New_York'),
    tags=["phdata", "spglobal", "loans"],
    catchup=False
)

file_upload_job = PythonOperator(
    task_id="download_and_validate",
    python_callable=download_and_validate,
    execution_timeout=timedelta(minutes=5),
    dag=dag
)

postgres_load_job = PythonOperator(
    task_id="run_spglobal_loans_postgres_load",
    python_callable=run_spglobal_loans_postgres_load,
    execution_timeout=timedelta(minutes=5),
    dag=dag
)

file_upload_job >> postgres_load_job