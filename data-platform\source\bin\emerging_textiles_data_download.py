import argparse
import logging
import os
import pandas as pd
import requests
from strunner import *
setupEnvironment() 

BASE_URL = 'https://emergingtextiles.com/api_get_data.php'
DATASETS = {
   'fibers': 'jainglobal1',
   'yarns': 'jainglobal2'
}


class EmergingTextilesDownloader:
   def __init__(self, date: str, output_path: str):
       self.date = date
       self.output_path = output_path

   def fetch_json(self, dataset_id: str):
       url = f"{BASE_URL}"
       params = {"id": dataset_id}
       headers = {
           "User-Agent": "Mozilla/5.0", # API expected to be a browser-like user agent
           "Accept": "application/json",
           "Connection": "keep-alive"
       }
       resp = requests.get(url, params=params, headers=headers, timeout=30)
       resp.raise_for_status()
       return resp.json()

   def preprocess_data(self, data, dataset_name: str):
       df = pd.DataFrame(data)
      
      # first 6 rows are disposable, remove them
      # and rename columns to 'Date', 'col_1', 'col_2', etc.
      # they are empty rows or weird formated headers
       if dataset_name == 'fibers':
           if len(df) >= 7:
               df.columns = ['Date'] + [f'col_{i}' for i in range(1, len(df.columns))]
               df = df.iloc[6:]
               df.reset_index(drop=True, inplace=True)
        # first 8 rows are disposable, remove them:
        # and rename columns to 'Date', 'col_1', 'col_2', etc.
        # they are empty rows or weird formated headers
       elif dataset_name == 'yarns':
           if len(df) >= 9:
               df.columns = ['Date'] + [f'col_{i}' for i in range(1, len(df.columns))]
               df = df.iloc[8:]
               df.reset_index(drop=True, inplace=True)
      
       return df

   def save_csv(self, df, filename: str):
       os.makedirs(self.output_path, exist_ok=True)
       path = os.path.join(self.output_path, filename)
       df.to_csv(path, index=False)
       logging.info(f"Saved file: {path}")

   def run(self):
       for name, dataset_id in DATASETS.items():
           filename = f"{name}_{self.date}.csv"
           path = os.path.join(self.output_path, filename)
           if os.path.exists(path):
               logging.info(f"File already exists, skipping: {path}")
               continue
           logging.info(f"Downloading {name} with id {dataset_id}")
           data = self.fetch_json(dataset_id)
           df = self.preprocess_data(data, name)
           self.save_csv(df, filename)
       logging.info(f"Run completed for date: {self.date}")

def parse_args():
   parser = argparse.ArgumentParser(description="Download Emerging Textiles datasets")
   parser.add_argument('--date', required=True, help='Run date in YYYYMMDD')
   parser.add_argument('--output_path', default='.', help='Directory for output CSV files')
   return parser.parse_args()

def main():
   args = parse_args()
   logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')
   downloader = EmergingTextilesDownloader(args.date, args.output_path)
   downloader.run()

if __name__ == '__main__':
   main()
