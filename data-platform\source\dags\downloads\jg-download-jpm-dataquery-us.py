from airflow import DAG
from airflow.operators.python import <PERSON>Operator
from datetime import datetime, timedelta, timezone
import os, sys, pendulum, subprocess, logging
from airflow.providers.http.operators.http import HttpOperator
from util.validate import check_for_anomalies
from airflow.timetables.trigger import CronTriggerTimetable

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()
from stcommon.time import *
import stcommon.tools.dates as dates
# importlib.import_module('jgdata.datasets.jpm.dataquery')
JGDATA_PATH = os.environ.get("JGDATA_PATH")

def run_jpm_dataquery_script():
    schedule_type = 'NY_BOD'
    command = (
        f"python3 {JGDATA_PATH}/bin/jpm.dataquery.gl.py --schedule_type {schedule_type}"
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

# def run_dq_check_script():
    
#     # today = datetime.now(timezone.utc)
#     # today_date = today.strftime('%Y%m%d')

#     command = (
#         f'python3 {JGDATA_PATH}/bin/dq_check_runner.py --dataset_name APACST_JPM --application_cd APACST_JPM'
#     )
#     try:
#         logging.info(command)
#         result = subprocess.run(
#             command,
#             shell=True,
#             check=True,
#             capture_output=True,
#             text=True
#         )
#         logging.info("---LOGS---")
#         logging.info("Command output: %s", result.stdout)
#         logging.info("Command error output: %s", result.stderr)
#     except subprocess.CalledProcessError as e:
#         logging.error("Command failed with exit status %d", e.returncode)
#         logging.error("STDOUT: %s", e.stdout)
#         logging.error("STDERR: %s", e.stderr)
#         raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 10, 8, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id="jg-etl-JPM-Dataquery",
    description="This DAG runs ETL for JPM Dataquery",
    default_args=default_args,
    # schedule_interval='0 8-12 * * 1-6',
    schedule=CronTriggerTimetable('0 8 * * 1-6', timezone="America/New_York"),
    tags=["jgdata","JPM", "APACST"],
    catchup=True
)

etl_job = PythonOperator(
    task_id="jg-etl-JPM-Dataquery",
    python_callable=run_jpm_dataquery_script,
    dag=dag
)

validation_job = HttpOperator(
    task_id="call-daily-data-validation-api",
    http_conn_id="http_default_166",
    endpoint=f"get_daily_data_validation_rule_based/?dataset_id=JPMDataQueryNA1200",
    method="GET",
    headers={"Content-Type": "application/json"},
    response_check=check_for_anomalies,
    extra_options={"check_response": True},
    log_response=True,
    dag=dag
)

# dq_job = PythonOperator(
#     task_id="jg-etl-JPM-Dataquery-dq",
#     python_callable=run_dq_check_script,
#     execution_timeout=timedelta(minutes=30),
#     dag=dag
# )

etl_job >> validation_job
 