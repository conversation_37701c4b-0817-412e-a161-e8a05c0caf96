CREATE OR R<PERSON>LACE VIEW COMMOD.AGRI_BRIDGETON.COT_LOTS AS(
SELECT
	DATE,
	MARKET,
	CASE
		WHEN T_LONG = 'No Data' THEN NULL
		ELSE TRY_CAST(T_LONG AS NUMBER(38, 2))
	END AS T_LONG,
	CASE
		WHEN T_SHORT = 'No Data' THEN NULL
		ELSE TRY_CAST(T_SHORT AS NUMBER(38, 2))
	END AS T_SHORT,
	CASE
		WHEN TRADERS_WW_CHG = 'No Data' THEN NULL
		ELSE TRY_CAST(TRADERS_WW_CHG AS NUMBER(38, 2))
	END AS TRADERS_WW_CHG,
	CASE
		WHEN NET_POS = 'No Data' THEN NULL
		ELSE TRY_CAST(NET_POS AS NUMBER(38, 2))
	END AS NET_POS,
	CASE
		WHEN NET_POS_WW_CHG = 'No Data' THEN NULL
		ELSE TRY_CAST(NET_POS_WW_CHG AS NUMBER(38, 2))
	END AS NET_POS_WW_CHG,
	CASE
		WHEN GROSS_LONG = 'No Data' THEN NULL
		ELSE TRY_CAST(GROSS_LONG AS NUMBER(38, 10))
	END AS GROSS_LONG,
	CASE
		WHEN GROSS_SHORT = 'No Data' THEN NULL
		ELSE TRY_CAST(GROSS_SHORT AS NUMBER(38, 2))
	END AS GROSS_SHORT,
	CASE
		WHEN TF_CTA_NET_POS = 'No Data' THEN NULL
		ELSE TRY_CAST(TF_CTA_NET_POS AS NUMBER(38, 2))
	END AS TF_CTA_NET_POS,
	CASE
		WHEN TF_CTA_WW_CHG = 'No Data' THEN NULL
		ELSE TRY_CAST(TF_CTA_WW_CHG AS NUMBER(38, 2))
	END AS TF_CTA_WW_CHG,
	CASE
		WHEN TF_PER_STRAT_CHG = 'No Data' THEN NULL
		ELSE TRY_CAST(TF_PER_STRAT_CHG AS NUMBER(38, 2))
	END AS TF_PER_STRAT_CHG,
	CASE
		WHEN TF_CTA_GROSS_LONG = 'No Data' THEN NULL
		ELSE TRY_CAST(TF_CTA_GROSS_LONG AS NUMBER(38, 2))
	END AS TF_CTA_GROSS_LONG,
	CASE
		WHEN TF_CTA_GROSS_SHORT = 'No Data' THEN NULL
		ELSE TRY_CAST(TF_CTA_GROSS_SHORT AS NUMBER(38, 2))
	END AS TF_CTA_GROSS_SHORT,
	CASE
		WHEN MV_CTA_NET_POS = 'No Data' THEN NULL
		ELSE TRY_CAST(MV_CTA_NET_POS AS NUMBER(38, 10))
	END AS MV_CTA_NET_POS,
	CASE
		WHEN MV_CTA_WW_CHG = 'No Data' THEN NULL
		ELSE TRY_CAST(MV_CTA_WW_CHG AS NUMBER(38, 2))
	END AS MV_CTA_WW_CHG,
	CASE
		WHEN MV_PER_STRAT_CHG = 'No Data' THEN NULL
		ELSE TRY_CAST(MV_PER_STRAT_CHG AS NUMBER(38, 2))
	END AS MV_PER_STRAT_CHG,
	CASE
		WHEN MV_CTA_GROSS_LONG = 'No Data' THEN NULL
		ELSE TRY_CAST(MV_CTA_GROSS_LONG AS NUMBER(38, 2))
	END AS MV_CTA_GROSS_LONG,
	CASE
		WHEN MV_CTA_GROSS_SHORT = 'No Data' THEN NULL
		ELSE TRY_CAST(MV_CTA_GROSS_SHORT AS NUMBER(38, 10))
	END AS MV_CTA_GROSS_SHORT,
	CASE
		WHEN MR_CTA_NET_POS = 'No Data' THEN NULL
		ELSE TRY_CAST(MR_CTA_NET_POS AS NUMBER(38, 10))
	END AS MR_CTA_NET_POS,
	CASE
		WHEN MR_CTA_WW_CHG = 'No Data' THEN NULL
		ELSE TRY_CAST(MR_CTA_WW_CHG AS NUMBER(38, 2))
	END AS MR_CTA_WW_CHG,
	CASE
		WHEN MR_PER_STRAT_CHG = 'No Data' THEN NULL
		ELSE TRY_CAST(MR_PER_STRAT_CHG AS NUMBER(38, 2))
	END AS MR_PER_STRAT_CHG,
	CASE
		WHEN MR_CTA_GROSS_LONG = 'No Data' THEN NULL
		ELSE TRY_CAST(MR_CTA_GROSS_LONG AS NUMBER(38, 10))
	END AS MR_CTA_GROSS_LONG,
	CASE
		WHEN MR_CTA_GROSS_SHORT = 'No Data' THEN NULL
		ELSE TRY_CAST(MR_CTA_GROSS_SHORT AS NUMBER(38, 2))
	END AS MR_CTA_GROSS_SHORT,
	CASE
		WHEN OTHER_ALGO_DISC_NET_POS = 'No Data' THEN NULL
		ELSE TRY_CAST(OTHER_ALGO_DISC_NET_POS AS NUMBER(38, 10))
	END AS OTHER_ALGO_DISC_NET_POS,
	CASE
		WHEN OTHER_ALGO_DISC_WW_CHG = 'No Data' THEN NULL
		ELSE TRY_CAST(OTHER_ALGO_DISC_WW_CHG AS NUMBER(38, 2))
	END AS OTHER_ALGO_DISC_WW_CHG,
	CASE
		WHEN COMMODITY_INDEX_NET_POS = 'No Data' THEN NULL
		ELSE TRY_CAST(COMMODITY_INDEX_NET_POS AS NUMBER(38, 2))
	END AS COMMODITY_INDEX_NET_POS,
	CASE
		WHEN COMMODITY_INDEX_WW_CHG = 'No Data' THEN NULL
		ELSE TRY_CAST(COMMODITY_INDEX_WW_CHG AS NUMBER(38, 2))
	END AS COMMODITY_INDEX_WW_CHG,
	SYMBOL,
	CASE
		WHEN OI = 'No Data' THEN NULL
		ELSE TRY_CAST(OI AS NUMBER(38, 2))
	END AS OI,
	CASE
		WHEN TOTAL_OI = 'No Data' THEN NULL
		ELSE TRY_CAST(T_LONG AS NUMBER(38, 2))
	END AS TOTAL_OI,
	NUMERIC_DELIVERY_MONTH,
	CASE
		WHEN TF_PER_OI = 'No Data' THEN NULL
		ELSE TRY_CAST(TF_PER_OI AS NUMBER(38, 16))
	END AS TF_PER_OI,
	CASE
		WHEN MV_PER_OI = 'No Data' THEN NULL
		ELSE TRY_CAST(MV_PER_OI AS NUMBER(38, 16))
	END AS MV_PER_OI,
	CASE
		WHEN MR_PER_OI = 'No Data' THEN NULL
		ELSE TRY_CAST(MR_PER_OI AS NUMBER(38, 16))
	END AS MR_PER_OI,
	CASE
		WHEN OTHER_ALGO_DISC_OI = 'No Data' THEN NULL
		ELSE TRY_CAST(OTHER_ALGO_DISC_OI AS NUMBER(38, 16))
	END AS OTHER_ALGO_DISC_OI,
	CASE
		WHEN COMMODITY_INDEX_PER_OI = 'No Data' THEN NULL
		ELSE TRY_CAST(COMMODITY_INDEX_PER_OI AS NUMBER(38, 16))
	END AS COMMODITY_INDEX_PER_OI,
	CASE
		WHEN TF_PER_TOTAL_OI = 'No Data' THEN NULL
		ELSE TRY_CAST(TF_PER_TOTAL_OI AS NUMBER(38, 16))
	END AS TF_PER_TOTAL_OI,
	CASE
		WHEN MV_PER_TOTAL_OI = 'No Data' THEN NULL
		ELSE TRY_CAST(MV_PER_TOTAL_OI AS NUMBER(38, 16))
	END AS MV_PER_TOTAL_OI,
	CASE
		WHEN OTHER_ALGO_DISC_PER_TOTAL_OI = 'No Data' THEN NULL
		ELSE TRY_CAST(OTHER_ALGO_DISC_PER_TOTAL_OI AS NUMBER(38, 16))
	END AS OTHER_ALGO_DISC_PER_TOTAL_OI,
	CASE
		WHEN COMMODITY_INDEX_PER_TOTAL_OI = 'No Data' THEN NULL
		ELSE TRY_CAST(COMMODITY_INDEX_PER_TOTAL_OI AS NUMBER(38, 16))
	END AS COMMODITY_INDEX_PER_TOTAL_OI,
	FILE_NAME,
	TIME_STAMP
FROM
	VENDOR_RAW.USDA_COMMOD.COT_LOTS_RAW);