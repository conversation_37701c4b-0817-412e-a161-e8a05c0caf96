CREATE OR <PERSON><PERSON><PERSON>CE VIEW COMMOD.AGRI_USDA.ESR_EXPORTS_LATEST(
	COMMODITY_CODE,
	COUNTRY_CODE,
	WEEKLY_EXPORTS,
	ACCUMULATED_EXPORTS,
	OUTSTANDING_SALES,
	G<PERSON><PERSON>_NEW_SALES,
	CURRENT_MY_NET_SALES,
	CURRENT_MY_TOTAL_COMMITMENT,
	NEXT_MY_OUTSTANDING_SALES,
	NEXT_MY_NET_SALES,
	UNIT_ID,
	WEEK_ENDING_DATE,
    MARKET_YEAR,
	FILE_NAME,
	TIME_STAMP
) AS
SELECT
	*
FROM
	VENDOR_RAW.USDA_COMMOD.ESR_EXPORTS_RAW eer
WHERE
	TIME_STAMP = (
	SELECT
		MAX(TIME_STAMP)
	FROM
		VENDOR_RAW.USDA_COMMOD.ESR_EXPORTS_RAW eer2
	WHERE
		eer.COMMODITY_CODE = eer2.COMMODITY_CODE
		AND eer.COUNTRY_CODE = eer2.COUNTRY_CODE
		AND eer.WEEK_ENDING_DATE = eer2.WEEK_ENDING_DATE);
