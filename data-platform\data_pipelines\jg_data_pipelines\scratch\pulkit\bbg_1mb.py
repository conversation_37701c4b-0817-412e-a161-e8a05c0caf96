import os
import shutil
import tempfile
import logging
import pandas as pd
from utils.compress_tools.recursive_uncompressor import RecursiveUncompressor

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# fut_th_file_path = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_1min_bars/responses/dp_fth_bid_2504070020.out.tar"
# fut_th_file_path = "/jfs/tech1_share/pulkit.vora/bbg_per_security/index_intraday/responses/dp_yz_1mb_trd_2024.out.tar"
fut_th_file_path = "/jfs/tech1_share/pulkit.vora/bbg_per_security/eqvol/spx_es_ste_1mb/responses/dp_UBPTMOMO_2025.out.tar"
final = "/jfs/tech1_share/pulkit.vora/for_connor/spx_es_ste_1mb/"


_columns = ["SECURITY", "TH_BAR_TIME", "TH_BAR_TYPE", "TH_BAR_OPEN", "TH_BAR_HIGH", "TH_BAR_LOW", "TH_BAR_CLOSE", "TH_BAR_VOLUME", "TH_BAR_VWAP", "TH_BAR_TICK_COUNT", "TH_BAR_OPEN_TIME", "TH_BAR_HIGH_TIME", "TH_BAR_LOW_TIME", "TH_BAR_CLOSE_TIME", "TH_BAR_SNAP", "TH_BAR_SNAP_TIME"]

_core_data_columns = ["TH_BAR_OPEN", "TH_BAR_HIGH", "TH_BAR_LOW", "TH_BAR_CLOSE", "TH_BAR_SNAP"]
_timestamp_cols_dtypes = {"TH_BAR_OPEN_TIME": str, "TH_BAR_HIGH_TIME": str, "TH_BAR_LOW_TIME": str, "TH_BAR_CLOSE_TIME": str, "TH_BAR_SNAP_TIME": str}

structure = {
        "*.out.tar": {"*.csv.gz": ["*"]}
    }

with tempfile.TemporaryDirectory() as temp_dir:
    root_folder = os.path.join(temp_dir, "root")
    if not os.path.exists(root_folder):
        os.makedirs(root_folder)
    shutil.copy(fut_th_file_path, root_folder)
    recursive_uncompressor = RecursiveUncompressor(root_folder, structure)
    for flat_file in recursive_uncompressor.run_generator(temp_dir):
        base_file_name=os.path.basename(flat_file)
        if base_file_name.endswith("UBPTMOMO Index_ohlc_1_1.csv"):
            # Assuming the CSV file is compressed with gzip

            # df_fut_1mb = pd.read_csv(flat_file, dtype=_timestamp_cols_dtypes)
            # df_fut_1mb = df_fut_1mb[_columns]
            # print(df_fut_1mb.dtypes)
            # print(df_fut_1mb.head())
            # print(df_fut_1mb.shape)

            shutil.copy(flat_file, final)
            
            
        