from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.providers.ssh.operators.ssh import SSHOperator
from airflow.providers.ssh.hooks.ssh import SSHHook
from datetime import datetime, timedelta, date
import os, sys, pendulum, subprocess, logging, pytz
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from airflow.timetables.trigger import CronTriggerTimetable
from strunner import *
from airflow.hooks.base import BaseHook

setupEnvironment()

today = datetime.now(tz=pytz.timezone('America/New_York'))
date_pattern = today.strftime('%Y%m%d')
JGDATA_PATH = os.environ.get("JGDATA_PATH")

dag_id = os.path.basename(__file__).replace(".py", "")

def get_jg_env():
    env_value = os.getenv('jg_env', 'prod').lower()
    if env_value not in ['prod', 'uat']:
        raise ValueError(f"Invalid jg_env value: {env_value}. Allowed values are 'prod' or 'uat'.")
    return env_value

env = get_jg_env()

source='usda'
dataset_ref= 'reference_new'
jsvc_path = '/opt/jsvc-datait'
source_path = f'{jsvc_path}/prod/JG-DATA-PLATFORM/source'
python_file_path = f'{source_path}/bin'
command_pipeline_rawdata_ref = f'--dataset {source}.{dataset_ref} --date {date_pattern}'
command_copy_ref = f'--dataset {source}.{dataset_ref} --date {date_pattern} --keypair'

dataset_commod = 'commod_new'
command_pipeline_rawdata_commod = f'--dataset {source}.{dataset_commod} --date {date_pattern}'
command_copy_commod = f'--dataset {source}.{dataset_commod} --date {date_pattern} --keypair'

conn = BaseHook.get_connection("dbt_conn_id")
key_file = conn.extra_dejson.get("path")


def download_reference():
    command = (
        f"python3 {JGDATA_PATH}/bin/usda_data_download.py --date {date_pattern}"
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise


def download_commodity_data():
    command = (
        f"python3 {JGDATA_PATH}/bin/url_expansion.py --date {date_pattern} --source usda"
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 8, 3, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id='jgetl-agri-usda-load',
    default_args=default_args,
    description='This DAG downloads the daily files for USDA Agri API',
    schedule=CronTriggerTimetable('15 8,12 * * *', timezone="America/New_York"),
    tags=["jgdata", "usda-agri"],
    catchup=False,
)

download_ref = PythonOperator(
    task_id="jg-raw-usda_agri-ref",
    python_callable=download_reference,
    dag=dag
)

download_commod =  PythonOperator(
    task_id="jg-raw-usda_agri-commod",
    python_callable=download_commodity_data,
    dag=dag
)

ssh_hook = SSHHook(
    remote_host='*************',
    username='jsvc-datait',
    key_file=key_file,
    port=22
)

run_ref_rawdata_command = SSHOperator(
    task_id='run_raw_data_command_ref',
    ssh_hook=ssh_hook,
    command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_rawdata.py {command_pipeline_rawdata_ref}',
    do_xcom_push=True
)


run_ref_sf_copy_command = SSHOperator(
    task_id='run_pipeline_sf_copy_command_ref',
    ssh_hook=ssh_hook,
    command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_sf_copy.py {command_copy_ref}',
    do_xcom_push=True
)

run_commod_rawdata_command = SSHOperator(
    task_id='run_raw_data_command_commod',
    ssh_hook=ssh_hook,
    command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_rawdata.py {command_pipeline_rawdata_commod}',
    do_xcom_push=True
)


run_commod_sf_copy_command = SSHOperator(
    task_id='run_pipeline_sf_copy_command_commod',
    ssh_hook=ssh_hook,
    command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_sf_copy.py {command_copy_commod}',
    do_xcom_push=True
)



download_ref >> run_ref_rawdata_command >> run_ref_sf_copy_command >>  download_commod >>    run_commod_rawdata_command >> run_commod_sf_copy_command 
