import os
import pandas as pd

from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.postgres.adaptor import PostgresAdaptor

if __name__ == "__main__":
    pg_adaptor = PostgresAdaptor(
        # host="tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com",
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    df_etfs = pg_adaptor.execute_query(
        """select distinct bbg_full_ticker from eqvol.sr_security_ref""",
    )
    etf_securities = df_etfs["bbg_full_ticker"].tolist()

    if 'VXX US Equity' not in etf_securities:
        etf_securities.append('VXX US Equity')

    if "SHY US Equity" not in etf_securities:
        etf_securities.append("SHY US Equity")

    if 'VIX Index' not in etf_securities:
        etf_securities.append('VIX Index')

    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )

    df_futures = sf_adaptor.read_data(
        "BBGH_FUTURES",
        """ 
            WITH CTE_RANKED_FUTURES AS (
            select ID_BB_GLOBAL_COMPANY, BBG_FULL_TICKER, LAST_TRADEABLE_DT, RANK() OVER(PARTITION by ID_BB_GLOBAL_COMPANY ORDER BY LAST_TRADEABLE_DT) AS DEPTH from BLOOMBERG.BBGH_FUTURES.VW_FUTURE_REF where id_bb_global_company in ('BBG001GSVJ48','BBG001GSVKW4','BBG001GDY557','BBG00H3RCVM3','BBG001GTFFK4','BBG001GTFKF9','BBG001GSGQL0','BBG001GCNG02','BBG001GFDRT1'           ,'BBG001H7GDH9','BBG001G9WHH9','BBG001H50248','BBG001GCNNL4','BBG00Y3YT2Y4','BBG00W2PJBC3','BBG001GT10D0','BBG00LB9NHR9','BBG001FN0WL6','BBG001GS4JP5','BBG001GSGPZ7','BBG001G9LVR9','BBG001GSW2Y1','BBG001H7G229','BBG001GT0T57','BBG001G9WHG0','BBG001H00WB5','BBG00BFN27R1', 'BBG001GNNGJ8', 'BBG001GDS518', 'BBG001GT7SB5', 'BBG001GRCPQ3', 'BBG00B6BBD85','BBG001FGTSN7','BBG001GRCQ81','BBG001GT1KF4','BBG001GSVMK3','BBG001GSG2H8','BBG004DDYNB8','BBG001GRCPR2', 'BBG00XQ3XZ80', 'BBG00KX8HG34', 'BBG001GSGR01') AND LAST_TRADEABLE_DT >= CURRENT_DATE 
)
            select DISTINCT BBG_FULL_TICKER from CTE_RANKED_FUTURES
            where DEPTH <= 2 and ID_BB_GLOBAL_COMPANY not in ('BBG001GRCPQ3')
        union 
            select DISTINCT ref.BBG_FULL_TICKER from BLOOMBERG.BBGH_FUTURES.FUTURE_GENERIC_REF gen_ref join 
                        BLOOMBERG.BBGH_FUTURES.VW_FUTURE_REF ref on ref.id_bb_global = gen_ref.id_bb_global
                        where gen_ref.date in (SELECT MAX(DATE) FROM BLOOMBERG.BBGH_FUTURES.FUTURE_GENERIC_REF) and ref.id_bb_global_company in ('BBG001GSVJ48','BBG001GSVKW4','BBG001GDY557','BBG00H3RCVM3','BBG001GTFFK4','BBG001GTFKF9','BBG001GSGQL0','BBG001GCNG02','BBG001GFDRT1'
            ,'BBG001H7GDH9','BBG001G9WHH9','BBG001H50248','BBG001GCNNL4','BBG00Y3YT2Y4','BBG00W2PJBC3','BBG001GT10D0','BBG00LB9NHR9','BBG001FN0WL6','BBG001GS4JP5','BBG001GSGPZ7','BBG001G9LVR9','BBG001GSW2Y1','BBG001H7G229','BBG001GT0T57','BBG001G9WHG0','BBG001H00WB5','BBG00BFN27R1', 'BBG001GNNGJ8', 'BBG001GDS518', 'BBG001GT7SB5', 'BBG00B6BBD85','BBG001FGTSN7','BBG001GRCQ81','BBG001GT1KF4','BBG001GSVMK3','BBG001GSG2H8','BBG004DDYNB8','BBG001GRCPR2', 'BBG00XQ3XZ80', 'BBG001GSGR01')
        union 
            select DISTINCT BBG_FULL_TICKER from CTE_RANKED_FUTURES
            where DEPTH <= 5 and ID_BB_GLOBAL_COMPANY in ('BBG001GRCPQ3')
        union 
            select DISTINCT BBG_FULL_TICKER from CTE_RANKED_FUTURES
            where DEPTH > 2 and ID_BB_GLOBAL_COMPANY in ('BBG001H7GDH9')
        union 
            select DISTINCT BBG_FULL_TICKER from CTE_RANKED_FUTURES
            where DEPTH > 2 and DEPTH <= 20 and ID_BB_GLOBAL_COMPANY in ('BBG00KX8HG34')
        """
    )

    futures = df_futures["BBG_FULL_TICKER"].tolist()

    currencies = ['DXY Curncy', 'USDCHF Curncy', 'CHFUSD Curncy', 'NZDUSD Curncy', 'CCN+1M BGN Curncy', 'USDGBP Curncy', 'USDKRW Curncy', 'USDCAD Curncy', 'GBPUSD Curncy', 'USDINR Curncy', 'USDCNH Curncy', 'KWN+1M BGN Curncy', 'TWDUSD Curncy', 'CNH+1M BGN Curncy', 'USDNOK Curncy', 'USDBRL Curncy', 'USDMXN Curncy', 'NTN+1M BGN Curncy', 'IRN+1M BGN Curncy', 'HKDUSD Curncy', 'USDZAR Curncy', 'JPYUSD Curncy', 'SGDUSD Curncy', 'KRWUSD Curncy', 'AUDUSD Curncy', 'USDEUR Curncy', 'CADUSD Curncy', 'EURUSD Curncy', 'USDSEK Curncy', 'USDJPY Curncy']

    generic_futures = ["FXYA Index", "KMSA Index"]

    all_securities = etf_securities + futures + currencies + generic_futures

    df_data = pd.DataFrame(all_securities, columns=["IDENTIFIER"])
    df_data["FIELD"] = "PX_LAST"
    df_data["SNAP_TYPE"] = "BPIPE-BAR"
    df_data["SNAP_TIME"] = "NA"
    df_data["DAYS_OFFSET"] = 0
    df_data["RUN_TYPE"] = "INTRADAY"
    df_data["IS_ACTIVE"] = True

    sf_adaptor = SnowflakeAdaptor(
        database="DATA_PLATFORM_CORE", 
        warehouse="TEST", 
        role="FR_DATA_PLATFORM")
    
    sf_adaptor.execute_query("CONFIG", f"delete from DATA_PLATFORM_CORE.CONFIG.BLOOMBERG_SNAP_CONFIG WHERE SNAP_TYPE = 'BPIPE-BAR'")
    sf_adaptor.write_pandas_dataframe("CONFIG", df_data, "BLOOMBERG_SNAP_CONFIG")

    