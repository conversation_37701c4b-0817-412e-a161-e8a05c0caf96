from jgdata.datasets.spglobal import *

SCHEMA = SchemaRegistry.register('spglobal.markit_mk4',{
  'sf_im_early':{
    'date':primarykey(np.datetime64),
    'availDate':primarykey(np.datetime64),
    'datadate':primarykey(np.datetime64),
    'sedol':primarykey(str),
    'dxlid':primarykey(str),
    'isin':str,
    'cusip':str,
    'quick':str,
    'instrumentname':str,
    'marketarea':str,
    'valueonloan':np.float64,
    'quantityonloan':np.float64,
    'lendablevalue':np.float64,
    'lendablequantity':np.float64,
    'lendervalueonloan':np.float64,
    'lenderquantityonloan':np.float64,
    'utilisation':np.float64,
    'averagetenure':np.float64,
    'transactioncount':np.float64,
    'activelendablevalue':np.float64,
    'activelendablequantity':np.float64,
    'activeavailablevalue':np.float64,
    'activeavailablequantity':np.float64,
    'activeutilisation':np.float64,
    'activeutilisationbyquantity':np.float64,
    'utilisationbyquantity':np.float64,
    'dcbs':np.float64,
    'dns':np.float64,
    'dips':np.float64,
    'dimv':np.float64,
    'dps':np.float64,
    'dss':np.float64,
    'lenderconcentration':np.float64,
    'lendermarketshare1':np.float64,
    'lendermarketshare2':np.float64,
    'inventoryconcentration':np.float64,
    'inventorymarketshare1':np.float64,
    'inventorymarketshare2':np.float64,
    'borrowerconcentration':np.float64,
    'borrowermarketshare1':np.float64,
    'borrowermarketshare2':np.float64,
    'shortloanquantity':np.float64,
    'shortloanvalue':np.float64,
    'indicativefee':np.float64,
    'indicativerebate':np.float64,
    'bbgid':str,
    'bb_ticker':str,
    'available_quantity_stability':np.float64,
    'available_value_stability':np.float64,
    'lendable_quantity_stability':np.float64,
    'lendable_value_stability':np.float64,
    'lender_quantity_on_loan_stability':np.float64,
    'lender_value_on_loan_stability':np.float64,
    'indicativefee1day':np.float64,
    'indicativefee7day':np.float64,
    'indicativerebate1day':np.float64,
    'indicativerebate7day':np.float64,
    'nextCorporateActionEvent': str,
    'nextCorporateActionEventDate': np.datetime64,
    'nextCorporateActionEventDateType': str,
    'nextCorpActionEventMtgDate': str,
    'shortSqueezeScore': np.float64,
    'daysToCover': np.float64,
    'daysToCoverByQuantity': np.float64,
    'shortLoanDaysToCover': np.float64,
    'shortLoanDaysToCoverByQuantity': np.float64,
    'pctFreeFloatQtyOnLoan': np.float64,
    'pctIssueValueOnLoan': np.float64,
    'shortLoanQtyAsPctOfFreeFltQty': np.float64,
    'pctSharesOutstandingOnLoan': np.float64,
    'shortLoanQtyAsPctOfSharesOut': np.float64,
    'pctMarketCapOnLoan': np.float64,
    'shortLoanValueAsPctOfMarketCap': np.float64,
    'newLoanValue': np.float64,
    'newLoanQty': np.float64,
    'activeLendingAgents': np.float64,
    'inactiveLendingAgents': np.float64,
    'primeBrokers': np.float64,
    'saf':np.float64,
    'sar':np.float64,
  },


  'sf_im_final':{
    'date':primarykey(np.datetime64),
    'availDate':primarykey(np.datetime64),
    'datadate':primarykey(np.datetime64),
    'sedol':primarykey(str),
    'dxlid':primarykey(str),
    'isin':str,
    'cusip':str,
    'quick':str,
    'instrumentname':str,
    'marketarea':str,
    'valueonloan':np.float64,
    'quantityonloan':np.float64,
    'lendablevalue':np.float64,
    'lendablequantity':np.float64,
    'lendervalueonloan':np.float64,
    'lenderquantityonloan':np.float64,
    'utilisation':np.float64,
    'averagetenure':np.float64,
    'transactioncount':np.float64,
    'activelendablevalue':np.float64,
    'activelendablequantity':np.float64,
    'activeavailablevalue':np.float64,
    'activeavailablequantity':np.float64,
    'activeutilisation':np.float64,
    'activeutilisationbyquantity':np.float64,
    'utilisationbyquantity':np.float64,
    'dcbs':np.float64,
    'dns':np.float64,
    'dips':np.float64,
    'dimv':np.float64,
    'dps':np.float64,
    'dss':np.float64,
    'lenderconcentration':np.float64,
    'lendermarketshare1':np.float64,
    'lendermarketshare2':np.float64,
    'inventoryconcentration':np.float64,
    'inventorymarketshare1':np.float64,
    'inventorymarketshare2':np.float64,
    'borrowerconcentration':np.float64,
    'borrowermarketshare1':np.float64,
    'borrowermarketshare2':np.float64,
    'shortloanquantity':np.float64,
    'shortloanvalue':np.float64,
    'indicativefee':np.float64,
    'indicativerebate':np.float64,
    'bbgid':str,
    'bb_ticker':str,
    'available_quantity_stability':np.float64,
    'available_value_stability':np.float64,
    'lendable_quantity_stability':np.float64,
    'lendable_value_stability':np.float64,
    'lender_quantity_on_loan_stability':np.float64,
    'lender_value_on_loan_stability':np.float64,
    'indicativefee1day':np.float64,
    'indicativefee7day':np.float64,
    'indicativerebate1day':np.float64,
    'indicativerebate7day':np.float64,
    'next_corporate_action_event': str,
    'next_corporate_action_event_date': np.datetime64,
    'next_corporate_action_event_date_type': str,
    'next_corporate_action_event_meeting_date': str,
    'short_squeeze_score': np.float64,
    'days_to_cover': np.float64,
    'days_to_cover_by_quantity': np.float64,
    'short_loan_days_to_cover': np.float64,
    'short_loan_days_to_cover_by_quantity': np.float64,
    'pct_free_float_quantity_on_loan': np.float64,
    'pct_free_float_value_on_loan': np.float64,
    'short_loan_quantity_as_pct_of_free_float_quantity': np.float64,
    'pct_shares_outstanding_on_loan': np.float64,
    'short_loan_quantity_as_pct_of_shares_outstanding': np.float64,
    'pct_market_cap_on_loan': np.float64,
    'short_loan_value_as_pct_of_market_cap': np.float64,
    'new_loan_value': np.float64,
    'new_loan_quantity': np.float64,
    'active_lending_agents': np.float64,
    'inactive_lending_agents': np.float64,
    'prime_brokers': np.float64,
    'saf':np.float64,
    'sar':np.float64,
  },
})
