from websocket import create_connection
import json
import getopt
import sys
from strunner import *
setupEnvironment()
import jglib.infra.python.fileio as fio
from stcommon.infra.rds.kafka_operation import KafkaPublisher
from stcommon.infra.rds.snowflake_operation import *
import logging
import pandas as pd
import os
import socket

logger_level = "INFO"

logger = logging.getLogger()
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root_temp = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.append(project_root_temp)

import jglib.infra.python.fileio as fio
config = fio.read_config_secrets()
objconfig = fio.read_config_secrets()
IRO_Fields=['ACTIV_DATE','BPV','CCY1','DAY_COUNT','FID_515','FID_516','FID_875','FOR_PREM','GEN_VAL1','GEN_VAL10','GEN_VAL15','GEN_VAL16','GEN_VAL2','GEN_VAL3','GEN_VAL4','GEN_VAL5','GEN_VAL6','GEN_VAL7','GEN_VAL9','GN_TX20_1','GN_TX20_2','GN_TX20_3','GN_TXT16_2','GN_TXT32_1','GN_TXT32_2','GN_TXT32_4','GV3_TEXT','GV4_TEXT','IMP_VOLT','PRC_VOLTY','PREMIUM','STLVAL2_6','STLVAL2_7','STLVAL2_8','STLVAL2_9','STLVAL3_6','STLVAL3_7','STRIKE_PRC','TIMACT','VALUE_DT1','VEGA']
final_message={}


df_processed=pd.DataFrame({'rec':[],'ACTIV_DATE':[],'img':[]})
logging.basicConfig(format='%(asctime)s %(message)s', datefmt='%m/%d/%Y %I:%M:%S %p')
HOSTNAME = str(socket.gethostname())


class WebSocketSession:
    
    MAX_RETRIES = 5  
    RETRY_DELAY = 5
    
    def __init__(self, instance_id=""):
        self.instance_id = instance_id
    
    def _process_messages(self,ws):
        obj_sf = SnowflakeDML("JG_TPICAP")
        config_df = obj_sf.fetch_query("SELECT * FROM JG_TPICAP.CONFIG.TP_ICAP_CONFIG;")
        regex_patterns_list = config_df[config_df['TICKER_TYPE']=='Intraday']['TICKER_REGEX_PATTERN'].values
        
        #f = open('ICAPRealTimeData','a+')
        global df_processed
        global final_message
        while 1==1:
            try:
                msg = json.loads(ws.recv())
            except Exception as e:
                logger.error(f"Error in receiving data: {e}... Reconnecting...\n {msg}")
                return -1
            msg_type = msg["msg"]
            
            if msg_type=="sub":
                if msg["rec"].startswith(tuple(regex_patterns_list)):
                    rec=msg["rec"]
                    try:
                        if final_message[rec]:
                            None
                    except Exception as e:
                        final_message[rec]={}

                    final_message[rec]["rec"]=msg["rec"]
                    final_message[rec]["host"] = HOSTNAME
                    for field in IRO_Fields:
                        try:
                            final_message[rec][field]=msg['fvs'][field]
                        except Exception as e:
                            None
                    #f.write(str(msg))
                    #f.write("#############")
                    #f.write(str(final_message[rec]))
                    #f.write("\n")
                    kafka_pub = KafkaPublisher(self.instance_id)
                    kafka_pub.publish_messages("tp_icap_realtime", msg["rec"], json.dumps(final_message[rec]))
                                    
    def connect(self):
        for attempt in range(1, self.MAX_RETRIES + 1):
            try:
                # Start websocket handshake
                logger.info("Connecting...")
                ws_address= objconfig["tpicap_ws_address"]
                ws = create_connection(ws_address)
                login_json={ "msg": "auth", "user": objconfig['tpicap_user'] ,"pass": objconfig['tpicap_pass']  }
                ws.send(json.dumps(login_json))
                logger.info("Connected...")
                return ws
            except Exception as e:
                logger.error(f"Connection attempt {attempt} failed: {e}")
                if attempt < self.MAX_RETRIES:
                    logger.info(f"Retrying in {self.RETRY_DELAY} seconds...")
                    time.sleep(self.RETRY_DELAY)
                else:
                    logger.error("Maximum retry attempts reached. Exiting.")
                    sys.exit(1)

def print_commandline_usage_and_exit(code):
    print("Usage: script.py --app_id=<id> --logger_level=<level> --instance_id=<id>")
    sys.exit(code)
    
if __name__ == "__main__":
    import time
    # Get command line parameters
    app_id = None
    logger_level = None
    instance_id = ""

    opts = []
    try:
        opts, args = getopt.getopt(sys.argv[1:], "", [
            "help", "app_id=", "logger_level=", "instance_id="])
    except getopt.GetoptError:
        print_commandline_usage_and_exit(2)
    for opt, arg in opts:
        if opt in "--help":
            print_commandline_usage_and_exit(0)
        elif opt in "--app_id":
            app_id = arg
        elif opt in "--logger_level":
            logger_level = arg
        elif opt == "--instance_id":
            instance_id = arg

    session1 = WebSocketSession(instance_id)
    ret=-1
    while ret == -1:
        ws = session1.connect()
        ret = session1._process_messages(ws)