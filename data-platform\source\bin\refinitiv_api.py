import sys
import time
import getopt
import requests
import logging
import socket
import json
import websocket
import threading
from datetime import datetime
import pandas as pd
import numpy as np
import os
import psycopg2
import toml
import snowflake.connector

from typing import Optional

logger = logging.getLogger()
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root_temp = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.append(project_root_temp)
logging.basicConfig(
    encoding='utf-8',
    level=logging.INFO,
    format="%(asctime)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger.debug(project_root_temp)

#import stcommon
#from stcommon.infra.datalake import *
from strunner import *
setupEnvironment()
import jgdata
import jglib.infra.python.fileio as fio
from stcommon.infra.rds.kafka_operation import KafkaPublisher
import psycopg2
import toml
from dateutil import relativedelta

# Global Default Variables
app_id = '256'
auth_token = ''
auth_url = 'https://api.refinitiv.com/auth/oauth2/v2/token'
clientid = ''
client_secret = ''
discovery_url = 'https://api.refinitiv.com/streaming/pricing/v1/'
hostName = ''
hostName2 = ''
hostList = []
backupHostList = []
hotstandby = False
port = 443
port2 = 443
position = ''
region = 'us-east-1'
ric = '/TRI.N'
scope = 'trapi.streaming.pricing.read'
service = 'ELEKTRON_DD'
session2 = None
curTS = 0
tokenTS = 0
data_df = pd.DataFrame(columns=["timestamp", "message"])

DICT_CUSIP_REF = {}
objconfig = {}

HOSTNAME = str(socket.gethostname())

def run_sf_query(query):
    try:
        logging.getLogger('snowflake.connector').setLevel(logging.WARNING)
        conn = snowflake.connector.connect(
            user = objconfig['sf_user'],
            password = objconfig['sf_password'],
            account = objconfig['sf_account'],
            warehouse = objconfig['sf_refinitiv_warehouse'],
            database = objconfig['sf_refinitiv_database'],
            schema = objconfig['sf_refinitiv_schema'],
            role = objconfig['sf_refinitiv_owner']
        )
        df = pd.read_sql(query, conn)
        return df
    except Exception as e:
        logger.error(f"Error querying Snowflake: {e}")
        return None

class WebSocketSession:
    session_name = ''
    web_socket_app = None
    web_socket_open = False
    host = ''
    force_disconnected = False
    reconnecting = True
    wst = None 

    def __init__(self, name, host, instance_id):
        logger.debug("In the __init__ method of WebSocket")
        self.session_name = name
        self.host = host
        self.instance_id = instance_id
        # quit()

    def _send_market_price_request(self, ric_name):
        """ Create and send simple Market Price request """
        
        # logger.debug("The latest cusip from snap proces start")
         
        # df_provided = datalake.readTable('gl','bloomberg.snap','bloombergprice',version='mk1')
        # latest_records_idx = df_provided.groupby(['date', 'identifier', 'attribute'])['availDate'].idxmax()
        # load_asOf_all_df = df_provided.loc[latest_records_idx].reset_index(drop=True)  
        
        # logger.debug("The latest cusip from snap is below")
        # logger.debug(load_asOf_all_df)
        # logger.debug(load_asOf_all_df[['date', 'identifier', 'attribute','value']])
        # cusip_value_list = load_asOf_all_df[load_asOf_all_df['attribute'] == 'ID_CUSIP']['value'].tolist()
        # cusip_value_list = list(set(cusip_value_list))
        # cusip_value_list = [cusip + "=TWEB" for cusip in cusip_value_list]
        # logger.debug(cusip_value_list)
        # wait(10)
        
        #cusip_value_list = ['USFN1555=TWEB','USFN15601=TWEB','USFN1560RA=TWEB','USFN30652=TWEB','USFN3065=TWEB','USFN30702=TWEB','USG230202=TWEB','USG230351=TWEB','USG23035RB=TWEB','USG23050RB=TWEB','USG23055RA=TWEB','USFN15151=TWEB','USFN1515=TWEB','USFN1520RA=TWEB','USFN1530RA=TWEB','USFN15351=TWEB','USFN1545RB=TWEB','USFN15701=TWEB','USFN1575RB=TWEB','USFN3075RA=TWEB','USG23075RA=TWEB','USG23080RA=TWEB','USFN1595RA=TWEB','USFN30902=TWEB','USFN30951=TWEB','USFN15951=TWEB','USFN30102=TWEB','USFN15702=TWEB','USFN1570=TWEB','USFN3075RB=TWEB','USG23085=TWEB','USG230901=TWEB','USFN1510=TWEB','USFN15901=TWEB','USFN3020=TWEB','USFN3030RB=TWEB','USFN30352=TWEB','USFN3035=TWEB','USFN3035RB=TWEB','USFN3050=TWEB','USFN30551=TWEB','USFN3060RB=TWEB','USFN3065RA=TWEB','USG23020=TWEB','USG230252=TWEB','USG23040=TWEB','USG230451=TWEB','USG23050RA=TWEB','USG230652=TWEB','USFN15152=TWEB','USG23055RB=TWEB','USFN1515RB=TWEB','USFN1520RB=TWEB','USFN15352=TWEB','USFN1535=TWEB','USFN15552=TWEB','USFN15801=TWEB','USFN30802=TWEB','USFN3080=TWEB','USFN30851=TWEB','USG230701=TWEB','USG230852=TWEB','USG23085RB=TWEB','USFN1595RB=TWEB','USG230102=TWEB','USFN15102=TWEB','USFN3010=TWEB','USG230651=TWEB','USFN1550=TWEB','USFN1580RA=TWEB','USFN30801=TWEB','USG230702=TWEB','USG23070RA=TWEB','USG23090RA=TWEB','USG23095RA=TWEB','USFN15902=TWEB','USFN30152=TWEB','USFN3025RB=TWEB','USFN30302=TWEB','USFN30351=TWEB','USFN3040RA=TWEB','USFN30601=TWEB','USFN30701=TWEB','USFN3070RA=TWEB','USFN3055=TWEB','USG23020RA=TWEB','USG23025=TWEB','USG23035=TWEB','USG23035RA=TWEB','USG230501=TWEB','USG23065RA=TWEB','USG23060RA=TWEB','USFN15201=TWEB','USFN1535RB=TWEB','USFN15851=TWEB','USFN15852=TWEB','USFN1590RB=TWEB','USG23095RB=TWEB','USG230951=TWEB','USFN15101=TWEB','USFN15952=TWEB','USFN30101=TWEB','USG23015RA=TWEB','USG23040RB=TWEB','USG23095=TWEB','USG23080RB=TWEB','USFN1555RB=TWEB','USFN1565RB=TWEB','USFN30251=TWEB','USFN3025=TWEB','USFN3030RA=TWEB','USFN30401=TWEB','USFN30451=TWEB','USFN3045RA=TWEB','USFN30502=TWEB','USFN3050RB=TWEB','USG230201=TWEB','USG23020RB=TWEB','USG23030RB=TWEB','USG230401=TWEB','USG230402=TWEB','USG23040RA=TWEB','USG23060RB=TWEB','USFN15252=TWEB','USFN1540=TWEB','USFN15451=TWEB','USFN15751=TWEB','USFN1575RA=TWEB','USFN1585=TWEB','USFN30751=TWEB','USFN3080RB=TWEB','USG23070=TWEB','USFN3090RA=TWEB','USG23010RA=TWEB','USG23090=TWEB','USG23030=TWEB','USG230502=TWEB','USFN1525=TWEB','USFN1530=TWEB','USFN15802=TWEB','USFN1585RB=TWEB','USG230802=TWEB','USG23010=TWEB','USFN1565RA=TWEB','USFN30151=TWEB','USFN3015=TWEB','USFN3040=TWEB','USFN30402=TWEB','USG230152=TWEB','USG230251=TWEB','USG23025RA=TWEB','USG230301=TWEB','USG230302=TWEB','USG23045RA=TWEB','USG23050=TWEB','USG230601=TWEB','USG23060=TWEB','USG230552=TWEB','USFN1515RA=TWEB','USFN1545RA=TWEB','USFN1550RA=TWEB','USFN1570RA=TWEB','USFN1570RB=TWEB','USFN30752=TWEB','USFN3075=TWEB','USFN3080RA=TWEB','USFN3085=TWEB','USFN1510RB=TWEB','USFN1590RA=TWEB','USG23010RB=TWEB','USFN3090=TWEB','USFN15651=TWEB','USFN3015RB=TWEB','USFN3020RA=TWEB','USFN3040RB=TWEB','USFN3050RA=TWEB','USFN3060=TWEB','USG23015=TWEB','USG23030RA=TWEB','USG230452=TWEB','USG23045=TWEB','USFN15202=TWEB','USFN1525RB=TWEB','USFN15302=TWEB','USFN15402=TWEB','USFN1540RA=TWEB','USFN1540RB=TWEB','USG230801=TWEB','USFN1510RA=TWEB','USFN3090RB=TWEB','USFN30901=TWEB','USG230101=TWEB','USFN30301=TWEB','USFN1530RB=TWEB','USFN1535RA=TWEB','USFN15551=TWEB','USFN15752=TWEB','USFN3095RA=TWEB','USFN30952=TWEB','USG230952=TWEB','USG23055=TWEB','USFN15602=TWEB','USFN1560=TWEB','USFN15652=TWEB','USFN1565=TWEB','USFN3015RA=TWEB','USFN30202=TWEB','USFN3020RB=TWEB','USFN3045RB=TWEB','USFN3055RB=TWEB','USFN30651=TWEB','USG230151=TWEB','USG230352=TWEB','USG230551=TWEB','USG23065=TWEB','USG23065RB=TWEB','USFN1520=TWEB','USFN1550RB=TWEB','USFN1580RB=TWEB','USFN1585RA=TWEB','USFN3085RA=TWEB','USG23075=TWEB','USG23075RB=TWEB','USG23080=TWEB','USFN3095=TWEB','USFN30252=TWEB','USFN3035RA=TWEB','USFN15452=TWEB','USFN3010RB=TWEB','USFN1590=TWEB','USFN3030=TWEB','USFN15501=TWEB','USFN1555RA=TWEB','USFN1560RB=TWEB','USFN30201=TWEB','USFN3025RA=TWEB','USFN30452=TWEB','USFN3045=TWEB','USFN30501=TWEB','USFN30552=TWEB','USFN30602=TWEB','USFN3060RA=TWEB','USFN3065RB=TWEB','USFN3070RB=TWEB','USG23015RB=TWEB','USG23025RB=TWEB','USG230602=TWEB','USFN15251=TWEB','USFN15301=TWEB','USFN15401=TWEB','USFN15502=TWEB','USFN1575=TWEB','USFN1580=TWEB','USFN3085RB=TWEB','USFN3095RB=TWEB','USG230902=TWEB','USFN3055RA=TWEB','USFN3070=TWEB','USG23045RB=TWEB','USFN1525RA=TWEB','USFN1545=TWEB','USG23070RB=TWEB','USG230751=TWEB','USG230851=TWEB','USG23085RA=TWEB','USFN3010RA=TWEB','USFN1595=TWEB','USFN30852=TWEB','USG230752=TWEB','USG23090RB=TWEB']
        cusip_value_list = list(DICT_CUSIP_REF.keys())
        
        mp_req_json = {
            'ID': 2,
            'Key': {
                'Name': cusip_value_list,
                'Service': service
            },
            'View': ["BID", "ASK", "CUSIP_CD", "Name", "VALUE_DT1", "VALUE_TS1", "QUOTE_DATE", "QUOTIM_NS"]
        }
        self.web_socket_app.send(json.dumps(mp_req_json))
        logger.debug(str(datetime.now()) + " SENT on " + self.session_name + ":")
        logger.debug(json.dumps(mp_req_json, sort_keys=True, indent=2, separators=(',', ':')))

    def _send_login_request(self, authn_token):
        """
            Send login request with authentication token.
            Used both for the initial login and subsequent reissues to update the authentication token
        """
        login_json = {
            'ID': 1,
            'Domain': 'Login',
            'Key': {
                'NameType': 'AuthnToken',
                'Elements': {
                    'ApplicationId': '',
                    'Position': '',
                    'AuthenticationToken': ''
                }
            }
        }

        login_json['Key']['Elements']['ApplicationId'] = app_id
        login_json['Key']['Elements']['Position'] = position
        login_json['Key']['Elements']['AuthenticationToken'] = authn_token

        self.web_socket_app.send(json.dumps(login_json))
        logger.debug(str(datetime.now()) + " SENT on " + self.session_name + ":")
        logger.debug(json.dumps(login_json, sort_keys=True, indent=2, separators=(',', ':')))

    def _process_login_response(self, message_json):
        """ Send item request upon login success """
        if message_json['Type'] == "Status" and message_json['Domain'] == "Login" and \
                (message_json['State']['Stream'] != "Open" or message_json['State']['Data'] != "Ok"):
            logger.error((str(datetime.now()) + " Error: Login failed, received status message, closing: StreamState={}, DataState={}" \
                .format(message_json['State']['Stream'],message_json['State']['Data'])))
            if self.web_socket_open:
                self.web_socket_app.close()
            self.force_disconnected = True
            return

        self._send_market_price_request(ric)

    def _process_message(self, message_json):
        """ Parse at high level and output JSON of message """
        message_type = message_json['Type']

        if message_type == "Ping":
            pong_json = {'Type': 'Pong'}
            self.web_socket_app.send(json.dumps(pong_json))
            logger.debug(str(datetime.now()) + " SENT on " + self.session_name + ":")
            logger.debug(json.dumps(pong_json, sort_keys=True, indent=2, separators=(',', ':')))
        else:
           if 'Domain' in message_json:
               message_domain = message_json['Domain']
               if message_domain == "Login":
                   self._process_login_response(message_json)
           elif (message_type == "Refresh" or message_type == "Update") and "Fields" in message_json :
               message_data = message_json['Fields']
               ric_name = message_json['Key']
               message_data.update(ric_name)
               current_utc_time = datetime.utcnow()
               logger.debug("********************BEGIN********************")
               logger.debug("********************BEFORE ENRICHMENT********************")
               logger.debug(message_data)
               
               if "CUSIP_CD" not in message_data.keys():
                   message_data["CUSIP_CD"] = DICT_CUSIP_REF[message_data["Name"]]
               else:
                   DICT_CUSIP_REF[message_data["Name"]] = message_data["CUSIP_CD"]    
               
               if "VALUE_DT1" not in message_data.keys():
                   message_data["VALUE_DT1"] = message_data["QUOTE_DATE"]

               if "VALUE_TS1" not in message_data.keys():
                   message_data["VALUE_TS1"] = message_data["QUOTIM_NS"]

               message_data['SETTLEDATE'] = derive_settle_month(pd.to_datetime(message_data['VALUE_DT1'] + ' ' + message_data['VALUE_TS1']), message_data['CUSIP_CD']).strftime('%Y-%m-%d')
               #df['SETTLEDATE'] = df.apply(lambda row: derive_settle_month(pd.to_datetime(row['VALUE_DT1'] + ' ' +row['VALUE_TS1']), row['CUSIP_CD']), axis=1)

               message_data['HOST'] = HOSTNAME
               required_columns = ['Name', 'Service', 'ASK', 'BID', 'VALUE_DT1', 'VALUE_TS1', 'HOST']
               for column in required_columns:
                   if column not in message_data.keys():
                       message_data[column] = None
                       
               logger.debug("********************AFTER ENRICHMENT********************")
               logger.debug(message_data)
               logger.debug("********************DONE********************")
               kafka_pub = KafkaPublisher(instance_id)
               kafka_pub.publish_messages("refinitiv-tba-realtime-api",message_data["CUSIP_CD"], json.dumps(message_data))

               

    # Callback events from WebSocketApp
    def _on_message(self, ws, message):
        """ Called when message received, parse message into JSON for processing """
        logger.debug(str(datetime.now()) + " RECEIVED on " + self.session_name + ":")
        message_json = json.loads(message)
        logger.debug(json.dumps(message_json, sort_keys=True, indent=2, separators=(',', ':')))

        for singleMsg in message_json:
            self._process_message(singleMsg)

    def _on_error(self, ws, error):
        """ Called when websocket error has occurred """
        logger.error(str(datetime.now()) + " " + str(self.session_name) + ": Error: "+ str(error))

    def _on_close(self, ws, close_status_code, close_message):
        """ Called when websocket is closed """
        self.web_socket_open = False
        logger.debug(str(datetime.now()) + " " + str(self.session_name) + ": WebSocket Closed\n")

    def _on_open(self, ws):
        """ Called when handshake is complete and websocket is open, send login """

        logger.debug(str(datetime.now()) + " " + str(self.session_name) + ": WebSocket successfully connected!")
        self.web_socket_open = True
        self.reconnecting = False
        self._send_login_request(auth_token)

    # Operations
    def connect(self):
        # Start websocket handshake
        ws_address = "wss://{}/WebSocket".format(self.host)
        #websocket.enableTrace(True)
        if (not self.web_socket_app) or self.reconnecting:
            self.web_socket_app = websocket.WebSocketApp(ws_address, 
                                                     on_message=self._on_message,
                                                     on_error=self._on_error,
                                                     on_close=self._on_close,
                                                     on_open=self._on_open,
                                                     subprotocols=['tr_json2'])
        # Event loop
        if not self.wst:
            logger.debug(str(datetime.now()) + " " + self.session_name + ": Connecting WebSocket to " + ws_address + "...")
            self.wst = threading.Thread(target=self.web_socket_app.run_forever, kwargs={'sslopt': {'check_hostname': False}})
            self.wst.daemon = True
            self.wst.start()
        elif self.reconnecting and not self.force_disconnected:
            logger.error(str(datetime.now()) + " " + self.session_name + ": Reconnecting WebSocket to " + ws_address + "...")
            self.web_socket_app.run_forever()


    def disconnect(self):
        self.force_disconnected = True
        if self.web_socket_open:
            logger.debug(str(datetime.now()) + " " + self.session_name + ": Closing WebSocket\n")
            self.web_socket_app.close()


def query_service_discovery(url=None):

    if url is None:
        url = discovery_url

    logger.info(f" Sending Refinitiv Data Platform service discovery request to {url}" )

    try:
        r = requests.get(url, headers={"Authorization": "Bearer " + auth_token}, params={"transport": "websocket"}, allow_redirects=False)

    except requests.exceptions.RequestException as e:
        logger.error('Refinitiv Data Platform service discovery exception failure:', e)
        return False

    if r.status_code == 200:
        # Authentication was successful. Deserialize the response.
        response_json = r.json()
        logger.info(str(datetime.now()) + " Refinitiv Data Platform Service discovery succeeded." + \
                " RECEIVED:")
        logger.info(json.dumps(response_json, sort_keys=True, indent=2, separators=(',', ':')))

        for index in range(len(response_json['services'])):
            if not response_json['services'][index]['location'][0].startswith(region):
                continue

            if not hotstandby:
                if len(response_json['services'][index]['location']) >= 2:
                    hostList.append(response_json['services'][index]['endpoint'] + ":" +
                                    str(response_json['services'][index]['port']))
                    continue
                if len(response_json['services'][index]['location']) == 1:
                    backupHostList.append(response_json['services'][index]['endpoint'] + ":" +
                                    str(response_json['services'][index]['port']))
                    continue
            else:
                if len(response_json['services'][index]['location']) == 1:
                    hostList.append(response_json['services'][index]['endpoint'] + ":" +
                                    str(response_json['services'][index]['port']))

        if hotstandby:
            if len(hostList) < 2:
                logger.debug("Expected 2 hosts but received:", len(hostList), "or the region:", region, "is not present in list of endpoints")
                sys.exit(1)
        else:
            if len(hostList) == 0:
                if len(backupHostList) > 0:
                    for hostIndex in range(len(backupHostList)):
                        hostList.append(backupHostList[hostIndex])
                else:
                    logger.debug("The region:", region, "is not present in list of endpoints")
                    sys.exit(1)

        return True

    elif r.status_code in [ 301, 302, 307, 308 ]:
        # Perform URL redirect
        logger.debug('Refinitiv Data Platform service discovery HTTP code:', r.status_code, r.reason)
        new_host = r.headers['Location']
        if new_host != None:
            logger.debug('Perform URL redirect to ', new_host)
            return query_service_discovery(new_host)
        return False
    elif r.status_code in [ 403, 404, 410, 451 ]:
        # Stop trying the request
        logger.error('Refinitiv Data Platform service discovery HTTP code:', r.status_code, r.reason)
        logger.error('Unrecoverable error when performing service discovery: stopped retrying request')
        return False
    else:
        # Retry request with an appropriate delay: 
        logger.error('Refinitiv Data Platform service discovery HTTP code:', r.status_code, r.reason)
        time.sleep(5)
        # CAUTION: This is sample code with infinite retries.
        logger.debug('Retrying the service discovery request')
        return query_service_discovery()


def get_auth_token(url=None):
    """
        Retrieves an authentication token.
    """

    if url is None:
        url = auth_url

    data = {'grant_type': 'client_credentials', 'scope': scope, 'client_id': clientid, 'client_secret': client_secret}

    logger.info(f" Sending authentication request with client credentials to {url}")
    try:
        # Request with auth for https protocol    
        r = requests.post(url,
                headers={'Accept' : 'application/json'},
                          data=data,
                          verify=True,
                          allow_redirects=False)

    except requests.exceptions.RequestException as e:
        logger.error('Refinitiv Data Platform authentication exception failure:', e)
        return None, None

    if r.status_code == 200:
        auth_json = r.json()
        logger.info("Refinitiv Data Platform Authentication succeeded. RECEIVED:")
        logger.info(json.dumps(auth_json, sort_keys=True, indent=2, separators=(',', ':')))
        return auth_json['access_token'], auth_json['expires_in']
    elif r.status_code in [ 301, 302, 307, 308 ]:
        # Perform URL redirect
        logger.debug('Refinitiv Data Platform authentication HTTP code:', r.status_code, r.reason)
        new_host = r.headers['Location']
        if new_host != None:
            logger.debug('Perform URL redirect to ', new_host)
            return get_auth_token(new_host)
        return None, None
    elif r.status_code in [ 400, 401, 403, 404, 410, 451 ]:
        # Stop trying the request
        # NOTE: With 400 and 401, there is not retry to keep this sample code simple
        logger.debug('Refinitiv Data Platform authentication HTTP code:', r.status_code, r.reason)
        logger.debug('Unrecoverable error: stopped retrying request')
        return None, None
    else:
        logger.debug('Refinitiv Data Platform authentication failed. HTTP code:', r.status_code, r.reason)
        time.sleep(5)
        # CAUTION: This is sample code with infinite retries.
        logger.debug('Retrying auth request')
        return get_auth_token()


def print_commandline_usage_and_exit(exit_code):
    print('Usage: market_price_rdpgw_client_cred_auth.py [--app_id app_id] '
          '--clientid clientid --clientsecret client secret [--position position] [--auth_url auth_url] '
          '[--hostname hostname] [--port port] [--standbyhostname hostname] [--standbyport port] ' 
          '[--discovery_url discovery_url] [--scope scope] [--service service]'
          '[--region region] [--ric ric] [--hotstandby] [--help]')
    sys.exit(exit_code)


def get_cusip_ref_from_sf():
    df_cusip_ref = run_sf_query("""select cr.ric, cr.cusip_combined cusip_cd
                                   from   refinitiv.tba.cusip_reference cr
                                   where  cr.value_ts =  (select max(value_ts) from refinitiv.tba.cusip_reference cr2 where cr.ric = cr2.ric)""")
    logger.debug(df_cusip_ref)
    return dict(zip(df_cusip_ref["RIC"], df_cusip_ref["CUSIP_CD"]))

def derive_settle_month(ts, cusip):
    m_cusip_month_code = " 123456789ABC"
    month = m_cusip_month_code.index(cusip[7])
    list_dts = [ts.date().replace(day=1) + relativedelta.relativedelta(months=x) for x in range(-2, 10)]
    return {x.month: x for x in list_dts}[month]

if __name__ == "__main__":
    import time
    # Get command line parameters
    opts = []
    instance_id = ""
    try:
        opts, args = getopt.getopt(sys.argv[1:], "", [
            "help", "app_id=", "clientsecret=", "clientid=", 
            "hostname=", "port=", "standbyhostname=", "standbyport=", 
            "position=", "auth_url=", "discovery_url=", 
            "scope=", "service=", "region=", "ric=", "hotstandby", "instance_id="])
    except getopt.GetoptError:
        print_commandline_usage_and_exit(2)
    for opt, arg in opts:
        if opt in "--help":
            print_commandline_usage_and_exit(0)
        elif opt in "--app_id":
            app_id = arg
        elif opt in "--clientsecret":
            client_secret = arg
        elif opt in "--clientid":
            clientid = arg
        elif opt in "--hostname":
            hostName = arg
        elif opt in "--standbyhostname":
            hostName2= arg
        elif opt in "--port":
            port = arg
        elif opt in "--standbyport":
            port2= arg
        elif opt in "--position":
            position = arg
        elif opt in "--auth_url":
            auth_url = arg
        elif opt in "--discovery_url":
            discovery_url = arg
        elif opt in "--scope":
            scope = arg
        elif opt in "--service":
            service = arg
        elif opt in "--region":
            region = arg
        elif opt in "--ric":
            ric = arg
        elif opt in "--hotstandby":
            hotstandby = True
        elif opt in "--instance_id":
            instance_id = arg

    objconfig = fio.read_config_secrets()
    DICT_CUSIP_REF = get_cusip_ref_from_sf()
    if clientid == '' or client_secret == '':
        clientid = objconfig['refinitiv_clientid']
        client_secret = objconfig['refinitiv_clientsecret']  
        if clientid == '' or client_secret == '':
            logger.debug("clientid and clientsecret are required options")
            sys.exit(2)
        
            
    if position == '':
        # Populate position if possible
        try:
            position_host = socket.gethostname()
            position = socket.gethostbyname(position_host) + "/" + position_host
        except socket.gaierror:
            position = "127.0.0.1/net"

    auth_token, expire_time = get_auth_token()
    if not auth_token:
        logger.error("Failed initial authentication with Refinitiv Data Platform. Exiting...")
        sys.exit(1)

    tokenTS = time.time()

    # If hostname is specified, use it for the connection
    if hostName != '':
        hostList.append(hostName + ':' + str(port))
        if hostName2 != '':
            hostList.append(hostName2 + ':' + str(port2))
    else:
        # Query VIPs from Refinitiv Data Platform service discovery if user did not specify hostname
        if not query_service_discovery():
            logger.error("Failed to retrieve endpoints from Refinitiv Data Platform Service Discovery. Exiting...")
            sys.exit(1)

    # Start websocket handshake; create two sessions when the hotstandby parameter is specified.
    session1 = WebSocketSession("Session1", hostList[0], instance_id)
    session1.connect()

    if hotstandby and len(hostList) > 1:
        session2 = WebSocketSession("Session2", hostList[1], instance_id)
        session2.connect()

    try:
        while True:
            # NOTE about connection recovery: When connecting or reconnecting 
            #   to the server, a valid token must be used. Upon being disconnecting, initial 
            #   reconnect attempt must be done with  a new token.
            #   If a successful reconnect takes longer than token expiration time, 
            #   a new token must be obtained proactively. 

            # Waiting a few seconds before checking for connection down and attempting reconnect
            time.sleep(5)
            if not session1.web_socket_open or ( session2 and not session2.web_socket_open ) :
                if session1.reconnecting or ( session2 and session2.reconnecting ) :
                    curTS = time.time()
                    if (int(expire_time) < 600):
                        deltaTime = float(expire_time) * 0.05
                    else:
                        deltaTime = 300
                    if (int(curTS) >= int(float(tokenTS) + float(expire_time) - float(deltaTime))):
                        auth_token, expire_time = get_auth_token() 
                        tokenTS = time.time()
                else:
                    auth_token, expire_time = get_auth_token() 
                    tokenTS = time.time()

                if not session1.web_socket_open and not session1.force_disconnected:
                    session1.reconnecting = True
                if ( session2 and not session2.web_socket_open ) and not session2.force_disconnected:
                    session2.reconnecting = True

                if auth_token is not None:
                    if (not session1.force_disconnected) and session1.reconnecting:
                        session1.connect()
                    if session2 and (not session2.force_disconnected) and session2.reconnecting:
                        session2.connect()
                else:
                    logger.error("Failed authentication with Refinitiv Data Platform. Exiting...")
                    sys.exit(1) 


    except KeyboardInterrupt:
        session1.disconnect()
        if hotstandby:
            session2.disconnect()
