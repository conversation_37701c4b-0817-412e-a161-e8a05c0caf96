from airflow import DAG
from airflow.operators.python import <PERSON>Operator
from datetime import datetime, timedelta, timezone
import os, sys, pendulum, subprocess, logging
from airflow.providers.http.operators.http import HttpOperator
from util.validate import check_for_anomalies
from airflow.timetables.trigger import CronTriggerTimetable

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()
from stcommon.time import *
import stcommon.tools.dates as dates
# importlib.import_module('jgdata.datasets.jpm.dataquery')
JGDATA_PATH = os.environ.get("JGDATA_PATH")


def run_dq_check_script():
    

    command = (
        f'python3 {JGDATA_PATH}/bin/dq_check_runner.py --dataset_name APACST_JPM --application_cd APACST_JPM'
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2025, 2, 12, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id="jg-jpm-dataquery-dq-runner",
    description="This DAG runs ETL for JPM Dataquery",
    default_args=default_args,
    # schedule_interval='0 8-12 * * 1-6',
    schedule=CronTriggerTimetable('15 8 * * 1-6', timezone="America/New_York"),
    tags=["jgdata","JPM", "APACST"],
    catchup=False
)

dq_job = PythonOperator(
    task_id="jg-jpm-dataquery-dq-runner",
    python_callable=run_dq_check_script,
    execution_timeout=timedelta(minutes=30),
    dag=dag
)

dq_job