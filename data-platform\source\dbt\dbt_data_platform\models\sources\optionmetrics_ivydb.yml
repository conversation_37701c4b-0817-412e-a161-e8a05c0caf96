# dbt tests for optionmetrics_ivydb: security_price and volatility_surface
# This file defines data quality tests for the OptionMetrics IvyDB source tables.
# Tests include:
# - not_null: Ensures columns are never null for the last business day (if today is Monday, checks Friday; otherwise, checks yesterday).
# - test_positive_or_zero: Ensures columns have only positive or zero values (with optional ignore_value for missing data) for the last business day.
# - test_zero_percent: Ensures the percentage of zeros in a column does not exceed 10% for the last business day.
# - accepted_values: Ensures string columns have only expected values (e.g., 'C' or 'P') for the last business day.
# - test_max_date_is_yesterday: Ensures the latest date in the table is yesterday or if is monday is friday(data freshness).
# All tests are filtered to only check data from the last business day (using a case statement: if today is Monday, checks Friday; otherwise, checks yesterday).

version: 2

sources:
  - name: optionmetrics_ivydb
    database: ivydbus
    schema: public
    tables:
      - name: security_price
        columns:
          - name: securityid
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: date
            tests:
              - test_max_date_is_yesterday:
                  ignore_weekend: true
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"

          - name: bidlow
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_zero_percent:
                  threshold: 0.1 # 10%
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: askhigh
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_zero_percent:
                  threshold: 0.1 # 10%
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: closeprice
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: volume
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_positive_or_zero:
                  ignore_value: -99.99
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_zero_percent:
                  threshold: 0.1 # 10%
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: totalreturn
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_zero_percent:
                  threshold: 0.1 # 10%
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: adjustmentfactor
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_positive_or_zero:
                  ignore_value: -99.99
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: openprice
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_positive_or_zero:
                  ignore_value: -99.99
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_zero_percent:
                  threshold: 0.1 # 10%
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: sharesoutstanding
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_positive_or_zero:
                  ignore_value: -99.99
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_zero_percent:
                  threshold: 0.1 # 10%
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: adjustmentfactor2
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_positive_or_zero:
                  ignore_value: -99.99
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"

      - name: volatility_surface
        columns:
          - name: securityid
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: date
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_max_date_is_yesterday:
                  ignore_weekend: true
          - name: days
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_positive_or_zero:
                  ignore_value: -99.99
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: delta
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: callput
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - accepted_values:
                  values: ["C", "P"]
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: impliedvolatility
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_positive_or_zero:
                  ignore_value: -99.99
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: impliedstrike
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_positive_or_zero:
                  ignore_value: -99.99
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_zero_percent:
                  threshold: 0.1 # 10%
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: impliedpremium
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_positive_or_zero:
                  ignore_value: -99.99
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_zero_percent:
                  threshold: 0.1 # 10%
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
          - name: dispersion
            tests:
              - not_null:
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
              - test_zero_percent:
                  threshold: 0.1 # 10%
                  where: "date = (case when extract(dow from current_date) = 1 then current_date - interval '3 day' else current_date - interval '1 day' end)"
