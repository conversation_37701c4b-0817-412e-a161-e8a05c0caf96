import pandas as pd

# from jgdata.datasets.bloomberg.snap_api.python.bloomberg.persecurity.py.bbg_302_nested_data_request import *
from datetime import datetime
from jgdata.datasets.bloomberg.snap_api.python.bloomberg.persecurity.py.bbg_309_history_request1 import *
from jgdata.datasets.bloomberg.snap_api.python.bloomberg.persecurity.py.bbg_302_nested_data_request import *
from stcommon.infra.rds.snowflake_operation import *
from stcommon.infra.rds.snowflake_operation import *
import jglib.infra.python.fileio as fio
from stcommon.infra.python.snap_config_reader import *
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
config_root_temp = os.path.abspath(os.path.join(current_dir, '../../../../conf/datasets/vendors'))
config_path = config_root_temp + '/snap.config.toml'
import logging
import sys
from stcommon.email_util_k8s import EmailUtility

logger = logging.getLogger("bloomberg_snap")
logger.setLevel(logging.INFO)

console_handler = logging.StreamHandler(sys.stdout)
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

logger.info("Bloomberg Snap job started.")
email_util = EmailUtility()

def build_bsnap_payload(st, tz):
    from datetime import datetime
    import time
    ticker_list = None
    obj_sf = SnowflakeDML("APACST_PROD_DB")
    df = obj_sf.fetch_query(f"""
                            SELECT DATE(CONVERT_TIMEZONE(sc.timezone, CURRENT_TIMESTAMP())) as END_DATE, 
                                    DATE(CONVERT_TIMEZONE(sc.timezone, CURRENT_TIMESTAMP())) - 15 as START_DATE,
                                    PARSE_JSON(IDENTIFIER_OVERRIDES):mnemonic::STRING AS OVERRIDE_MNEMONIC,
                                    PARSE_JSON(IDENTIFIER_OVERRIDES):override::STRING AS OVERRIDE_VALUE,* 
                            FROM   BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG sc
                            WHERE  SNAP_TIME = '{st}'
                            AND    TIMEZONE = '{tz}'
                            --AND SNAP_TYPE like '%BDP%'
                           -- AND IDENTIFIER_OVERRIDES IS NOT NULL
                    """)

    build_all(df, obj_sf, st, tz, ticker_list)
  


def build_bsnap_payload_adhoc(snap_configs):
    """
    this is how we are expecting a dag to pass the list of dicts with keys: identifier, field, snap_type
    Example:
    [
        {"identifier": "ABC", "field": "PX_LAST", "snap_type": "bdp"},
        {"identifier": "XYZ", "field": "PX_VOLUME", "snap_type": "bdh"}
    ]
    """
    from datetime import datetime
    import time
    import pandas as pd
    import json

    obj_sf = SnowflakeDML("APACST_PROD_DB")


    result = [dict(part.split('=') for part in record.split(', ')) for record in snap_configs.split(';')]
    print(result)

    columns = list(result[0].keys())
    header = f"WITH my_cte ({', '.join(columns)}) AS ("
 
    rows = [
        "SELECT " + ", ".join("'{}'".format(str(row[col]).replace("'", "''")) for col in columns)
        for row in result
        ]
 
    cte_sql = header + "\n    " + "\n    UNION ALL\n    ".join(rows) + "\n)"
    print(cte_sql)

    # Fetch relevant config from Snowflake
    df_config = obj_sf.fetch_query(f""" {cte_sql} 
            SELECT DISTINCT  DATE(CONVERT_TIMEZONE(sc.timezone, CURRENT_TIMESTAMP())) as END_DATE, 
               DATE(CONVERT_TIMEZONE(sc.timezone, CURRENT_TIMESTAMP())) - 15 as START_DATE,
               IDENTIFIER,
               FIELD, 
               SNAP_TYPE,
               'NA'  SNAP_TIME,
               1     IS_ACTIVE,
               'NA'  TIMEZONE,
               PARSE_JSON(IDENTIFIER_OVERRIDES):mnemonic::STRING AS OVERRIDE_MNEMONIC,
               PARSE_JSON(IDENTIFIER_OVERRIDES):override::STRING AS OVERRIDE_VALUE
        FROM   BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG sc
        WHERE  (IDENTIFIER,FIELD,SNAP_TYPE) IN (select IDENTIFIER, ATTRIBUTE, SNAP_TYPE from my_cte)
    """)

    print(df_config)
    if df_config.empty:
        raise ValueError("No matching configuration found for the provided identifier, field, and snap_type combination.")

    logger.info(f"Returned DF: {df_config}")
    # Commenting for now to for testing
    build_all(df_config, obj_sf, None, None, None, 'adhoc')

def build_all(df, obj_sf, st, tz, ticker_list= None, type=None): 
    from datetime import datetime   
    logger.info(f"Returned DF: {df}")
    
    if df.empty:
        logger.info("Nothing to run")
        return
    
    sd = str(df.iloc[0]['START_DATE']).replace("-","")
    ed = str(df.iloc[0]['END_DATE']).replace("-","")
    logger.info(f"SD : {sd}, ED: {ed}")
    sd = (datetime.strptime(str(sd), '%Y%m%d')).strftime('%Y-%m-%d')
    ed = (datetime.strptime(str(ed), '%Y%m%d')).strftime('%Y-%m-%d')
    
    logger.info("Qualified Ticker to go to BDH")
    logger.info(df[(df['SNAP_TYPE'] == 'BDH')])
    build_309(df[(df['SNAP_TYPE'] == 'BDH')], sd, ed, obj_sf, st, tz, type)
    
    logger.info(df[df['SNAP_TYPE'].isin(['BDP-DYNAMIC', 'BDP'])])
    build_302_dynamic(df[df['SNAP_TYPE'].isin(['BDP-DYNAMIC', 'BDP'])], sd, ed, obj_sf, st, tz, ticker_list, type)

    logger.info("Qualified Ticker to go to BDP_STATIC")
    logger.info(df[(df['SNAP_TYPE'] == 'BDP-STATIC')])
    build_302_static(df[(df['SNAP_TYPE'] == 'BDP-STATIC')], sd, ed, obj_sf, st, tz, type)
    
    
def build_302_static(df_cusip_reference, sd, ed, obj_sf, st, tz, type = None):
  if not df_cusip_reference.empty:
    tickers_list = []
    # for index, row in df_cusip_reference.iterrows():
    #         tickers_list.append({'IDENTIFIER': row['IDENTIFIER'], 'FIELD': row['FIELD']})
                
    for index, row in df_cusip_reference.iterrows():
            tickers_list.append({'IDENTIFIER': row['IDENTIFIER'], 'FIELD': row['FIELD'], 
                                 'OVERRIDE_MNEMONIC': row['OVERRIDE_MNEMONIC'], 'OVERRIDE_VALUE': row['OVERRIDE_VALUE']})
 
    if not tickers_list: 
        df_empty = pd.DataFrame()
        return df_empty
    else:      
        df_qualified_tickers = pd.DataFrame(tickers_list)
        
    
    unique_config_types = df_qualified_tickers['FIELD'].unique()
    unique_config_types_lst = df_qualified_tickers['FIELD'].unique().tolist()
    df_302_api_response_final = pd.DataFrame()
    
    for config_type in unique_config_types:
        # unique_config_values = df_qualified_tickers[df_qualified_tickers['FIELD'] == config_type]['IDENTIFIER'].unique()
        # unique_config_values_lst = list(unique_config_values)
        
        unique_config_df = df_qualified_tickers[df_qualified_tickers['FIELD'] == config_type][['IDENTIFIER', 'OVERRIDE_MNEMONIC', 'OVERRIDE_VALUE']]
        unique_config_values_lst = unique_config_df.to_dict(orient='records')

        
        logger.info("302 API Call")
        response_df2 = call_302_api(config_type, unique_config_values_lst, unique_config_types_lst)
        logger.info("Response from 302 API")
        logger.info(response_df2)
        
        
        df_302_api_response_final = pd.concat([df_302_api_response_final, response_df2], ignore_index=True)
        logger.info(f"Response from 302 API, {df_302_api_response_final}")
    

    logger.info(f"After Concat 302:")
    logger.info(df_302_api_response_final)
    
    # df_302_api_response_final['REFERENCE_DATE'] = df_302_api_response_final['REFERENCE_DATE'].astype('datetime64[ns]')
    df_302_api_response_final['date'] = ed
    df_302_api_response_final['availDate'] = pd.Timestamp.utcnow()
    df_302_api_response_final['ref_date'] = df_302_api_response_final['REFERENCE_DATE'].apply(lambda x: x if pd.notnull(x) else None)
    df_302_api_response_final['ref_date'] = pd.to_datetime(df_302_api_response_final['ref_date'])
    df_302_api_response_final['ref_date'] = df_302_api_response_final['ref_date'].dt.strftime('%Y-%m-%d %H:%M:%S')
    df_302_api_response_final = df_302_api_response_final.dropna(subset=['Value']).reset_index(drop=True)
    df_302_api_response_final.rename(columns={'date': 'SNAP_DATE', 'IDENTIFIER': 'IDENTIFIER', 'Attribute': 'ATTRIBUTE', 'Value': 'VALUE', 'availDate': 'AVAILDATE', 'ref_date': 'REF_DATE'}, inplace=True)
    df_302_api_response_final['VALUE_TRANSFORM'] = df_302_api_response_final['VALUE'].apply(convert_to_float)
    df_302_api_response_final['VALUE_TRANSFORM'] = df_302_api_response_final['VALUE_TRANSFORM'].map('{:.6f}'.format)
    df_302_api_response_final['VALUE_TRANSFORM'] = df_302_api_response_final['VALUE_TRANSFORM'].astype('float64')
    logger.info("Post dt conversion 302 dataframe")
    logger.info(df_302_api_response_final)
    
    # latest_records = obj_sf.get_latest_record('BLOOMBERG.SNAP.DATA_FEED', 'AVAILDATE', ['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE'])
    
    snap_condition = f" AND bsc.SNAP_TIME = '{st}' AND bsc.TIMEZONE = '{tz}'" if type != 'adhoc' else "AND 1=1"
    query = f'''
            SELECT distinct df.* FROM BLOOMBERG.SNAP.DATA_FEED df
            JOIN BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG bsc
            ON bsc.IDENTIFIER = df.IDENTIFIER
            AND bsc.field = df.ATTRIBUTE
            {snap_condition}
            WHERE AVAILDATE = (SELECT MAX(AVAILDATE) FROM BLOOMBERG.SNAP.DATA_FEED df2
                  WHERE df.IDENTIFIER = df2.IDENTIFIER
                  AND df.ATTRIBUTE = df2.ATTRIBUTE)
        '''
    print(query)
    latest_records = obj_sf.fetch_query(query)
    
    
    
    existing_df = latest_records[['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE']]
    existing_df['REF_DATE'] = existing_df['REF_DATE'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) else None)
    existing_df['IDENTIFIER'] = existing_df['IDENTIFIER'].astype('object')
    existing_df['ATTRIBUTE'] = existing_df['ATTRIBUTE'].astype('object')
    # existing_df['VALUE'] = existing_df['VALUE'].astype('float64')
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE'].apply(convert_to_float)
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE_TRANSFORM'].map('{:.6f}'.format)
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE_TRANSFORM'].astype('float64')
    logger.info("Post dt conversion on existing dataframe")
    logger.info(existing_df)

    if existing_df.empty:
        df_toBeLoaded = df_302_api_response_final.copy()
    else:
        # existing_df = existing_df.drop_duplicates(subset=['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'VALUE_TRANSFORM']).reset_index(drop=True)
        # df_merged = df_302_api_response_final.merge(existing_df, on=['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE'], how='left', indicator=True)
        
        existing_df = existing_df.drop_duplicates(subset=['IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'VALUE_TRANSFORM']).reset_index(drop=True)
        df_merged = df_302_api_response_final.merge(existing_df, on=['IDENTIFIER', 'ATTRIBUTE'], how='left', indicator=True)

        tolerance = 0.0001
        df_merged['value_diff'] = df_merged['VALUE_TRANSFORM_x'] - df_merged['VALUE_TRANSFORM_y']
        
        logger.info("DF Merged for insertion with value_diff column")
        logger.info(df_merged)
        
        df_toBeLoaded = df_merged[(df_merged['_merge'] == 'left_only') | ((df_merged['_merge'] == 'both') & (df_merged['value_diff'].abs() > tolerance))].drop(columns=['_merge', 'VALUE_y', 'value_diff'])
        df_toBeLoaded.rename(columns = {'VALUE_x':'VALUE'}, inplace=True)
        df_toBeLoaded.rename(columns = {'REF_DATE_x':'REF_DATE'}, inplace=True)
        logger.info("DF to be loaded")
        logger.info(df_toBeLoaded)
        
    df_toBeLoaded = df_toBeLoaded.sort_values(by='REF_DATE').reset_index(drop=True)
    df_toBeLoaded['AVAILDATE'] = df_toBeLoaded['AVAILDATE'].dt.strftime('%Y-%m-%d %H:%M:%S')
    logger.info("\nInserting data into snowflake table")
    df_toBeLoaded = df_toBeLoaded[['DL_REQUEST_ID','SNAP_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'AVAILDATE', 'REF_DATE']]
    if not df_toBeLoaded.empty:
        logger.info("Data to be loaded for 302 API are")
        logger.info(df_toBeLoaded)
        # df_toBeLoaded.to_csv("302_static_result.csv", index=False)
        obj_sf.insert_dataframe(df_toBeLoaded, 'BLOOMBERG.SNAP.DATA_FEED')
        logger.info("Data is inserted into snowflake")
    else:
        logger.info("Nothing to insert from 302 into snowflake")
          
def build_302_dynamic(df_cusip_reference, sd, ed, obj_sf,  st, tz, ticker_list= None, type= None):
  if not df_cusip_reference.empty:
    tickers_list = []
    for index, row in df_cusip_reference.iterrows():
            tickers_list.append({'IDENTIFIER': row['IDENTIFIER'], 'FIELD': row['FIELD'], 
                                 'OVERRIDE_MNEMONIC': row['OVERRIDE_MNEMONIC'], 'OVERRIDE_VALUE': row['OVERRIDE_VALUE']})
                
    if not tickers_list: 
        df_empty = pd.DataFrame()
        return df_empty
    else:      
        df_qualified_tickers = pd.DataFrame(tickers_list)
        
    
    unique_config_types = df_qualified_tickers['FIELD'].unique()
    unique_config_types_lst = df_qualified_tickers['FIELD'].unique().tolist()
    df_302_api_response_final = pd.DataFrame()
    
    for config_type in unique_config_types:
        # unique_config_values = df_qualified_tickers[df_qualified_tickers['FIELD'] == config_type]['IDENTIFIER'].unique()
        # unique_config_values_lst = list(unique_config_values)
        
        unique_config_df = df_qualified_tickers[df_qualified_tickers['FIELD'] == config_type][['IDENTIFIER', 'OVERRIDE_MNEMONIC', 'OVERRIDE_VALUE']]
        unique_config_values_lst = unique_config_df.to_dict(orient='records')

        
        logger.info("302 API Call")
        response_df2 = call_302_api(config_type, unique_config_values_lst, unique_config_types_lst)
        logger.info("Response from 302 API")
        logger.info(response_df2)
        
        
        df_302_api_response_final = pd.concat([df_302_api_response_final, response_df2], ignore_index=True)
        logger.info(f"Response from 302 API, {df_302_api_response_final}")
    

    logger.info(f"After Concat 302:")
    logger.info(df_302_api_response_final)
    
    # df_302_api_response_final['REFERENCE_DATE'] = df_302_api_response_final['REFERENCE_DATE'].astype('datetime64[ns]')
    df_302_api_response_final['date'] = ed
    df_302_api_response_final['availDate'] = pd.Timestamp.utcnow()
    df_302_api_response_final['ref_date'] = df_302_api_response_final['REFERENCE_DATE'].apply(lambda x: x if pd.notnull(x) else pd.Timestamp.utcnow().strftime('%Y-%m-%d'))
    df_302_api_response_final['ref_date'] = pd.to_datetime(df_302_api_response_final['ref_date'])
    df_302_api_response_final['ref_date'] = df_302_api_response_final['ref_date'].dt.strftime('%Y-%m-%d %H:%M:%S')
    df_302_api_response_final = df_302_api_response_final.dropna(subset=['Value']).reset_index(drop=True)
    df_302_api_response_final.rename(columns={'date': 'SNAP_DATE', 'IDENTIFIER': 'IDENTIFIER', 'Attribute': 'ATTRIBUTE', 'Value': 'VALUE', 'availDate': 'AVAILDATE', 'ref_date': 'REF_DATE'}, inplace=True)
    df_302_api_response_final['VALUE_TRANSFORM'] = df_302_api_response_final['VALUE'].apply(convert_to_float)
    df_302_api_response_final['VALUE_TRANSFORM'] = df_302_api_response_final['VALUE_TRANSFORM'].map('{:.6f}'.format)
    df_302_api_response_final['VALUE_TRANSFORM'] = df_302_api_response_final['VALUE_TRANSFORM'].astype('float64')
    logger.info("Post dt conversion 302 dataframe")
    logger.info(df_302_api_response_final)
    
    # latest_records = obj_sf.get_latest_record('BLOOMBERG.SNAP.DATA_FEED', 'AVAILDATE', ['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE'], 15, 'REF_DATE', ed)
    
    ref_date_values = "', '".join(df_302_api_response_final['REF_DATE'].unique())
    if ticker_list is None:
        snap_condition = f" AND bsc.SNAP_TIME = '{st}' AND bsc.TIMEZONE = '{tz}'" if type != 'adhoc' else "AND 1=1"
        query = f'''
                SELECT distinct df.* FROM BLOOMBERG.SNAP.DATA_FEED df
                JOIN BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG bsc
                ON bsc.IDENTIFIER = df.IDENTIFIER
                AND bsc.FIELD = df.ATTRIBUTE
                {snap_condition}
                WHERE AVAILDATE = (SELECT MAX(AVAILDATE) FROM BLOOMBERG.SNAP.DATA_FEED df2
                      WHERE df.IDENTIFIER = df2.IDENTIFIER
                      AND df.ATTRIBUTE = df2.ATTRIBUTE
                      AND df.REF_DATE = df2.REF_DATE)
                AND REF_DATE IN ('{ref_date_values}')
            '''
    else:
        ticker_list_arr = ticker_list.split(',')
        formatted_tickers = ",".join(f"'{ticker.strip()}'" for ticker in ticker_list_arr)
        query = f'''
                SELECT distinct df.* FROM BLOOMBERG.SNAP.DATA_FEED df
                JOIN BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG bsc
                ON bsc.IDENTIFIER = df.IDENTIFIER
                AND bsc.FIELD = df.ATTRIBUTE
                AND bsc.IDENTIFIER IN ({formatted_tickers})
                WHERE AVAILDATE = (SELECT MAX(AVAILDATE) FROM BLOOMBERG.SNAP.DATA_FEED df2
                      WHERE df.IDENTIFIER = df2.IDENTIFIER
                      AND df.ATTRIBUTE = df2.ATTRIBUTE
                      AND df.REF_DATE = df2.REF_DATE)
                AND REF_DATE IN ('{ref_date_values}')
                '''    
    print(query)
    latest_records = obj_sf.fetch_query(query)
    
    existing_df = latest_records[['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE']]
    existing_df['REF_DATE'] = existing_df['REF_DATE'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) else None)
    existing_df['IDENTIFIER'] = existing_df['IDENTIFIER'].astype('object')
    existing_df['ATTRIBUTE'] = existing_df['ATTRIBUTE'].astype('object')
    # existing_df['VALUE'] = existing_df['VALUE'].astype('float64')
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE'].apply(convert_to_float)
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE_TRANSFORM'].map('{:.6f}'.format)
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE_TRANSFORM'].astype('float64')
    logger.info("Post dt conversion on existing dataframe")
    logger.info(existing_df)

    if existing_df.empty:
        df_toBeLoaded = df_302_api_response_final.copy()
    else:
        existing_df = existing_df.drop_duplicates(subset=['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'VALUE_TRANSFORM']).reset_index(drop=True)
        df_merged = df_302_api_response_final.merge(existing_df, on=['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE'], how='left', indicator=True)
        tolerance = 0.0001
        df_merged['value_diff'] = df_merged['VALUE_TRANSFORM_x'] - df_merged['VALUE_TRANSFORM_y']
        
        logger.info("DF Merged for insertion with value_diff column")
        logger.info(df_merged)
        
        df_toBeLoaded = df_merged[(df_merged['_merge'] == 'left_only') | ((df_merged['_merge'] == 'both') & (df_merged['value_diff'].abs() > tolerance))].drop(columns=['_merge', 'VALUE_y', 'value_diff'])
        df_toBeLoaded.rename(columns = {'VALUE_x':'VALUE'}, inplace=True)
        logger.info("DF to be loaded")
        logger.info(df_toBeLoaded)
        
    df_toBeLoaded = df_toBeLoaded.sort_values(by='REF_DATE').reset_index(drop=True)
    df_toBeLoaded['AVAILDATE'] = df_toBeLoaded['AVAILDATE'].dt.strftime('%Y-%m-%d %H:%M:%S')
    logger.info("\nInserting data into snowflake table")
    df_toBeLoaded = df_toBeLoaded[['DL_REQUEST_ID','SNAP_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'AVAILDATE', 'REF_DATE']]
    if not df_toBeLoaded.empty:
        logger.info("Data to be loaded for 302 API are")
        logger.info(df_toBeLoaded)
        # df_toBeLoaded.to_csv("302_dynamic_result.csv", index=False)
        obj_sf.insert_dataframe(df_toBeLoaded, 'BLOOMBERG.SNAP.DATA_FEED')
        logger.info("Data is inserted into snowflake")
    else:
        print("Nothing to insert from 302 into snowflake")



TABLE_STYLE = """
                <style>
                    table {
                        border-collapse: collapse;
                        width: auto; /* Auto width based on content */
                        max-width: 100%; /* Ensures it doesn't overflow */
                        font-size: 12px; /* Small font */
                    }
                    th {
                        background-color: #4CAF50; /* Green header */
                        color: white;
                        padding: 5px;
                        text-align: left;
                        border: 1px solid #ddd;
                    }
                    td {
                        padding: 5px;
                        text-align: left;
                        # border: 1px solid #ddd;
                        border: 1px solid black; 
                    }
                    tr:nth-child(odd) {
                        background-color: #3bdbed; 
                    }
                    tr:nth-child(even) {
                        background-color: #7ae8f5; 
                    }
                </style>
                """
                

def format_dataframe_html(df, title):
    return f"<strong>{title}:</strong><br>" + TABLE_STYLE + df.to_html(index=False, escape=False) + "<br><br>"


def build_309(df_cusip_reference, sd, ed, obj_sf,st, tz, type= None):
  if not df_cusip_reference.empty:
    tickers_list = []
    for index, row in df_cusip_reference.iterrows():
            tickers_list.append({'IDENTIFIER': row['IDENTIFIER'], 'FIELD': row['FIELD'], 
                                 'OVERRIDE_MNEMONIC': row['OVERRIDE_MNEMONIC'], 'OVERRIDE_VALUE': row['OVERRIDE_VALUE']})
                
    if not tickers_list: 
        df_empty = pd.DataFrame()
        return df_empty
    else:      
        df_qualified_tickers = pd.DataFrame(tickers_list)
        
    
    unique_config_types = df_qualified_tickers['FIELD'].unique()
    unique_config_types_lst = df_qualified_tickers['FIELD'].unique().tolist()
    df_309_api_response_final = pd.DataFrame()
    
    for config_type in unique_config_types:
        unique_config_values = df_qualified_tickers[df_qualified_tickers['FIELD'] == config_type]['IDENTIFIER'].unique()
        unique_config_values_lst = list(unique_config_values)
        
        # unique_config_df = df_qualified_tickers[df_qualified_tickers['FIELD'] == config_type][['IDENTIFIER', 'OVERRIDE_MNEMONIC', 'OVERRIDE_VALUE']]
        # unique_config_values_lst = unique_config_df.to_dict(orient='records')

        
        logger.info("309 API Call")
        config = fio.read_config_secrets()
        user_terminal_number = config['bbrg_user_terminal_number']
        response_df2 = call_main_309_for_ref_date_api(config_type,sd, ed, unique_config_values_lst, user_terminal_number)
        df_non_null_from_api_309 = response_df2.dropna(subset=[config_type])
        df_309_api_response_final = pd.concat([df_309_api_response_final, df_non_null_from_api_309], ignore_index=True)
        logger.info(f"Response from 309 API, {df_non_null_from_api_309}")
    

    logger.info(f"After Concat 309: {df_309_api_response_final}")
    logger.info(df_309_api_response_final)
    
    # columns = ["DL_SNAPSHOT_START_TIME", "IDENTIFIER", "DATE","PX_LAST", "FUT_AGGTE_VOL", "ID_CUSIP"]
    columns = ["DL_REQUEST_ID","DL_SNAPSHOT_START_TIME", "IDENTIFIER", "DATE"] + unique_config_types_lst
    existing_columns = [col for col in columns if col in df_309_api_response_final.columns]
    df_309_api_response_final_filtered = pd.DataFrame(df_309_api_response_final[existing_columns])
    
    logger.info("Melting the df_309_api_response_final_filtered")
    df_melted_309 = df_309_api_response_final_filtered.melt(id_vars=["DL_REQUEST_ID","DL_SNAPSHOT_START_TIME", "IDENTIFIER", "DATE"], 
                        var_name="Attribute", 
                        value_name="Value")

    logger.info(df_melted_309)
    df_melted_309['date'] = ed
    df_melted_309['availDate'] = pd.Timestamp.utcnow()
    df_melted_309['ref_date'] = df_melted_309['DATE'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) else None)
    df_melted_309.rename(columns={'date': 'SNAP_DATE', 'IDENTIFIER': 'IDENTIFIER', 'Attribute': 'ATTRIBUTE', 'Value': 'VALUE', 'availDate': 'AVAILDATE', 'ref_date': 'REF_DATE'}, inplace=True)
    df_melted_309 = df_melted_309[['DL_REQUEST_ID','SNAP_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'AVAILDATE', 'REF_DATE']]                 
    # df_melted_309.to_csv("testing_309_output.csv", index =False)
    df_melted_309 = df_melted_309.dropna(subset=['VALUE']).reset_index(drop=True)
    df_melted_309['VALUE_TRANSFORM'] = df_melted_309['VALUE'].apply(convert_to_float)
    df_melted_309['VALUE_TRANSFORM'] = df_melted_309['VALUE_TRANSFORM'].map('{:.6f}'.format)
    df_melted_309['VALUE_TRANSFORM'] = df_melted_309['VALUE_TRANSFORM'].astype('float64')
    
    logger.info("Post dt conversion 309 dataframe")
    logger.info(df_melted_309)
    
    # latest_records = obj_sf.get_latest_record('BLOOMBERG.SNAP.DATA_FEED', 'AVAILDATE', ['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE'], 15, 'REF_DATE', ed)
    
    snap_condition = f" AND bsc.SNAP_TIME = '{st}' AND bsc.TIMEZONE = '{tz}'" if type != 'adhoc' else "AND 1=1"
    
    query = f'''
            SELECT distinct df.* FROM BLOOMBERG.SNAP.DATA_FEED df
            JOIN BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG bsc
            ON bsc.IDENTIFIER = df.IDENTIFIER
            AND bsc.FIELD = df.ATTRIBUTE
            {snap_condition}
            WHERE AVAILDATE = (SELECT MAX(AVAILDATE) FROM BLOOMBERG.SNAP.DATA_FEED df2
                  WHERE df.IDENTIFIER = df2.IDENTIFIER
                  AND df.ATTRIBUTE = df2.ATTRIBUTE
                  AND df.REF_DATE = df2.REF_DATE)
            AND REF_DATE >= DATEADD(DAY, -15, CURRENT_DATE)
        '''
    print(query)
    latest_records = obj_sf.fetch_query(query)
    
    existing_df = latest_records[['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE']]
    existing_df['REF_DATE'] = existing_df['REF_DATE'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) else None)
    existing_df['IDENTIFIER'] = existing_df['IDENTIFIER'].astype('object')
    existing_df['ATTRIBUTE'] = existing_df['ATTRIBUTE'].astype('object')
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE'].apply(convert_to_float)
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE_TRANSFORM'].map('{:.6f}'.format)
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE_TRANSFORM'].astype('float64')
    logger.info("Post dt conversion existing dataframe")
    logger.info(existing_df)
    
    if existing_df.empty:
        df_toBeLoaded = df_melted_309.copy()
    else:
        existing_df = existing_df.drop_duplicates(subset=['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'VALUE_TRANSFORM']).reset_index(drop=True)
        df_merged = df_melted_309.merge(existing_df, on=['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE'], how='left', indicator=True)
        tolerance = 0.0001
        logger.info("DF Mergered 309")
        logger.info(df_merged)
        df_merged['value_diff'] = df_merged['VALUE_TRANSFORM_x'] - df_merged['VALUE_TRANSFORM_y']
        df_toBeLoaded = df_merged[(df_merged['_merge'] == 'left_only') | ((df_merged['_merge'] == 'both') & (df_merged['value_diff'].abs() > tolerance))].drop(columns=['_merge', 'VALUE_y', 'value_diff'])
        df_toBeLoaded.rename(columns = {'VALUE_x':'VALUE'}, inplace=True)
    
   
    # Start: Logic for DQ Exception:
    try:
        
        df_error = df_toBeLoaded[
        (df_merged['_merge'] == 'both') &
        (df_merged['value_diff'].abs() > tolerance) & 
        (df_merged['REF_DATE'] < ed)]
        
        # df_error = df_toBeLoaded
        
        # df_error = df_error[['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE']]
        # df_error_identifier = df_error['IDENTIFIER'].tolist()
        print(f"DF Error: {df_error}")

        
        if df_error is not None and not df_error.empty:
            query_dq_check = f'''
                    SELECT DISTINCT
                        PARSE_JSON(config_parameters):schema_name::STRING AS schema_name,
                        PARSE_JSON(config_parameters):mailist::STRING AS mailist
                    FROM   DATA_PLATFORM_CORE.EXCEPTIONS.CHECK_CONFIG cc
                    where     cc.check_cd = 'HISTORY_CHANGE_CHECK'
                    AND    cc.APPLICATION_CD  = 'APACST_BLOOMBERG'
                    '''
            print(query_dq_check)
            df_dq_check_config = obj_sf.fetch_query(query_dq_check)
            
            for index, row in df_dq_check_config.iterrows():
                table_name = f"{row['SCHEMA_NAME']}.BLOOMBERG_SNAP_CONFIG"
                maillist = str(row['MAILIST'])
                maillist = maillist.split(",")
                
                query_identifier_list = f'''
                        SELECT DISTINCT
                               identifier
                        FROM   {table_name}
                        '''
                print(query_identifier_list)
                df_db_identifier_list = obj_sf.fetch_query(query_identifier_list)
                db_identifier_list = df_db_identifier_list['IDENTIFIER'].tolist()

                df_error = df_error[df_error['IDENTIFIER'].isin(db_identifier_list)]
                
                if df_error is not None and not df_error.empty:
                    body = ("Hi team,<br><br>Please find the below tickers which changes its value from last snaped value.<br>")
                    body += format_dataframe_html(df_error, "Exception Details")

                    email_util.send_email(
                        # to_recipient=["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"],
                        to_recipient = maillist, #["<EMAIL>"],
                        subject=f"BloomberSnap Value Changes Alert",
                        body=body,
                        df=None
                    )
    except Exception as e:
        print(f"Mail Exception encountered: {str(e)}")    
    # END: Logic for DQ Exception
           
    
    df_toBeLoaded = df_toBeLoaded.sort_values(by='REF_DATE').reset_index(drop=True)
    df_toBeLoaded['AVAILDATE'] = df_toBeLoaded['AVAILDATE'].dt.strftime('%Y-%m-%d %H:%M:%S')
    df_toBeLoaded = df_toBeLoaded[['DL_REQUEST_ID', 'SNAP_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'AVAILDATE', 'REF_DATE']]
    logger.info("To Be loaded for 309")
    logger.info(df_toBeLoaded)
    # df_toBeLoaded.to_csv("309_sf_insertion.csv", index = False)
    if not df_toBeLoaded.empty:
        logger.info("Data to be loaded from 309 API are")
        logger.info(df_toBeLoaded)
        logger.info("\nInserting data into snowflake table")
        # df_toBeLoaded.to_csv("309_result.csv", index=False)
        obj_sf.insert_dataframe(df_toBeLoaded, 'BLOOMBERG.SNAP.DATA_FEED')
        logger.info("Data is inserted into snowflake")
    else:
        logger.info("Nothing to insert into snowflake")
 
def convert_to_float(value):
    try:
        # Try converting directly to float
        return float(value)
    except ValueError:
        try:
            # Try converting to datetime and then to epoch
            return pd.to_datetime(value).timestamp()
        except Exception:
            return None 
