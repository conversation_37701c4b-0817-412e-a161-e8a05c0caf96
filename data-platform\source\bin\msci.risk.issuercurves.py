"""
Schedule cron like this:
(crontab -l; echo "0 1 * * * /jfs/tech1/conda/envs/tech1-datait/bin/python
/opt/jsvc-datait/msci.risk/msci.risk.issuercurves.py >>
/opt/jsvc-datait/msci.risk/cron_msci_sftp_runner.log 2>&1") | crontab -

Also possible to run via specific date:
python msci.risk.issuercurves.py --date 20250403

# Backfill *all* missing dates based on SFTP contents
python msci.risk.issuercurves.py --backfill

# Allows ability to just test run a function
python msci.risk.issuercurves.py --test_function

# Allows ability to ingest pre-downloaded data into RDS database
python msci.risk.issuercurves.py --ingest_existing
"""

import os, sys, re, json, datetime, paramiko, time, argparse
from datetime import timedelta
from collections import defaultdict
from pathlib import Path
from typing import List, Set
from strunner import *
setupEnvironment()
from jgdata.datasets.msci.util import *
from jgdata import *
from stcommon.infra.rds.postgres_adaptor import PostgresAdaptor
from stcommon import *
from stcommon.infra.python.fileio import read_toml

# ---------------- CONFIG -------------------
SFTP_HOST = 'sftp.risk.msci.com'
SFTP_PORT = 22
SFTP_USERNAME = ''
SFTP_PASSWORD = ''
REMOTE_DIR = 'outgoing/'

DATA_BASE_DIR = '/jfs/tech1/apps/rawdata/MSCI/risk/data_files'
STATE_FILE = os.path.join(DATA_BASE_DIR, 'download_state.json')

TARGET_FILES = [
    "corp_issuer_recoveries.1D.{date}.txt",
    "mds_usd_issuer_constituents.1D.{date}.txt",
    "mds_usd_issuer.1D.{date}.txt"
]

DB_CONFIG = {
    'host': 'adfo1-instance-1.clgyky4sgiqx.us-east-1.rds.amazonaws.com',
    'dbname': 'fe_risk',
    'schema': 'var',
    'user': '',
    'password': ''
}

SCHEMAS = {
    'stg_mds_usd_issuer': {
        'mds_uid': str,
        'curve_description': str,
        'country': str,
        'currency': str,
        'sector': str,
        'rating': pd.Int64Dtype(),
        'pay_freq': pd.Int64Dtype(),
        'px_maturity': str,
        'px_date': np.dtype('datetime64[ms]'),
        'px_yield': np.float64,
        'px_spread_gov': np.float64,
        'created_at': np.dtype('datetime64[ms]'),
        'file_path': str
    },
    'stg_corp_issuer_recoveries': {
        'recovery_rate_rm_name': str,
        'px_date': np.dtype('datetime64[ms]'),
        'px_recovery_rate': np.float64,
        'created_at': np.dtype('datetime64[ms]'),
        'file_path': str
    },
    'stg_mds_usd_issuer_constituents': {
        'mds_uid': str,
        'curve_description': str,
        'isin': str,
        'currency': str,
        'bond_description': str,
        'seniority': str,
        'obs_date': np.dtype('datetime64[ms]'),
        'recovery_rate_rm_name': str,
        'created_at': np.dtype('datetime64[ms]'),
        'file_path': str
    }
}

MAX_RETRIES = 3
BACKOFF_BASE = 5  # seconds

# --------- UTILITY FUNCTIONS -------------

def call_stored_procedure(engine, procedure_name: str, args=None):
    raw_conn = engine.raw_connection()
    try:
        with raw_conn.cursor() as cursor:
            if args:
                placeholders = ', '.join(['%s'] * len(args))
                sql = f"CALL {procedure_name}({placeholders});"
                cursor.execute(sql, args)
            else:
                sql = f"CALL {procedure_name}();"
                cursor.execute(sql)

        raw_conn.commit()
        log.info(f"Procedure {procedure_name} executed successfully.")

        # Log any PostgreSQL NOTICE messages
        for notice in raw_conn.notices:
            log.info(notice.strip())

    except Exception as e:
        log.error(f"Failed to execute procedure {procedure_name}: {e}")
        raise

def get_today_str():
    return (datetime.datetime.now() - timedelta(days=1)).strftime("%Y%m%d")

def load_state() -> Set[str]:
    if not os.path.exists(STATE_FILE):
        return set()
    with open(STATE_FILE, 'r') as f:
        return set(json.load(f))

def save_state(state: Set[str]):
    with open(STATE_FILE, 'w') as f:
        json.dump(sorted(state), f)

def ensure_local_path(file_name: str, date_str: str):
    folder = file_name.split('.')[0]
    year = date_str[:4]
    date_folder = os.path.join(DATA_BASE_DIR, folder, year, date_str)
    Path(date_folder).mkdir(parents=True, exist_ok=True)
    return os.path.join(date_folder, file_name)

def retryable(func):
    def wrapper(*args, **kwargs):
        for attempt in range(1, MAX_RETRIES + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                wait_time = BACKOFF_BASE * 2 ** (attempt - 1)
                log.warning(f"Attempt {attempt} failed: {e}. Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
        raise Exception(f"All {MAX_RETRIES} attempts failed for {func.__name__}")
    return wrapper

@retryable
def sftp_connect():

    # Create the SSH client
    ssh = paramiko.SSHClient()

    # Accept unknown host keys (equivalent to -o StrictHostKeyChecking=no)
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    # Connect with RSA algorithms explicitly enabled
    ssh.connect(
        hostname=SFTP_HOST,
        port=SFTP_PORT,
        username=SFTP_USERNAME,
        password=SFTP_PASSWORD,
        allow_agent=False,
        look_for_keys=False,
        disabled_algorithms=dict(pubkeys=["rsa-sha2-256", "rsa-sha2-512"])  # Force old 'ssh-rsa'
    )

    # Open the SFTP session
    return ssh.open_sftp()

@retryable
def download_file(sftp, remote_path, local_path):
    sftp.get(remote_path, local_path)

def table_exists(conn, schema: str, table_name: str) -> bool:
    sql = sql = f"SELECT 1 FROM information_schema.tables WHERE table_schema = '{schema}' AND table_name = '{table_name}'"
    df = conn.execute_query(sql)
    return not df.empty

def cast_types(df, table_name):
    tps = SCHEMAS.get(table_name).copy()
    for col in SCHEMAS.get(table_name):
        if col not in df.columns:
            del tps[col]
            continue
        _type = tps[col]
        if _type in (np.float64, pd.Int64Dtype()):
            df[col] = df[col].replace('', None)

    df = df.astype(tps)
    return df

def write_file_to_table_deprecated(file_path: str, table_name: str):

    df = parse_msci_issuer_file(file_path)
    df['FILE_PATH'] = file_path

    if df.empty:
        log.info(f"No data in file {file_path}, skipping DB write.")
        return
    try:
        conn = PostgresAdaptor(
            DB_CONFIG["host"],
            DB_CONFIG["dbname"],
            DB_CONFIG["schema"],
            DB_CONFIG["user"],
            DB_CONFIG["password"]
        )

        log.info(f"Appending to table {table_name}")
        conn.load_dataframe(df, table_name, 'append')
        log.info(f"Wrote {len(df)} rows to {table_name}")
    except Exception as e:
        log.error(f"Error: {e}")
    finally:
        conn.close()

def write_file_to_stg_table(file_path: str, stg_table: str):
    df = parse_msci_issuer_file(file_path)
    df.columns = df.columns.str.lower()
    df = cast_types(df, stg_table)
    df = df.drop_duplicates(keep='first')

    conn = None

    if df.empty:
        log.info(f"No data in file {file_path}, skipping DB write.")
        return
    try:
        conn = PostgresAdaptor(
            DB_CONFIG["host"],
            DB_CONFIG["dbname"],
            DB_CONFIG["schema"],
            DB_CONFIG["user"],
            DB_CONFIG["password"]
        )
        
        sql = f'TRUNCATE TABLE {DB_CONFIG["schema"]}.{stg_table}'
        log.info(f"{sql}")

        with conn.engine.begin() as conn_tmp:
            from sqlalchemy import text
            conn_tmp.execute(text(sql))

        log.info(f"Loading data to table {stg_table}")
        success = conn.load_dataframe(df, stg_table, 'append')
        if success:
            log.info(f"Loading complete into {stg_table}")
        else:
            raise ValueError(f"Loading {file_path} into {stg_table} failed!")
    except Exception as e:
        log.exception(f"Fatal error loading {file_path} into {stg_table}. {str(e)}")
        raise
    finally:
        conn.close()

    if conn:
        return conn
    else:
        PostgresAdaptor( DB_CONFIG["host"],DB_CONFIG["dbname"],DB_CONFIG["schema"],DB_CONFIG["user"],DB_CONFIG["password"])

# ----------- MAIN WORKFLOW ----------------

def daily_download_missing_files(date_str: str):
    expected_set = set(name.format(date=date_str) for name in TARGET_FILES)
    downloaded_files = set(load_state())
    orig_size = len(downloaded_files)
    conn = None
    sftp = None

    log.info(f"Checking for files with date: {date_str}")

    try:
        sftp = sftp_connect()
        remote_files = set(sftp.listdir(REMOTE_DIR))

        # Fail early if any expected files are missing
        missing_remote = expected_set - remote_files
        
        if missing_remote:
            for f in missing_remote:
                log.info(f"Expected file not found in SFTP: {f}")
            raise FileNotFoundError("Required files are missing on the SFTP server. Aborting.")

        missing_files = expected_set - downloaded_files
        execute_sproc = False

        for file_name in missing_files:
            remote_path = os.path.join(REMOTE_DIR, file_name)

            # use `date_str` to build local path
            local_path = ensure_local_path(file_name, date_str)
            
            if os.path.exists(local_path):
                log.info(f"File already exists locally, skipping download: {local_path}")
                downloaded_files.add(file_name)
                continue

            try:
                download_file(sftp, remote_path, local_path)
                log.info(f"Successfully downloaded: {file_name}")
                
                downloaded_files.add(file_name)

                # Derive table name from file name
                table_name = f"stg_{file_name.split('.')[0]}"
                # Ingest file contents into database
                try:
                    log.info(f"Load file: {local_path} to table: {table_name}")
                    conn = write_file_to_stg_table(local_path, table_name)
                    execute_sproc = True
                    log.info(f"Completed file load: {local_path} to table: {table_name}")
                except Exception as e:
                    log.error(f"Error ingesting file {file_name} into {table_name}: {e}")
            except Exception as e:
                log.error(f"Error downloading {file_name}: {e}")

        if missing_files and execute_sproc:
            log.info(f"Upsert data from stg tables into standard tables via sproc")
            call_stored_procedure(conn.engine, f'{DB_CONFIG["schema"]}.sp_run_all_data_ingestion')
            log.info(f"Completed upserting data from stg tables into standard tables via sproc")

        if len(downloaded_files) != orig_size:
            save_state(downloaded_files)
        log.info(f"Completed daily data file load process!")
    except Exception as e:
        log.error(f"SFTP operation failed: {e}")
        sys.exit(1)
    finally:
        sftp and sftp.close()
        conn and conn.close()

def run_backfill_for_files_download_only():
    downloaded_files = set(load_state())
    pattern = re.compile(r"(corp_issuer_recoveries|mds_usd_issuer|mds_usd_issuer_constituents)\.1D\.(\d{8})\.txt")

    try:
        sftp = sftp_connect()
        remote_files = set(sftp.listdir(REMOTE_DIR))

        # Organize files by date
        date_to_files = defaultdict(list)
        for filename in remote_files:
            match = pattern.fullmatch(filename)
            if match:
                _, date_str = match.groups()
                date_to_files[date_str].append(filename)

        for date_str in sorted(date_to_files.keys()):
            expected_set = set(tpl.format(date=date_str) for tpl in TARGET_FILES)
            not_downloaded = expected_set - downloaded_files

            # Check if any required file is missing in SFTP for this date
            missing_remote = expected_set - remote_files
            if missing_remote:
                for f in missing_remote:
                    log.error(f"Missing file in SFTP for {date_str}: {f}")
                log.warning(f"Skipping backfill for {date_str} due to missing files.")
                continue  # Skip this date, move on
            
            # Only fetch files not yet downloaded
            to_fetch = not_downloaded

            if not to_fetch:
                log.info(f"All files already downloaded for {date_str}. Skipping.")
                continue

            log.info(f"Backfilling files for {date_str}: {list(to_fetch)}")

            for file_name in to_fetch:
                try:
                    remote_path = os.path.join(REMOTE_DIR, file_name)
                    local_path = ensure_local_path(file_name, date_str)

                    if os.path.exists(local_path):
                        log.info(f"File already exists locally, skipping: {local_path}")
                        downloaded_files.add(file_name)
                        continue

                    download_file(sftp, remote_path, local_path)
                    log.info(f"Backfill success: {file_name}")
                    downloaded_files.add(file_name)

                except Exception as e:
                    log.error(f"Error backfilling {file_name}: {e}")

        save_state(downloaded_files)
        sftp.close()

    except Exception as e:
        log.critical(f"Backfill failed: {e}")
        sys.exit(1)

def load_to_db_existing_files(base_path: str, test_path: str = None):
    if os.path.isfile(base_path):
        files = [base_path]
    else:
        files = []
        for subdir, _, fs in os.walk(base_path):
            for f in fs:
                if f.endswith(".txt"):
                    files.append(os.path.join(subdir, f))

    file_map = defaultdict(dict)
    for file in files:
        base_filename = os.path.basename(file)
        for target_pattern in TARGET_FILES:
            regex_pattern = re.escape(target_pattern).replace(r'\{date\}', r'(\d{8})')
            match = re.fullmatch(regex_pattern, base_filename)
            if match:
                date_str = match.group(1)
                file_map[date_str][target_pattern] = file
                break

    # Step 3: process in date order
    for date_str in sorted(file_map.keys()):
        conn = None
        for target_pattern in TARGET_FILES:
            file = file_map[date_str].get(target_pattern)
            if file:
                table_name = f"stg_{target_pattern.split('.')[0]}"
                log.info(f"Load file: {file} to table: {table_name}")
                conn = write_file_to_stg_table(file, table_name)
                log.info(f"Completed file load: {file} to table: {table_name}")
        if conn and not test_path:
            log.info("Upsert data from stg tables into standard tables via sproc")
            call_stored_procedure(conn.engine, f'{DB_CONFIG["schema"]}.sp_run_all_data_ingestion')
            log.info("Completed upserting data from stg tables into standard tables via sproc")
            conn.close()

    return conn  

# -------------- ENTRY POINT ----------------

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="SFTP Daily File Puller")
    parser.add_argument('--date', help="Optional YYYYMMDD override for backfilling", type=str)
    parser.add_argument('--backfill_files', help="Fetch all historical missing files", action='store_true')
    parser.add_argument('--load_to_db', help="Ingest all previously downloaded files to DB", action='store_true')
    parser.add_argument('--test_function', help="Tests a custom function", action='store_true')
    parser.add_argument("--dataset", help="dataset name", type=str, required=True)

    args = parser.parse_args()

    fpath = os.environ.get('JGDATA_PATH')
    if fpath is None:
        raise ValueError(f"Missing JGDATA_PATH Environment Variable")
    config_secret=read_toml(f'{fpath}/conf/sources/.secret.toml')
  
    ##If you find the credentials in the secret.toml, use that.
    key_to_check = args.dataset+".kwargs"

    try:
        if key_to_check in config_secret:
            SFTP_USERNAME = config_secret[key_to_check]['username']
            SFTP_PASSWORD = config_secret[key_to_check]['password']
            DB_CONFIG['user'] = config_secret[key_to_check]['postgres_fe_risk_username']
            DB_CONFIG['password'] = config_secret[key_to_check]['postgres_fe_risk_password']
        else:
            raise ValueError(f"No credentials found for: {key_to_check} - Check the secret toml file.")

        if args.test_function:
            #You can upload a absolute file or put a folder directory here and this will fetch all child items
            conn = None
            file_paths = [
                ['/jfs/tech1/apps/rawdata/MSCI/risk/data_files/mds_usd_issuer_constituents/2025/20250523/',
                 '/jfs/tech1/apps/rawdata/MSCI/risk/data_files/mds_usd_issuer/2025/20250523/',
                 '/jfs/tech1/apps/rawdata/MSCI/risk/data_files/corp_issuer_recoveries/2025/20250523/'],
            ]
            for file_path in file_paths:
                log.info(f"Loading data for one set at a time!")
                for each_set in file_path:
                    log.info(f"Loading data for file set: {each_set}")
                    conn = load_to_db_existing_files(each_set, 1)
                log.info(f"Upsert data from tables into standard tables")
                call_stored_procedure(conn.engine, f'{DB_CONFIG["schema"]}.sp_run_all_data_ingestion')
                log.info(f"Completed upserting data from stg tables into standard tables via sproc")
                conn.close()
        elif args.load_to_db:
            load_to_db_existing_files(DATA_BASE_DIR)
        elif args.backfill_files:
            run_backfill_for_files_download_only()
        else:
            target_date = args.date if args.date else get_today_str()
            daily_download_missing_files(date_str=target_date)
    except Exception as e:
        log.exception(f"Fatal error in pipeline: {e}")
        sys.exit(1)