raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/emerging_textiles"  ## Location of Raw Files
  s3_prefix: "emerging_textiles" ## Internal S3path to files

  structure: '[
    "yarns_$DATE$.csv",
    "fibers_$DATE$.csv"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "emerging_textiles"

  table_map:
    YARNS_RAW:
      pattern: "yarns_$DATE$.csv" ## Need to be a regex format
      col_num: 157
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "emerging_textiles/" ##<stage name>/<stage path>
      file_format: "FF_EMERGING_TEXTILES_YARNS"

    FIBERS_RAW:
      pattern: "fibers_$DATE$.csv" ## Need to be a regex format
      col_num: 129
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "emerging_textiles/" ##<stage name>/<stage path>
      file_format: "FF_EMERGING_TEXTILES_FIBERS"