from confluent_kafka import Producer
from typing import Optional
import jglib.infra.python.fileio as fio
import socket
import time
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger(__name__)

config_secret = fio.read_config_secrets()

current_hostname = socket.gethostname()
print(f"Current hostname: {current_hostname}")


bootstrap_server = config_secret["kafka_bootstrap_url"]
user_name = config_secret["kafka_username"]
password = config_secret["kafka_password"]
    

class SingletonMeta(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super().__call__(*args, **kwargs)
            cls._instances[cls] = instance
        return cls._instances[cls]

class KafkaPublisher(metaclass=SingletonMeta):
    def __init__(self, instance_id = ""):
        if instance_id in ("AA", "prod"):
            bootstrap_server = config_secret["kafka_bootstrap_prod"]
        else:
            bootstrap_server = config_secret["kafka_bootstrap_url"]
        self.conf = {
                "bootstrap.servers": bootstrap_server,
                "security.protocol": "SASL_SSL",
                "sasl.mechanisms": "SCRAM-SHA-512",
                "enable.ssl.certificate.verification": False,
                "sasl.username": config_secret["kafka_username"],
                "sasl.password": config_secret["kafka_password"],
                'acks': 'all',
                'retries': 5,
                'retry.backoff.ms': 500,
                'linger.ms': 50,  # Increase linger time to batch messages
                'batch.size': 32768,  # Increase batch size
                'compression.type': 'gzip',
            }
        self.producer: Optional[Producer] = None
   
    def get_producer(self) -> Producer:
        if not self.producer:
            self.producer = Producer(self.conf)
        return self.producer
   
    def publish_messages(self, topic, key, body):
        producer = self.get_producer()
        producer.produce(topic=topic, key=key, value=body, callback=self.delivery_report)
        producer.poll(0)
 
    def delivery_report(self, err, msg):
        if err is not None:
            logger.error(f'Message delivery failed to topic {msg.q()} [partition {msg.partition()} @ offset {msg.offset()}]: {err.str()}')
        
    def flush(self):
        if self.producer:
            self.producer.flush()
