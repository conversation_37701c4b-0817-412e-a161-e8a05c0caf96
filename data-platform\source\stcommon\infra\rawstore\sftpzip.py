from stcommon.infra.rawstore import split_fpath
from stcommon.infra.rawstore.sftp import SftpLoader
import stcommon.tools.aws as aws
import zipfile, logging, os
from pathlib import PurePosixPath

class SftpZipLoader(SftpLoader):
    def __init__(self, extract_zip: bool = True, remove_zip: bool = False, **kwargs):
        super().__init__(**kwargs)
        self.extract_zip = extract_zip
        self.remove_zip = remove_zip
        self.tmp_dir = f"/var/tmp/{os.environ.get('USER')}"

    def _handle_s3_zip(self, s3_path, fpath):
        # Parse S3 URI
        bucket = s3_path.split('/')[2]
        
        # Safely compose the S3 key path
        base_path = '/'.join(s3_path.split('/')[3:])
        key = str(PurePosixPath(base_path) / fpath)
        
        # Use custom temp directory
        os.makedirs(self.tmp_dir, exist_ok=True)
        temp_zip = os.path.join(self.tmp_dir, 'temp.zip')
        extract_dir = os.path.join(self.tmp_dir, 'extracted')
        os.makedirs(extract_dir, exist_ok=True)
        
        try:
            # Download zip from S3 using aws.s3download
            logging.info(f"Downloading {key} from S3 bucket {bucket}")
            aws.s3download(bucket, key, temp_zip)
            
            # Extract zip
            with zipfile.ZipFile(temp_zip, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
                
            # Upload extracted files back to S3 using aws.s3put
            for root, _, files in os.walk(extract_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, extract_dir)
                    s3_key = os.path.join(os.path.dirname(key), relative_path)
                    logging.info(f"Uploading {relative_path} to S3 {s3_key}")
                    aws.s3put(file_path, bucket, s3_key, overwrite=True)
            
            # Optionally remove original zip from S3
            if self.remove_zip:
                logging.info(f"Removing zip file from S3: {key}")
                aws.s3delete(bucket, key, dryrun=False)
                
        finally:
            # Cleanup temp files
            if os.path.exists(temp_zip):
                os.remove(temp_zip)
            if os.path.exists(extract_dir):
                import shutil
                shutil.rmtree(extract_dir)

    def sync(self, conn, fentry, fpath_source=True):
        super().sync(conn, fentry, fpath_source)
        
        if not self.extract_zip:
            return
            
        fpath, fname = split_fpath(fentry)
        if not fname.endswith('.zip'):
            return
            
        if self.s3target_local_dir:
            # Handle local directory case
            full_path = os.path.join(self.s3target_local_dir, fpath, fname)
            try:
                with zipfile.ZipFile(full_path, 'r') as zip_ref:
                    extract_path = os.path.dirname(full_path)
                    logging.info(f"Extracting {full_path} to {extract_path}")
                    zip_ref.extractall(extract_path)
                if self.remove_zip:
                    os.remove(full_path)
            except Exception as e:
                logging.error(f"Failed to extract {full_path}: {str(e)}")
        elif self.target.startswith('s3://'):
            # Handle S3 case
            try:
                self._handle_s3_zip(self.target, fentry)
            except Exception as e:
                logging.error(f"Failed to process S3 zip file: {str(e)}")
