USE ROLE SYSADMIN ;


CREATE DATABASE WSHORIZON_UAT;

USE DATABASE WSHORIZON_UAT;

----------------------
-- ROLES
----------------------
USE ROLE SECURITYADMIN;

DROP ROLE IF EXISTS DR_WSHORIZON_UAT_READ_ONLY;
DROP ROLE IF EXISTS DR_WSHORIZON_UAT_READ_WRITE;
DROP ROLE IF EXISTS DR_WSHORIZON_UAT_DB_OWNER;


CREATE ROLE IF NOT EXISTS DR_WSHORIZON_UAT_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_WSHORIZON_UAT_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_WSHORIZON_UAT_DB_OWNER;

GRANT ROLE DR_WSHORIZON_UAT_DB_OWNER TO ROLE FR_DATA_PLATFORM_LEADS;
GRANT ROLE DR_WSHORIZON_UAT_READ_WRITE TO ROLE FR_DATA_PLATFORM;

USE ROLE SYSADMIN ;

----------------------
--DB OWNERSHIP
----------------------
GRANT OWNERSHIP ON DATABASE WSHORIZON_UAT TO ROLE DR_WSHORIZON_UAT_DB_OWNER;

----------------------
--- FUTURE STATEMENTS 
----------------------
USE ROLE SECURITYADMIN;

----
--SCHEMA  FUTURE STATEMENTS
----
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE WSHORIZON_UAT TO ROLE DR_WSHORIZON_UAT_READ_WRITE;
GRANT CREATE TABLE, CREATE VIEW ON FUTURE SCHEMAS IN DATABASE WSHORIZON_UAT TO ROLE DR_WSHORIZON_UAT_READ_WRITE;

----
--FUTURE GRANTS READ WRITE ROLE
----
GRANT INSERT, UPDATE, DELETE, TRUNCATE ON FUTURE TABLES IN DATABASE WSHORIZON_UAT TO ROLE DR_WSHORIZON_UAT_READ_WRITE;

----
--FUTURE GRANTS READ ROLE
----
GRANT SELECT ON FUTURE TABLES IN DATABASE WSHORIZON_UAT TO ROLE DR_WSHORIZON_UAT_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN DATABASE WSHORIZON_UAT TO ROLE DR_WSHORIZON_UAT_READ_ONLY;


----
--Roll Read Only into Read Write Role
----
GRANT ROLE DR_WSHORIZON_UAT_READ_ONLY TO ROLE DR_WSHORIZON_UAT_READ_WRITE;

-- FUTURE OWNERSHIP TO OWNER ROLE(ALWAYS)
--Ensure that the owner role is always the owner of schemas
GRANT OWNERSHIP ON FUTURE SCHEMAS IN DATABASE WSHORIZON_UAT TO ROLE DR_WSHORIZON_UAT_DB_OWNER;


USE ROLE DR_WSHORIZON_UAT_DB_OWNER;

----------------------
--DB USAGE
----------------------
GRANT USAGE ON DATABASE WSHORIZON_UAT TO ROLE DR_WSHORIZON_UAT_READ_WRITE;
GRANT USAGE ON DATABASE WSHORIZON_UAT TO ROLE DR_WSHORIZON_UAT_READ_ONLY;

----------------------
--CREATE DBT SCHEMAS
----------------------
CREATE SCHEMA WSHORIZON_UAT.STAGE;
CREATE SCHEMA WSHORIZON_UAT.INTEGRATION;
CREATE SCHEMA WSHORIZON_UAT.EVENTS;

-----
--READ ROLE ONLY USAGE ON FINAL SCHEMA
-----
GRANT USAGE ON SCHEMA WSHORIZON_UAT.EVENTS TO ROLE DR_WSHORIZON_UAT_READ_ONLY;


