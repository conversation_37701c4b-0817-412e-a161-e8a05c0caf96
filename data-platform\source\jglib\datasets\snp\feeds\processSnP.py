import pandas as pd
import os

def processSnPETFsBulk(path):
    return_dict = {}
    fileNames= [path+'/Summary.txt',path+'/ETFHeader.txt',path+'/Basket.txt',path+'/FXRate.txt',path+'/Constituent.txt']
    for fileName in fileNames:
        if os.path.getsize(fileName) > 0:
            df = pd.read_csv(fileName, sep='\t', low_memory=False, on_bad_lines='skip')
            if df.empty:
                print(f"Skipping empty file: {fileName} - No Data available")
            else:
                df['lenFile']=[len(i) for i in df['fileName'].str.split("_")]
                cnt3=len(df.loc[df['lenFile'] == 3])
                if cnt3 > 0:
                    df.loc[df['lenFile'] == 3, "fileDate"]=(df['fileName'].str.split("_",expand=True)[2]).str.split(".",expand=True)[0]
                cnt5=len(df.loc[df['lenFile'] == 5])
                if cnt5 > 0:
                    df.loc[df['lenFile'] == 5, "fileDate"]=(df['fileName'].str.split("_",expand=True)[3]).str.split(".",expand=True)[0]
                if "SecurityID" in df.columns:
                    df["SecurityID"] = df["SecurityID"].astype("Int64")
                return_dict[fileName.split(".txt")[0].split("/")[-1]] = df
        else:
            print(f"Skipping empty file: {fileName}")
    return return_dict

def generateFiles(data_path):
    header_conditions = {
        "IsHeaderOnly": "Summary",
        "SharesOutstanding": "ETFHeader",
        "Basket": "Basket",
        "FromCurrencyCode": "FXRate",
        "AdjustedOpenPrice": "Constituent"
    }

    record_conditions = {
        "S": "Summary",
        "H": "ETFHeader",
        "B": "Basket",
        "F": "FXRate",
        "C": "Constituent"
    }
    print("Generating process files")
    os.chdir(data_path)
    print(f"Data Path: {data_path}")
    os.system('egrep "^X" ETF_*.txt|sed -e \'s/.*\\.txt:/fileName\\t/g\' | tr -d \'\r\' | sort | uniq > Headers.txt')

    for header_condition, target_file in header_conditions.items():
        os.system(f'grep "{header_condition}" Headers.txt > {target_file}.txt')

    for record_condition, target_file in record_conditions.items():
        os.system(f'egrep "^{record_condition}" ETF_*.txt|sed -e\'s/\\.txt:/\\.txt\\t/g\' >> {target_file}.txt')
        print(f"Generated: {data_path}/{target_file}.txt")
