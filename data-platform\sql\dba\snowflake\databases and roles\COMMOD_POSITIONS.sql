----------------------
-- ROLES
----------------------
USE ROLE SECURITYADMIN;


CREATE ROLE IF NOT EXISTS DR_COMMOD_POSITIONS_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_COMMOD_POSITIONS_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_COMMOD_POSITIONS_DB_OWNER;

GRANT ROLE DR_COMMOD_POSITIONS_DB_OWNER TO ROLE FR_DATA_PLATFORM_LEADS ;
GRANT ROLE DR_COMMOD_POSITIONS_READ_WRITE TO ROLE DR_COMMOD_POSITIONS_DB_OWNER;
GRANT ROLE DR_COMMOD_POSITIONS_READ_ONLY TO ROLE DR_COMMOD_POSITIONS_READ_WRITE;

GRANT ROLE DR_COMMOD_POSITIONS_READ_WRITE TO USER SVC_CTRM;
GRANT ROLE DR_COMMOD_POSITIONS_READ_ONLY TO ROLE  FR_COMMOD_TECH;


USE ROLE SYSADMIN ;

CREATE DATABASE COMMOD_POSITIONS;

USE DATABASE COMMOD_POSITIONS;

----------------------
--DB OWNERSHIP
----------------------
GRANT OWNERSHIP ON DATABASE COMMOD_POSITIONS TO ROLE DR_COMMOD_POSITIONS_DB_OWNER;
GRANT USAGE ON DATABASE COMMOD_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_ONLY;
GRANT USAGE ON DATABASE COMMOD_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_WRITE;

----------------------
--- FUTURE STATEMENTS 
----------------------
USE ROLE SECURITYADMIN;

-- FUTURE OWNERSHIP TO OWNER ROLE(ALWAYS)
--Ensure that the owner role is always the owner of schemas
GRANT OWNERSHIP ON FUTURE SCHEMAS IN DATABASE COMMOD_POSITIONS TO ROLE DR_COMMOD_POSITIONS_DB_OWNER;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE COMMOD_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_ONLY;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE COMMOD_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_WRITE;


----
--FUTURE GRANTS READ WRITE ROLE
----
GRANT SELECT,INSERT, UPDATE, DELETE, TRUNCATE ON FUTURE TABLES IN DATABASE COMMOD_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_WRITE;
GRANT SELECT ON FUTURE VIEWS IN DATABASE COMMOD_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_WRITE;

----
--FUTURE GRANTS READ ROLE
----
GRANT SELECT ON FUTURE TABLES IN DATABASE COMMOD_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN DATABASE COMMOD_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_ONLY;



USE ROLE DR_COMMOD_POSITIONS_DB_OWNER;


CREATE SCHEMA COMMOD_POSITIONS.PHYS_TPT_POSITIONS;

GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA COMMOD_POSITIONS.PHYS_TPT_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_WRITE;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD_POSITIONS.PHYS_TPT_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_WRITE;


GRANT SELECT ON FUTURE TABLES IN SCHEMA COMMOD_POSITIONS.PHYS_TPT_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD_POSITIONS.PHYS_TPT_POSITIONS TO ROLE DR_COMMOD_POSITIONS_READ_ONLY;
