import logging
import pandas as pd
import os, sys, argparse
from glob import glob
import snowflake.connector
from datetime import datetime, timedelta
import tempfile
import time
import re

logger = logging.getLogger()
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root_temp = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.append(project_root_temp)
logging.basicConfig(encoding='utf-8', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',datefmt='%Y-%m-%d %H:%M:%S')

from strunner import *
setupEnvironment()
config_root= os.environ.get('JGDATA_PATH')
sys.path.append(config_root)
import jgdata
import jglib.infra.python.fileio as fio
from jgdata.datasets.bloomberg.util.parser import parse
from stcommon.email_util_k8s import EmailUtility
from stcommon.infra.python.fileio import read_toml
from jgdata.datasets.bloomberg import getPath

def sf_query(conn,query):
    logging.getLogger('snowflake.connector').setLevel(logging.WARNING)
    cs = conn.cursor()
    cs.execute(query)
    cs.close()

TABLE_STYLE = """
                <style>
                    table {
                        border-collapse: collapse;
                        width: auto; /* Auto width based on content */
                        max-width: 100%; /* Ensures it doesn't overflow */
                        font-size: 12px; /* Small font */
                    }
                    th {
                        background-color: #4CAF50; /* Green header */
                        color: white;
                        padding: 5px;
                        text-align: left;
                        border: 1px solid #ddd;
                    }
                    td {
                        padding: 5px;
                        text-align: left;
                        # border: 1px solid #ddd;
                        border: 1px solid black; 
                    }
                    tr:nth-child(odd) {
                        background-color: #3bdbed; 
                    }
                    tr:nth-child(even) {
                        background-color: #7ae8f5; 
                    }
                </style>
                """
                

def format_dataframe_html(df, title):
    return f"<strong>{title}:</strong><br>" + TABLE_STYLE + df.to_html(index=False, escape=False) + "<br><br>"

def raise_exceptions_alerts(conn,DATABASE_GSC,DATABASE_ST,date,email):
    ##TODO: Put the exceptions in the exceptions table once they stabilize. For now just the plain query
    sql = f"""
        WITH MSCI AS (
        SELECT BARRAID, SEDOL, ISIN FROM MSCI.BARRA.ASSETIDENTITY_GL WHERE CURRENT_DATE() BETWEEN STARTDATE AND ENDDATE
        UNION
        SELECT BARRAID, SEDOL, ISIN FROM MSCI.BARRA.ASSETIDENTITY_NA WHERE CURRENT_DATE() BETWEEN STARTDATE AND ENDDATE
        UNION
        SELECT BARRAID, SEDOL, ISIN FROM MSCI.BARRA.ASSETIDENTITY_EU WHERE CURRENT_DATE() BETWEEN STARTDATE AND ENDDATE
        UNION
        SELECT BARRAID, SEDOL, ISIN FROM MSCI.BARRA.ASSETIDENTITY_AS_JPN WHERE CURRENT_DATE() BETWEEN STARTDATE AND ENDDATE
        UNION
        SELECT BARRAID, SEDOL, ISIN FROM MSCI.BARRA.ASSETIDENTITY_AS_APAC WHERE CURRENT_DATE() BETWEEN STARTDATE AND ENDDATE),
        GSC AS (
        SELECT mkch.BARRA_ID BARRAID, mkch.SEDOL_ID SEDOL, inst.ISIN_ID ISIN
        FROM   goldensource_uat.refined.gsc_market_instrument_characteristics mkch
        JOIN   GOLDENSOURCE_UAT.refined.gsc_financial_instrument inst
        on     inst.instrument_key_sok = mkch.instrument_key_sok 
        WHERE  mkch.data_warehouse_status_num = 1
        AND    inst.data_warehouse_status_num = 1
        ),
        FINAL AS (
        SELECT MSCI.BARRAID MSCI_BARRAID, MSCI.SEDOL MSCI_SEDOL, MSCI.ISIN MSCI_ISIN, 
            GSC.BARRAID GSC_BARRAID, GSC.SEDOL GSC_SEDOL, GSC.ISIN GSC_ISIN,
            CASE WHEN COALESCE(MSCI_BARRAID,'') <> COALESCE(GSC_BARRAID,'') THEN TRUE ELSE FALSE END BARRAID_MISSING_IN_GSC,
            CASE WHEN COALESCE(MSCI_SEDOL,'') <> COALESCE(GSC_SEDOL,'')     THEN TRUE ELSE FALSE END SEDOL_DIFFERENT,
            CASE WHEN COALESCE(MSCI_ISIN,'') <> COALESCE(GSC_ISIN,'')       THEN TRUE ELSE FALSE END ISIN_DIFFERENT
        FROM   MSCI
        LEFT JOIN GSC
        ON    MSCI.BARRAID = GSC.BARRAID
        )
            ;
    """
    logger.info(sql)
    df = pd.read_sql(sql, conn)
    # print(df)

    email_util = EmailUtility()
    body = (
        "Hi team,<br><br>Here are the BBG To GSC Comparison Results.<br>"
    )

    if df is not None and not df.empty:
        # body += format_dataframe_html(df, "Exception Details")
        with tempfile.NamedTemporaryFile(mode='w+', suffix='.csv', delete=False) as tmp_file:
            temp_path = tmp_file.name  # full path to the temp CSV
            df.to_csv(temp_path, index=False)
            logger.info(f"Saved file {temp_path}")

        email_util.send_email(
            to_recipient=email.split(','),
            subject=f"[BBG_TO_GSC_CHECKS][ERROR] Exception Report for {DATABASE_GSC} {date}",
            body=body,
            df=None,
            attachment=temp_path
        )

if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog=__name__, description='ST Checks')
    parser.add_argument('-d', '--date', required=True, help="date in YYYYMMDD format")
    parser.add_argument('-c', '--config', required=True, help="config should uat or prod")
    args = parser.parse_args()

    objconfig = {}
    objconfig = fio.read_config_secrets()
    config_path = config_root + "/conf/sources/st_check.toml"
    config = read_toml(config_path)

    # config = toml.load(config_path)
    config_curr = config['config'][args.config]

    email=config_curr['email_bbg']

    DATABASE_ST=config_curr['database_st']
    DATABASE_GSC=config_curr['database_gsc']
    DATABASE_SM=config_curr['database_sec_mast']
    SCHEMA_ST="ST_CHECK"
    SCHEMA_GSC_ENRICHMENT="GSC_ENRICHMENT"
    current_year_str = str(datetime.now().year)
    
    conn = snowflake.connector.connect(
        user = objconfig['sf_user'],
        password = objconfig['sf_password'],
        account = objconfig['sf_account'],
        warehouse = objconfig['sf_data_platform_wh'],
        database = DATABASE_ST,
        schema = SCHEMA_ST,
        role = objconfig['sf_data_platform_role']
    )

    raise_exceptions_alerts(conn,DATABASE_GSC,DATABASE_ST,latest_date_str,email)
    conn.close()
