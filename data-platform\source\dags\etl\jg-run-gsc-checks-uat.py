from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
import os, sys, pendulum, subprocess, logging, pytz
from airflow.providers.http.operators.http import HttpOperator
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup
from airflow.timetables.trigger import CronTriggerTimetable

current_file_dir = os.path.dirname(os.path.abspath(__file__))

sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()
import stcommon.tools.dates as dates
JGDATA_PATH = os.environ.get("JGDATA_PATH")

# Default DAG arguments
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.yesterday(tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

# Python callable to run st_checks process
def run_gsc_checks(config: str, program: str = "gsc_to_st_checks_equities.uat.py", region: str = "", nowait: bool = True):

    today = datetime.now().strftime("%Y%m%d")
    command = f'python3 {JGDATA_PATH}/bin/{program} --date {today} --config {config} --region "{region}"'
    if nowait:
        command += " --nowait"

    try:
        logging.info(f"Running command: {command}")
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

# Define the DAG
with DAG(
    dag_id="jg-run-gsc-checks-eu-uat",
    description="This DAG loads ST Data and runs checks",
    default_args=default_args,
    schedule=CronTriggerTimetable('40 7 * * 1-5',timezone="Europe/London"),
    max_active_runs = 1,
    catchup=True,
    tags=["jgdata", "gsc_checks"]
) as dag:

    PythonOperator(
        task_id=f"run_gsc_checks_eu",
        python_callable=run_gsc_checks,
        op_kwargs={
            "config": "uat",
            "region": "EU"
            }
    )

with DAG(
    dag_id="jg-run-gsc-checks-na-uat",
    description="GSC checks with --nowait=False at 8 AM EST",
    default_args=default_args,
    schedule=CronTriggerTimetable("0 8 * * 1-5", timezone="America/New_York"),  # 8:00 AM EST
    max_active_runs=1,
    catchup=True,
    tags=["jgdata", "gsc_checks", "nowait_false"]
) as dag_8am:

    PythonOperator(
        task_id="run_gsc_checks_na",
        python_callable=run_gsc_checks,
        op_kwargs={
            "config": "uat",
            "region": "NA",
            "nowait": False
        }
    )