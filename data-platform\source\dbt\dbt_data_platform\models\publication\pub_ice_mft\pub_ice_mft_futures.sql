{{ config(alias="futures", materialized="view", tags=["ice","mft","futures"]) }}

select
    'EOD' AS source,
    TRADE_DATE AS trade_date,
    null AS expiration_date,
    '' AS market_id,
    HUB AS hub,
    LONG_NAME AS long_name,
    COMMODITY AS commodity,
    STRIP AS strip,
    '' as contract_type,
    '' as strike,
    SETTLEMENT_PRICE AS settlement_price,
    null as net_change,
    PRODUCT AS product,
    '' AS relative_period,
    LOW_PRICE as low_price,
    HIGH_PRICE as high_price,
    OPEN_PRICE as open_price,
    CLOSE_PRICE as close_price,
    TOTAL_VOLUME as total_volume,
    OPEN_INTEREST as open_interest,
    EFP_VOLUME as efp_volume,
    EFS_VOLUME as efs_volume,
    BLOCK_VOLUME as block_volume,
    SPREAD_VOLUME as spread_volume,
    MIC AS mic,
    FILE_FLAG  AS file_flag,
    FNAME_SHORT as fnme_short,
    FILENAME as filename,
    START_SCAN_TIME as start_scan_time
from {{ ref("int_ice_mft_futures") }}
UNION ALL
select
    'UTIL MKT' AS source,
    CAST(SETTLEMENT_PRICE_DATE AS DATE) AS trade_date,
    null AS expiration_date,
    MARKET_ID AS market_id,
    HUB AS hub,
    PRODUCT AS long_name,
    COMMODITY_CODE AS commodity,
    STRIP AS strip,
    CONTRACT_TYPE AS contract_type,
    '' as strike,
    SETTLEMENT_PRICE AS settlement_price,    
    null as net_change,
    PRODUCT_ID AS product,
    RELATIVE_PERIOD AS relative_period,
    null as low_price,
    null as high_price,
    null as open_price,
    null as close_price,
    null as total_volume,
    null as open_interest,
    null as epp_volume,
    null as efds_volume,
    null as block_volume,
    null as spread_volume,
    MIC AS mic,
    file_flag,
    FNAME_SHORT as fnme_short,
    FILENAME as filename,
    START_SCAN_TIME as start_scan_time
from {{ ref("int_ice_mft_utilmkt_futures") }}