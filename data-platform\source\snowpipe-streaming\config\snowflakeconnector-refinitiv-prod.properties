name=snowpipe_refinitiv_prod_poc
rest.port=8300
connector.class=com.snowflake.kafka.connector.SnowflakeSinkConnector
topics=refinitiv-tba-realtime-api
snowflake.private.key.passphrase=sf_msk
snowflake.database.name=REFINITIV
snowflake.schema.name=TBA
snowflake.topic2table.map=refinitiv-tba-realtime-api:MSK_REFINITIV_TBA_MARKETPRICE_RT_PROD
snowflake.url.name=byb06077.us-east-1.snowflakecomputing.com
snowflake.user.name=JSVC_MSK
snowflake.private.key=<PRIVATE_KEY>
snowflake.role.name=JG_MSK
snowflake.ingestion.method=snowpipe_streaming
value.converter.schemas.enable=false
jmx=true
key.converter=org.apache.kafka.connect.storage.StringConverter
value.converter=org.apache.kafka.connect.json.JsonConverter
errors.tolerance=all
offset.storage.file.filename=/tmp/connect.offsets
offset.storage.topic=connect-offsets
enable.auto.commit=false
offset.flush.interval.ms=500
tasks.max=3
buffer.count.records=1
buffer.flush.time=5
buffer.size.bytes=200
snowflake.streaming.max.client.lag=1
consumer.override.auto.offset.reset=latest
consumer.override.fetch.min.bytes=1
consumer.override.fetch.max.wait.ms=5