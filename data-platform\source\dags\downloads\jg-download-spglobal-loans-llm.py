from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.timetables.trigger import CronTriggerTimetable
from airflow.hooks.base import BaseHook
from datetime import timedelta, datetime
import os, sys, pendulum, subprocess, logging, requests
from util.validate import check_for_anomalies

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()

def download_and_validate():
    run_spglobal_loans_script()  # try download

    # Fetch the Airflow connection by conn_id
    conn = BaseHook.get_connection("vendor_http")

    # Build full URL using connection details + endpoint
    endpoint = "getJFSFeedAvailabilitySatusDailyDownload/jg-download-spglobal-loans-llm"
    base_url = f"{conn.host}"
    if conn.port:
        base_url += f":{conn.port}"
    full_url = f"{base_url}/{endpoint}"

    # Set headers (you can also pull these from conn.extra if stored there)
    headers = {"Content-Type": "application/json"}

    # Make the request
    response = requests.get(full_url, headers=headers)

    # Validate response
    try:
        success, message = check_for_anomalies(response)
        if success:
            logging.info(message)
    except Exception as e:
        logging.error("Validation failed: %s", str(e))
        raise e

    return "success"

def run_spglobal_loans_script():

    JGDATA_PATH = os.environ.get("JGDATA_PATH")
    
    ny_tz = pendulum.timezone("America/New_York")
    current_date_ny = datetime.now(ny_tz) 

    if current_date_ny.weekday() == 0:
        file_date = (current_date_ny - timedelta(days=3)).strftime('%Y-%m-%d')
    else:
        file_date = (current_date_ny - timedelta(days=1)).strftime('%Y-%m-%d')



    command = (
        f'python3 {JGDATA_PATH}/bin/download.py --dataset spglobal.loans.llm --date {file_date}'
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.warning("Command failed with exit status %d", e.returncode)
        logging.warning("STDOUT: %s", e.stdout)
        logging.warning("STDERR: %s", e.stderr)
        raise


default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2025, 4, 18, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 11,
    "retry_delay": timedelta(minutes=5)
}

dag = DAG(
    dag_id="jg-download-spglobal-loans-llm",
    description="This DAG runs file download for S&P Loans llm",
    default_args=default_args,
    schedule = CronTriggerTimetable('0 2 * * 1-5', timezone='America/New_York'),
    tags=["phdata", "spglobal", "loans"],
    catchup=False
)

file_upload_job = PythonOperator(
    task_id="download_and_validate",
    python_callable=download_and_validate,
    execution_timeout=timedelta(minutes=40),

    dag=dag
)


file_upload_job