apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: loki
  labels:
    app: loki
    release: loki
spec:
  replicas: 1
  selector:
    matchLabels:
      app: loki
      release: loki
  serviceName: loki-headless
  template:
    metadata:
      labels:
        app: loki
        name: loki
        release: loki
    spec:
      containers:
      - command:
        - /bin/sh
        - -c
        - /etc/loki/loki-start.sh && tail -f /dev/null
        image: #ACCOUNT_ID#.dkr.ecr.#AWS_REGION#.amazonaws.com/#PREFIX#-loki-cwiqfs-boltdb-#ENV#:latest
        imagePullPolicy: Always
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - echo Shutting down gracefully; sleep 10
        livenessProbe:
          failureThreshold: 5
          httpGet:
            path: /metrics
            port: 3100
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 20
          successThreshold: 1
          timeoutSeconds: 1
        name: loki
        ports:
        - containerPort: 3100
          name: http-metrics
          protocol: TCP
        - containerPort: 9095
          name: grpc
          protocol: TCP
        - containerPort: 7946
          name: memberlist-port
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /ready
            port: 3100
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        securityContext:
          capabilities:
            add:
            - SYS_ADMIN
          privileged: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /var/tmp
          name: tmp
        - mountPath: /var/tmp/boltdb-shipper-compactor
          name: loki-boltdb-shipper-compactor
        - mountPath: /var/tmp/boltdb-shipper-active
          name: loki-boltdb-shipper-active
        - mountPath: /var/tmp/boltdb-shipper-cache
          name: loki-boltdb-shipper-cache
        - mountPath: /var/tmp/chunks
          name: loki-chunks
        - mountPath: /etc/loki/config
          name: config
        - mountPath: /data
          name: storage
        - mountPath: /lib/modules
          name: modules
          readOnly: true
        - mountPath: /dev/fuse
          name: fuse
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccountName: loki
      terminationGracePeriodSeconds: 60
      volumes:
      - name: tmp
        persistentVolumeClaim:
          claimName: loki-var-tmp-vol
      - name: loki-boltdb-shipper-compactor
        persistentVolumeClaim:
          claimName: loki-boltdb-shipper-compactor
      - name: loki-boltdb-shipper-active
        persistentVolumeClaim:
          claimName: loki-boltdb-shipper-active
      - name: loki-boltdb-shipper-cache
        persistentVolumeClaim:
          claimName: loki-boltdb-shipper-cache
      - name: loki-chunks
        persistentVolumeClaim:
          claimName: loki-chunks
      - configMap:
          defaultMode: 420
          name: loki-config
        name: config
      - emptyDir: {}
        name: storage
      - hostPath:
          path: /lib/modules
          type: Directory
        name: modules
      - hostPath:
          path: /dev/fuse
          type: CharDevice
        name: fuse
  updateStrategy:
    type: RollingUpdate