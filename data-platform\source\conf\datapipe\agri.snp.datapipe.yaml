raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/usda_agri"
  s3_bucket: "jg-data-dp-vendor-data"
  s3_prefix: "usda-agri"
  include_prefix: true

  structure: '[
   "snp_data_US_$DATE$.csv",
   "snp_data_Global_$DATE$.csv"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "USDA_COMMOD"

  table_map:
  
    SNP_GLOBAL_DATA_RAW:
      pattern: "^snp_data_Global_$DATE$.csv" 
      col_num: 29
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 

    SNP_US_DATA_RAW:
      pattern: "^snp_data_US_$DATE$.csv" 
      col_num: 29
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 




  

    
    




    