import logging
import snowflake.connector as sc
import os

logging.getLogger().setLevel(logging.INFO)


class Snowflake:
    def __init__(self,keypair=False):
        conn_params = {
            "account": os.environ.get("SNOWFLAKE_ACCOUNT"),
            "user": os.environ.get("SNOWFLAKE_USER"),
            "warehouse": os.environ.get("SNOWFLAKE_DEFAULT_WAREHOUSE"),
            "database": os.environ.get("SNOWFLAKE_DEFAULT_DATABASE"),
            "schema": os.environ.get("SNOWFLAKE_DEFAULT_SCHEMA"),
            "role": os.environ.get("SNOWFLAKE_ROLE"),
        }

        if keypair:
            logging.info(f"Using key to authentication")
            conn_params["private_key_file"] = os.environ.get("SNOWFLAKE_PRIVATE_KEY_PATH")
        else:
            logging.info(f"Using password to authentication")
            conn_params["password"] = os.environ.get("SNOWFLAKE_PASSWORD")

        ctx = sc.connect(**conn_params)
        self.cursor = ctx.cursor()

    def put_file_table_stage(self, file_path, db_name, schema_name, table_name):
        logging.info(f"Sending file {file_path} to table stage {table_name}")
        cmd = f"PUT file://{file_path} @{db_name}.{schema_name}.%{table_name}"
        logging.info(cmd)
        self.cursor.execute(cmd)

    def remove_all_files(self, db_name, schema_name, table_name):
        cmd = f"REMOVE  @{db_name}.{schema_name}.%{table_name}"
        self.cursor.execute(cmd)

    def list_all_files(self,stage_path, pattern=None):
        cmd = f"List {stage_path}"

        if pattern:
            cmd = f"{cmd} pattern='{pattern}'"

    
        logging.info(cmd)
        result = self.cursor.execute(cmd).fetchall()

        ## from result return only first column with file name and just the file nane
        ## query will return
        ## s3://s3-bucket/path/to/file/FILE_NAME.CSV
        ## function will return
        ## FILE_NAME.CSV
        return [rec[0].split("/")[-1] for rec in result]

    def copy_from_select(self, db_name, schema_name, table_name, select_query):

        qry = f"""
            copy into {db_name}.{schema_name}.{table_name}
            from ({select_query})
            purge = TRUE
        """
        logging.info(qry)
        result = self.cursor.execute(qry).fetchall()
        logging.info(result)

    def copy_from_file (self,
        db_name,
        schema_name,
        table_name,
        stage_path,
        file_name,
        file_format,
        metadata_alias,
        metadata_cols=None):
        qry = f"""
            copy into {db_name}.{schema_name}.{table_name}
            from {stage_path}{file_name}
            FILE_FORMAT = '{db_name}.{schema_name}.{file_format}'
            MATCH_BY_COLUMN_NAME = CASE_INSENSITIVE 
            $metadata$
            purge = TRUE
        """
        if metadata_cols:   
           qry_metadata = ",".join([f"{i[0]}=metadata${i[1]}" for i in list(zip(metadata_alias,metadata_cols))])
           qry = qry.replace('$metadata$',f'INCLUDE_METADATA= ({qry_metadata})')

        logging.info(qry)
        result = self.cursor.execute(qry).fetchall()
        logging.info(result)

    def genereate_file_query(
        self,
        db_name,
        schema_name,
        stage_path,
        file_name,
        file_format,
        number_cols,
        metadata_cols=None,
    ):

        qry_rows = ",".join([f"${i}" for i in range(1, number_cols + 1, 1)])

        if metadata_cols:
            qry_metadata = ",".join([f"metadata${i}" for i in metadata_cols])

            qry_rows = f"{qry_rows},{qry_metadata}"

        qry_select = f"""Select
                    {qry_rows}
                    from {stage_path}{file_name}
                    (FILE_FORMAT => '{db_name}.{schema_name}.{file_format}')
        """
        return qry_select

    def process(self, database_name,schema_name,table_name, table_params):
        number_cols = table_params.get("col_num")
        table_alias = table_params.get("table_alias")
        if table_alias:
            table_name = table_alias
        metadata_cols = table_params.get("metadata_columns")
        metadata_alias = table_params.get("metadata_alias")
        file_format = table_params.get("file_format")
        parse_header = table_params.get("parse_header")
        pattern = table_params.get("pattern")
        stage_path = f'@{os.environ.get("SNOWFLAKE_STAGE_NAME")}/{table_params.get("stage_path")}'

        all_files = self.list_all_files(stage_path, pattern)

        for file in all_files:

            if parse_header:
                self.copy_from_file( 
                    database_name,
                    schema_name,
                    table_name,
                    stage_path,
                    file,
                    file_format,
                    metadata_alias,
                    metadata_cols
                )
            else:
                qry = self.genereate_file_query(
                    database_name,
                    schema_name,
                    stage_path,
                    file,
                    file_format,
                    number_cols,
                    metadata_cols,
                )
                self.copy_from_select(database_name, schema_name, table_name, qry)
