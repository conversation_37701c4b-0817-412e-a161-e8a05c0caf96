import numpy as np
import pandas as pd
from datetime import date
from loguru import logger as log
from datetime import datetime
import pytz

from strunner import *
setupEnvironment()

from stcommon.infra.rds.snowflake_operation import *
from stcommon.email_util_k8s import EmailUtility

try:
    utc_now = datetime.now(pytz.utc)
    et_now = utc_now.astimezone(pytz.timezone('US/Eastern'))
    et_date = et_now.strftime('%Y-%m-%d')
    obj_sf = SnowflakeDML("IVYDBUS")
    sf_query=f"""SELECT CONVERT_TIMEZONE('GMT', 'America/New_York', DATA_UPDATED) AS TIME_EST
                FROM IVYDBUS.PUBLIC.LOADER_STATUS
                WHERE TABLE_NAME = 'OPTION_PRICE'
                AND LOAD_STATUS = 'True'
                AND LOAD_TYPE = 'PATCH'
                AND DATE(TIME_EST) = '{et_date}'
                ORDER BY DATA_UPDATED DESC
            """
    df = obj_sf.fetch_query(sf_query)
    
    options_df = obj_sf.fetch_query(f"SELECT COUNT(*) FROM IVYDBUS.PUBLIC.OPTION_PRICE WHERE DATE = '{et_date}'")

    if df is not None and options_df is not None:
        email_util = EmailUtility()
        is_exception_present = df.empty or options_df.iloc[0,0] == 0
    
        body = (
            f"Hi team,<br><br>IVYDB data is available for the date '{et_date}'.<br>"
            if not is_exception_present
            else f"Hi team,<br><br>The data quality check for IVYDB data load has failed. Data is not available for the date '{et_date}'.<br><br>"
        )

        if is_exception_present:
            email_util.send_email(
                to_recipient=["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"],
                subject=f"[{'Success' if not is_exception_present else 'Failure'}] Data Quality Check Report for IVYDB",
                body=body,
                df=None
            )
            log.error(f"[ERROR] Data Quality Check failure for IVYDB")
except Exception as e:
    log.error("Unexpected error on running the DQ check: {}".format(str(e)))
    raise e  
