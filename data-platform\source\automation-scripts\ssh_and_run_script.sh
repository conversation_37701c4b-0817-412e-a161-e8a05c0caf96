#!/bin/bash

# Set the path to the YAML config file, the SSH private key, and the remote script location
CONFIG_FILE_PATH="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/conf/log_archieve.yaml"
SSH_KEY_PATH="/jfs/tech1/apps/datait/jg-code/secure/private_key/id_rsa"
SCRIPT_LOCATION="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/archieve_logs.sh"

echo "Checking paths..."
echo "Config file path: $CONFIG_FILE_PATH"
echo "SSH key path: $SSH_KEY_PATH"

# Check if the paths are empty or if the files don't exist
if [[ -z "$CONFIG_FILE_PATH" || -z "$SSH_KEY_PATH" || ! -f "$CONFIG_FILE_PATH" || ! -f "$SSH_KEY_PATH" ]]; then
    echo "Usage: Config file or SSH key path is incorrect."
    exit 1
fi

# Read the config file once using yq
mapfile -t servers < <(yq eval '.servers | keys | .[]' "$CONFIG_FILE_PATH")

# Loop through each server
for server in "${servers[@]}"; do
    server_ip=$(yq eval ".servers.$server.ip" "$CONFIG_FILE_PATH")

    # Skip if no IP found
    if [[ -z "$server_ip" ]]; then
        echo "Skipping $server: No IP found"
        continue
    fi

    base_dirs=$(yq eval ".servers.$server.base_dirs[]" "$CONFIG_FILE_PATH" | paste -sd "," -)
    dest_dir=$(yq eval ".servers.$server.dest_dir" "$CONFIG_FILE_PATH")

    # Validate required fields
    if [[ -z "$base_dirs" || -z "$dest_dir" ]]; then
        echo "Skipping $server: Missing base_dirs or dest_dir"
        continue
    fi

    echo "Connecting to $server ($server_ip)..."
    ssh -i "$SSH_KEY_PATH" \
        -o StrictHostKeyChecking=no \
        -o UserKnownHostsFile=/dev/null \
        -o ConnectTimeout=5 \
        $server_ip <<EOF
        echo "Running cleanup on $server_ip"
        bash "$SCRIPT_LOCATION" "$base_dirs" "$dest_dir"
EOF
    echo "Completed cleanup on $server_ip"
done
