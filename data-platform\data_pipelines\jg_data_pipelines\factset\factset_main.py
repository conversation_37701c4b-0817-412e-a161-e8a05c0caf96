import os
import sys
import logging
import tempfile
import zipfile
import pandas as pd
import argparse
sys.path.append(os.getcwd())
import json
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '../../../source/bin')))
from strunner import *
setupEnvironment()
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)

from utils.file import temp_folder
from datetime import datetime
from utils.file import temp_folder


configPath = os.environ.get('CONFIG_PATH', os.getcwd())
with open(f'{configPath}/config.json', 'r') as f:
    config = json.load(f)

def jg_config_path():
    return config["JG_CONFIG_PATH"]

def read_config_secrets():
    config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
    with open(config_secret_path, 'r') as f:
        config_secret = json.load(f)
    return config_secret
objconfig = read_config_secrets()

os.environ['SF_USERNAME'] = objconfig['sf_user']
os.environ['SF_PASSWORD'] = objconfig['sf_password']
os.environ['SF_DATABASE'] = objconfig['factset_database']
os.environ['SF_WAREHOUSE'] = objconfig['factset_warehouse']
os.environ['SF_ROLE'] = objconfig['factset_role']
os.environ['FACTSET_FTP_PASSWORD'] = objconfig['factset_ftp_password']

from factset.factset_utils import (find_minimum_unprocessed_file_version, 
                                   find_incremental_file_versions_to_process, 
                                   stage_inc_file_to_sf,
                                   truncate_staging,
                                   copy_staged_file_into_staging,
                                   merge_into_core,
                                   get_format_for_version_regex,
                                   get_last_processed_version)


from factset.factset_configs import (ESTIMATES_ADVANCED_QF_VARS,
                                     ESTIMATES_ADVANCED_AF_VARS,
                                     ESTIMATES_ADVANCED_SAF_VARS,
                                     ESTIMATES_SEC_HUB_VARS,
                                     FUNDAMENTAL_BASIC_VARS,
                                     FUNDAMENTAL_BASIC_DER_VARS,
                                     FUNDAMENTAL_ADVANCED_VARS,
                                     FUNDAMENTAL_ADVANCED_DER_VARS,
                                     FUNDAMENTAL_SEC_HUB_VARS,
                                     JFS_ESTIMATES_ADVANCED_CON_DIR, 
                                     JFS_FACTSET_BASIC_DIR,
                                     JFS_FACTSET_BASIC_DER_DIR,
                                     JFS_FACTSET_ADVANCED_DIR,
                                     JFS_FACTSET_ADVANCED_DER_DIR,
                                     SYMBOL_SEDOL_HIST_VARS,
                                     SYMBOL_TICKER_REGION_HIST_VARS,
                                     SYM_COVERAGE_VARS,
                                     SYM_BBG_VARS,
                                     JFS_FACTSET_SYMBOL_TICKER_REGION_HIST,
                                     JFS_FACTSET_SYMBOL_SEDOL_HIST,
                                     JFS_FACTSET_SYM_COVERAGE,
                                     JFS_FACTSET_SYM_BBG,
                                     JFS_ESTIMATES_SEC_HUB_DIR,
                                     JFS_FUNDAMENTAL_SEC_HUB_DIR,
                                     FactsetDataType)

from factset.factset_bulk_loader import SnowflakeBulkLoader
import concurrent.futures

import warnings
# Suppress specific warning
warnings.filterwarnings("ignore", category=UserWarning, message="pandas only supports SQLAlchemy.*")

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
MAX_WORKERS = 15

def process_file(file_info,jfs_dir,factsetDataType):
    inc_file_name, inc_file_version = file_info
    logger.info(f"Staging file into Snowflake: {inc_file_name}, {inc_file_version}")
    
    num_columns = stage_inc_file_to_sf(jfs_dir, inc_file_version, inc_file_name, factsetDataType)
    
    logger.info(f"Copying file into Snowflake Staging Schema: {inc_file_name}, {inc_file_version}")
    copy_staged_file_into_staging(jfs_dir, inc_file_version, inc_file_name, factsetDataType, num_columns)

def process_factset_files(jfs_dir, factset_vars, factsetDataType: FactsetDataType,load_type="INC",inputversion=None):
    last_processed_version = get_last_processed_version(factsetDataType) or 0
    
    files = os.listdir(jfs_dir)
    if load_type=="FULL":
        if inputversion:
            version=inputversion
        else:
            version = find_minimum_unprocessed_file_version(files, factset_vars["full_file_regex"],last_processed_version)
        full_version = version if version else 1

        if full_version is not None:
            full_file_format = get_format_for_version_regex(factset_vars["full_file_regex"])
            file_name = full_file_format.format(version=full_version)
            logger.info(f"Full file to be processed: {file_name}")
        else:
            logger.warning("No unprocessed factsets full files found")
        process_file((file_name,version),jfs_dir,factsetDataType)
        last_processed_version=int(version)
    
        logger.info(f"Starting Incremental Load")
        files_to_process = find_incremental_file_versions_to_process(files, factset_vars["incremental_file_regex"], last_processed_version)
        # files_to_process=[('fe_advanced_conh_saf_am_v4_2.zip',2),('fe_advanced_conh_saf_am_v4_3.zip',3),('fe_advanced_conh_saf_am_v4_4.zip',4),('fe_advanced_conh_saf_am_v4_5.zip',5),('fe_advanced_conh_saf_am_v4_6.zip',6),('fe_advanced_conh_saf_am_v4_7.zip',7),('fe_advanced_conh_saf_am_v4_8.zip',8),('fe_advanced_conh_saf_am_v4_9.zip',9),('fe_advanced_conh_saf_am_v4_10.zip',10)]
        logger.info(f"Found {len(files_to_process)} new files to be processed")
        # try:
        #truncate_staging(factsetDataType)
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            futures = [executor.submit(process_file, file_info, jfs_dir,factsetDataType) for file_info in files_to_process]
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"Error processing file: {e}")
        merge_into_core(load_type, factsetDataType)

    if load_type=="INC":
        if not last_processed_version:
            raise ValueError("No last processed version found for factsets")
        logger.info(f"Starting Incremental Load")
        files_to_process = find_incremental_file_versions_to_process(files, factset_vars["incremental_file_regex"], last_processed_version)
        logger.info(f"Found {len(files_to_process)} new files to be processed")
        for file_info in files_to_process:
            inc_file_name, inc_file_version = file_info
            process_file(file_info,jfs_dir,factsetDataType)
            merge_into_core(load_type, factsetDataType,inc_file_version)

# Parameters
parser = argparse.ArgumentParser(prog=__name__, description='Factset files')
parser.add_argument('-f', '--filetype', required=True, help="file type saf/af/qf/sym_tkr_reg_hist/sym_sed_hist")
parser.add_argument('--fullprocess', action='store_true', help="Load the full file first")
parser.add_argument('-v', '--fullversion', required=False, help="full version number to be loaded")
args = parser.parse_args()

dispatch_map = {
    'fe_saf': (ESTIMATES_ADVANCED_SAF_VARS, FactsetDataType.ESTIMATES_SAF_ADVANCED_CON,JFS_ESTIMATES_ADVANCED_CON_DIR),
    'fe_af':  (ESTIMATES_ADVANCED_AF_VARS, FactsetDataType.ESTIMATES_AF_ADVANCED_CON,JFS_ESTIMATES_ADVANCED_CON_DIR),
    'fe_qf':  (ESTIMATES_ADVANCED_QF_VARS, FactsetDataType.ESTIMATES_QF_ADVANCED_CON,JFS_ESTIMATES_ADVANCED_CON_DIR),
    'fe_sec':  (ESTIMATES_SEC_HUB_VARS, FactsetDataType.ESTIMATES_SEC_HUB,JFS_ESTIMATES_SEC_HUB_DIR),
    'sym_tkr_reg_hist': (SYMBOL_TICKER_REGION_HIST_VARS, FactsetDataType.SYMBOL_TICKER_REGION_HIST,JFS_FACTSET_SYMBOL_TICKER_REGION_HIST),
    'sym_sed_hist':  (SYMBOL_SEDOL_HIST_VARS, FactsetDataType.SYMBOL_SEDOL_HIST,JFS_FACTSET_SYMBOL_SEDOL_HIST),
    'sym_coverage':  (SYM_COVERAGE_VARS, FactsetDataType.SYM_COVERAGE,JFS_FACTSET_SYM_COVERAGE),
    'sym_bbg':  (SYM_BBG_VARS, FactsetDataType.SYMBOL_BBG,JFS_FACTSET_SYM_BBG),
    'ff_basic': (FUNDAMENTAL_BASIC_VARS, FactsetDataType.FUNDAMENTAL_BASIC,JFS_FACTSET_BASIC_DIR),
    'ff_basic_der': (FUNDAMENTAL_BASIC_DER_VARS, FactsetDataType.FUNDAMENTAL_BASIC_DERIVED,JFS_FACTSET_BASIC_DER_DIR),
    'ff_advanced': (FUNDAMENTAL_ADVANCED_VARS, FactsetDataType.FUNDAMENTAL_ADVANCED,JFS_FACTSET_ADVANCED_DIR),
    'ff_advanced_der': (FUNDAMENTAL_ADVANCED_DER_VARS, FactsetDataType.FUNDAMENTAL_ADVANCED_DERIVED,JFS_FACTSET_ADVANCED_DER_DIR),
    'ff_sec':  (FUNDAMENTAL_SEC_HUB_VARS, FactsetDataType.FUNDAMENTAL_SEC_HUB,JFS_FUNDAMENTAL_SEC_HUB_DIR),
}

if __name__ == "__main__":
    
    if args.filetype in dispatch_map:
        vars_, datatype_, jfs_dir_ = dispatch_map[args.filetype]
        
        if args.fullprocess:
            process_factset_files(jfs_dir_, vars_, datatype_,"FULL",args.fullversion)
        else:
            process_factset_files(jfs_dir_, vars_, datatype_,"INC")

    
