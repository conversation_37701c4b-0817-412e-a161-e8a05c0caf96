-- Steps followed are as documented here:
-- https://mydataoutlet.freshdesk.com/support/solutions/articles/***********-first-time-installation-for-snowflake-admins

CREATE WAREHOUSE IF NOT EXISTS MDO_WH
    WAREHOUSE_SIZE = 'X-SMALL'
    AUTO_SUSPEND = 60
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;


CREATE WAREHOUSE IF NOT EXISTS MDOWAREHOUSE
    WAREHOUSE_SIZE = 'X-SMALL'
    AUTO_SUSPEND = 60
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE MDO_WH TO ROLE ACCOUNTADMIN; 

declare
APPNAME VARCHAR default 'MDOSDK';--the default application name is MDOSDK

-- This template was broken when running the setup, so we could not use the MDO_WH, and had to use MDOWAREHOUSE instead.
WHNAME VARCHAR default 'MDOWAREHOUSE'; --the default warehouse to create is MDOWAREHOUSE
SQLSTMT VARCHAR;

begin
USE ROLE ACCOUNTADMIN;
SQLSTMT := 'CALL "'|| :APPNAME ||'".PUBLIC.USP_CREATE_MDO_CUSTOM_DATABASE_INITIAL()';
EXECUTE IMMEDIATE :SQLSTMT;
SQLSTMT := 'GRANT CREATE STREAMLIT ON SCHEMA "'|| :APPNAME ||'_CUSTOM_DATA".PUBLIC TO ROLE ACCOUNTADMIN';
EXECUTE IMMEDIATE :SQLSTMT;

SQLSTMT := 'GRANT EXECUTE TASK ON ACCOUNT TO APPLICATION "'|| :APPNAME ||'"';
EXECUTE IMMEDIATE :SQLSTMT;

SQLSTMT := 'GRANT EXECUTE MANAGED TASK ON ACCOUNT TO APPLICATION "'|| :APPNAME ||'"';
EXECUTE IMMEDIATE :SQLSTMT;

SQLSTMT := 'CREATE WAREHOUSE IF NOT EXISTS "'|| :WHNAME ||'" WITH WAREHOUSE_SIZE = ''LARGE''';
EXECUTE IMMEDIATE :SQLSTMT;

SQLSTMT :=
'
CREATE OR REPLACE TASK "'||:APPNAME ||'_CUSTOM_DATA".PUBLIC.POST_PROCESS_'||:APPNAME||' WAREHOUSE  = '''|| :WHNAME || '''
AS
EXECUTE IMMEDIATE
$$
DECLARE
SQLSTMT VARCHAR;
begin
SQLSTMT := (CALL "' || :APPNAME|| '".PUBLIC.USP_POSTPROC_SCRIPTS());
execute immediate :SQLSTMT;
END;
$$;';
EXECUTE IMMEDIATE :SQLSTMT;

SQLSTMT := 'GRANT USAGE ON DATABASE "' || :APPNAME|| '_CUSTOM_DATA" TO ROLE ACCOUNTADMIN';
EXECUTE IMMEDIATE :SQLSTMT;

SQLSTMT := 'GRANT USAGE ON SCHEMA "' || :APPNAME|| '_CUSTOM_DATA".PUBLIC TO ROLE ACCOUNTADMIN';
EXECUTE IMMEDIATE :SQLSTMT;

SQLSTMT := 'EXECUTE TASK "' || :APPNAME|| '_CUSTOM_DATA".PUBLIC.POST_PROCESS_'||:APPNAME;
EXECUTE IMMEDIATE :SQLSTMT;

SQLSTMT := 'GRANT OPERATE ON TASK "' || :APPNAME|| '_CUSTOM_DATA".PUBLIC.POST_PROCESS_'||:APPNAME || ' TO APPLICATION "'||:APPNAME || '"';
EXECUTE IMMEDIATE :SQLSTMT;
return 'POST PROCESS TASK HAS BEEN SETUP';
end;