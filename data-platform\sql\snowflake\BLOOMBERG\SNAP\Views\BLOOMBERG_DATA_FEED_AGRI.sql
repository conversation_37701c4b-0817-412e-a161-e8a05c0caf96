create or replace view BLOOMBERG.SNAP.BLOOMBERG_DATA_FEED_AGRI(
	SNAP_DATE,
	IDENTIFIER,
	ATTRIBUTE,
	VALUE,
	AVAILDATE,
	REF_DATE
) as
SELECT jqt.SNAP_DATE, jqt.IDENTIFIER, jqt.ATTRIBUTE, jqt.VALUE, jqt.AVAILDATE, jqt.REF_DATE
FROM BLOOMBERG.SNAP.DATA_FEED jqt 
JOIN COMMOD.AGRI_SUGAR.AGRI_BLOOMBERG_TICKERS jdc
ON jqt.IDENTIFIER = jdc.IDENTIFIER and jqt.attribute = jdc.field;


GRANT SELECT ON view BLOOMBERG.SNAP.BLOOMBERG_DATA_FEED_AGRI TO FR_COMMOD_AGRI_PM;
