import requests 

# Replace with your desired MLINK URL 
# MLINK_PROD_URL = 'https://mlink-live.nms.saturn.spiderrockconnect.com/rest/json'
MLINK_PROD_URL = "https://mlink-delay.nms.saturn.spiderrockconnect.com/rest/json"

# Replace with your MLINK API Key
API_KEY = '05A91D09-35FA-419A-AF2B-829AAA682404'

# Replace with your desired MsgType.  
MSG_TYPE = 'LiveImpliedQuote'

# Replace with your desired view. A "|" separated list of views can be provided
# If no view is provided, all views will be returned
VIEW = 'ticker|uPrc|uOff|years|xAxis|rate|sdiv|ddiv|oBid|oAsk|oBidIv|oAskIv|atmVol|sVol|sPrc|sMark|veSlope|de|ga|th|ve|va|vo|ro|ph|deDecay|up50|dn50|up15|dn15|up06|dn08|synSpot|priceType|calcErr|calcSource|srcTimestamp|netTimestamp|timestamp'

# Replace with your desired where clause.
# a string in the form "field1:eq:value" or "(field1:ne:value1 & field1:ne:value2)
# "WHERE" clauses can contain the following comparison symbols:
# :gt: is greater than
# :ge: is greater than or equal to
# :lt: is less than
# :le: is less than or equal to
# :eq: is equal
# :ne: is not equal
# %26 is an AND statement
# | is an OR statement
# :sw: is starts with
# :ew: is ends with
# :cv: is contains values
# :nv: is does not contain value
# :cb: is contained between (two dates for instance) separated by '$'
# WHERE = ''

# Replace with your desired limit of how many messages you receive. The default limit is 500
LIMIT = 500

# Order clause eg. "(field1:DESC | field1:ASC | field2:DESC:ABS | field2:ASC:ABS" (default is unordered; default is faster)
ORDER = 'ticker:ASC'

# Request Parameters for getmsgs Of The MsgType
params = {
    # Required Parameters
    "apiKey": API_KEY,
    "cmd": 'getmsgs',
    "msgType": MSG_TYPE,
    # Optional Parameters
    "view": VIEW, 
    # "where": WHERE,
    "limit": LIMIT,
    "order": ORDER
}

response = requests.get(MLINK_PROD_URL, params=params)
if response.status_code != 200:
    raise Exception(f"Error: {response.status_code}, {response.text}")

data = response.json()
print(data)
