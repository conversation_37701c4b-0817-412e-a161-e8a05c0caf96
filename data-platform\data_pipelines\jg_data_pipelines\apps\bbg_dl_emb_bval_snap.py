import os
import pandas as pd
from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType

from bloomberg.per_security.parser import BloombergParser
from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from utils.postgres.adaptor import PostgresAdaptor
from utils.date_utils import get_today, get_now


def get_dld_converts():
    pg_host = "tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com"
    pg_database = "postgres"
    pg_schema = "refdata"
    pg_username = os.environ["RDS_PROD_USERNAME"]
    pg_password = os.environ["RDS_PROD_PASSWORD"]

    pg_adaptor = PostgresAdaptor(
        host=pg_host,
        database=pg_database,
        schema=pg_schema,
        user=pg_username,
        password=pg_password,
    )
    query = """
            SELECT DISTINCT s.bbg_figi
            FROM refdata.positions p
            JOIN refdata.securities s on s.spn = p.spn
            JOIN refdata.bundle b on b.jg_bundle_id = p.bundle_id::numeric
            where b.bundle_group_name = 'DLD'
            AND s.fo_type_name in ('Convert', 'Non-convertible debt')
            AND p.date = '{today_str}'
        """.format(today_str=get_today().strftime("%Y-%m-%d"))
    df_converts = pg_adaptor.execute_query(query)

    return df_converts["bbg_figi"].tolist()


if __name__ == "__main__":

    emb_securities = ["BBG00YD9DW33", "BBG01K8B1PJ4", "BBG01K89Y3P5", "BBG01K8B22H6", "BBG01KD6JSQ6", "BBG00VSTWJ91", "BBG0117702V4", "BBG00PP1H1Q5", "BBG00NJ2Q096", "BBG00VHVNK22", "BBG01T2L56B5", "BBG01N86VDV9", "BBG01RQQTMD9"]
    dld_converts = get_dld_converts()
    all_bval_securities = list(set(emb_securities + dld_converts))

    if len(all_bval_securities) == 0:
        raise ValueError("No securities found for BVAL snapshot.")
    
    if len(all_bval_securities) > 250:
        raise ValueError("Too many securities for BVAL snapshot. Limit is 250.")

    per_sec_req_type = PerSecurityRequestType.getdata
    batch_name = "dp_bval1"

    request_dict = {
        "firm_name": "dl47544",
        "program_flag": "adhoc",
        "sec_id": "BB_GLOBAL",
        "closing_values": "yes",
        "derived": "yes",
        "bval_tier": "1",
        "bval_snapshot": "NY4PM",
        "bval_snapshot_date": get_today().strftime("%Y%m%d"),
        "output_format": "bulklist",
        "fields": ["BVAL_SNAPSHOT", "BVAL_MID_YIELD", "BVAL_MID_SCORE", "BVAL_MID_PRICE", "BVAL_MID_YTM"],
        "securities": all_bval_securities,
    }
    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/bval_4pm/"
    bval_4pm_file_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)
    print(bval_4pm_file_path)
    
    parser = BloombergParser(
        bval_4pm_file_path, sep="|", skipinitialspace=True, on_bad_lines="error"
    )
    df_data = parser.parse_data()

    df_data.drop(columns=["ERROR_CODE", "NUM_FLDS", "EMPTY_COL"], inplace=True)
    df_data.rename(columns={"SECURITIES": "BBG_IDENTIFIER"}, inplace=True)
    df_data["BVAL_MID_YIELD"] = pd.to_numeric(df_data["BVAL_MID_YIELD"], errors="coerce")
    df_data["BVAL_MID_PRICE"] = pd.to_numeric(df_data["BVAL_MID_PRICE"], errors="coerce")
    df_data["BVAL_MID_YTM"] = pd.to_numeric(df_data["BVAL_MID_YTM"], errors="coerce")
    df_data["BVAL_MID_SCORE"] = pd.to_numeric(df_data["BVAL_MID_SCORE"], errors="coerce").astype("Int64")
    df_data["BVAL_DATE"] = get_today()
    df_data["LAST_UPDATED"] = get_now()
    
    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_ONDEMAND",
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER"
        )

    ret_val = adaptor.upsert(df_data, "BVAL_SNAP_PRICES", ["BBG_IDENTIFIER", "BVAL_SNAPSHOT", "BVAL_DATE"])
    print(ret_val)
    