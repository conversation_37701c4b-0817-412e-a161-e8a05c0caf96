raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/usda_agri"
  s3_bucket: "jg-data-dp-vendor-data"
  s3_prefix: "usda-agri"
  include_prefix: true

  structure: '[
   "GetDSCI_Climate Divisions_$DATE$.csv",
   "GetDSCI_Counties_$DATE$.csv",
   "GetDroughtSeverityStatisticsByArea_Climate Divisions_$DATE$.csv",
   "GetDroughtSeverityStatisticsByArea_Counties_$DATE$.csv"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "USDA_COMMOD"


  table_map:

    CLIMATE_DIVISION_STATISTICS_RAW:
      pattern: "^GetDroughtSeverityStatisticsByArea_Climate_Divisions_$DATE$.csv" 
      col_num: 13
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 

    County_STATISTICS_RAW:
      pattern: "^GetDroughtSeverityStatisticsByArea_Counties_$DATE$.csv" 
      col_num: 14
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 

    DSCI_Climate_Division_RAW:
      pattern: "^GetDSCI_Climate_Divisions_$DATE$.csv" 
      col_num: 6
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 

    DSCI_COUNTY_RAW:
      pattern: "GetDSCI_Counties_$DATE$.csv" 
      col_num: 6
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 
