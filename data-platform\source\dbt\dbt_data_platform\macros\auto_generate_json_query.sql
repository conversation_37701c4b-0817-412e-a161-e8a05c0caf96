-- macros/auto_generate_json_query.sql
-- Macro to discover all unique keys in the payload column and generate extraction SQL


{% macro auto_generate_json_query(table_name, payload_column='payload') %}
  
  {% if execute %}
    {% set query %}
      SELECT DISTINCT f.key as column_name
      FROM {{ table_name }} t,
      LATERAL FLATTEN(input => t.{{ payload_column }}) f
      WHERE f.key IS NOT NULL
      ORDER BY f.key
    {% endset %}
    
    {% set results = run_query(query) %}
    {% if results %}
      {% set column_extractions = [] %}
      {% for row in results.rows %}
        {% set column_name = row[0] %}
        {% set clean_column_name = column_name | replace('-', '_') | replace(' ', '_') | replace('.', '_') | replace('(', '_') | replace(')', '_') | lower %}
        {% set extraction = payload_column ~ ":" ~ column_name ~ "::STRING as " ~ clean_column_name %}
        {% set _ = column_extractions.append(extraction) %}
      {% endfor %}
      {{ return(column_extractions | join(',\n    ')) }}
    {% else %}
      {{ return('') }}
    {% endif %}
  {% else %}
    {{ return('') }}
  {% endif %}
  
{% endmacro %}


-- macros/auto_generate_json_query_typed.sql
-- Enhanced version that also detects data types for better casting


{% macro auto_generate_json_query_typed(table_name, payload_column='payload') %}
  
  {% if execute %}
    {% set query %}
      SELECT DISTINCT 
        f.key as column_name,
        TYPEOF(f.value) as data_type,
        COUNT(*) as frequency
      FROM {{ table_name }} t,
      LATERAL FLATTEN(input => t.{{ payload_column }}) f
      WHERE f.key IS NOT NULL
      GROUP BY f.key, TYPEOF(f.value)
      ORDER BY f.key, frequency DESC
    {% endset %}
    
    {% set results = run_query(query) %}
    {% if results %}
      {% set column_extractions = [] %}
      {% set processed_columns = [] %}
      
      {% for row in results.rows %}
        {% set column_name = row[0] %}
        {% set data_type = row[1] %}
        
        {# Only process each column once (take the most frequent data type) #}
        {% if column_name not in processed_columns %}
          {% set clean_column_name = column_name | replace('-', '_') | replace(' ', '_') | replace('.', '_') | replace('(', '_') | replace(')', '_') | lower %}
          
          {# Map JSON types to appropriate SQL types #}
          {% if data_type in ['INTEGER', 'DECIMAL'] %}
            {% set sql_type = 'NUMBER' %}
          {% elif data_type == 'BOOLEAN' %}
            {% set sql_type = 'BOOLEAN' %}
          {% elif data_type == 'ARRAY' %}
            {% set sql_type = 'ARRAY' %}
          {% elif data_type == 'OBJECT' %}
            {% set sql_type = 'OBJECT' %}
          {% else %}
            {% set sql_type = 'STRING' %}
          {% endif %}
          
          {% set extraction = payload_column ~ ":'" ~ column_name ~ "'::" ~ sql_type ~ " as " ~ clean_column_name %}
          {% set _ = column_extractions.append(extraction) %}
          {% set _ = processed_columns.append(column_name) %}
        {% endif %}
      {% endfor %}
      
      {{ return(column_extractions | join(',\n    ')) }}
    {% else %}
      {{ return('') }}
    {% endif %}
  {% else %}
    {{ return('') }}
  {% endif %}
  
{% endmacro %}
