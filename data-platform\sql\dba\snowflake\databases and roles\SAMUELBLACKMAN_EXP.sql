USE ROLE SECURITYADMIN;

CREATE USER DANMIRABELLA  PASSWORD='JG_Mirabella2025' DISPLAY_NAME = '<PERSON> Mirabella' FIRST_NAME = 'Daniel' LAST_NAME = 'Mirabella' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;

CREATE USER SAMUELBLACKMAN  PASSWORD='JG_Blackman2025' DISPLAY_NAME = '<PERSON>' FIRST_NAME = 'Samuel' LAST_NAME = 'Blackman' EMAIL = 'Samuel.<PERSON><EMAIL>' MUST_CHANGE_PASSWORD = TRUE;

CREATE ROLE FR_DANMIRABELLA;
GRANT ROLE FR_DANMIRABELLA TO ROLE FR_DATA_PLATFORM_LEADS;
GRANT ROLE JG_GENERAL TO ROLE FR_DANMIRABELLA;

GRANT ROLE FR_DANMIRABELLA TO USER DANMIRABELLA ;
GRANT ROLE FR_<PERSON><PERSON><PERSON><PERSON>BELLA TO USER SAMUELBLACKMAN ;
GRANT ROLE FR_<PERSON>ANMIRABELLA TO USER PULKITVORA ;

USE ROLE SYSADMIN ;
CREATE DATABASE SAMUELBLACKMAN_EXP ;

GRANT OWNERSHIP ON DATABASE SAMUELBLACKMAN_EXP TO ROLE FR_DANMIRABELLA;
CREATE SCHEMA SAMUELBLACKMAN_EXP.POC;
GRANT OWNERSHIP ON SCHEMA SAMUELBLACKMAN_EXP.POC to ROLE FR_DANMIRABELLA;

