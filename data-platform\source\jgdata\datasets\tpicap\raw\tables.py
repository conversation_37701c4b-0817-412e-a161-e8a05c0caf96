import numpy as np
import pandas as pd
from datetime import date
from loguru import logger as log

from jgdata.datasets.tpicap.raw import *
from stcommon.infra.rds.snowflake_operation import *

def buildEODData(d, schedule_type):
    try:
        print(schedule_type)
        obj_sf = SnowflakeDML("JG_TPICAP")
        config_df = obj_sf.fetch_query("SELECT * FROM JG_TPICAP.CONFIG.TP_ICAP_CONFIG;")
        prev_bus_date = calendar.getPriorBusDate('gl',calendar.getToday('gl'))
        prev_bus_date = datetime.strptime(str(prev_bus_date), "%Y%m%d").strftime("%Y-%m-%d")
        print(f"Previous Business Date: {prev_bus_date}")
        
        for index, row in config_df.iterrows():
            if(row['TICKER_TYPE'] == 'EOD' and row['SCHEDULE_TYPE'] == schedule_type):
                df = obj_sf.fetch_query(f"SELECT COUNT(*) FROM {row['SOURCE']} WHERE RECORD like '{row['TICKER_REGEX_PATTERN']}' AND ACTIV_DATE = '{prev_bus_date}'")
                if df.iloc[0, 0] != 0:
                    sf_query = f'''INSERT INTO {row['TARGET_TABLE']} 
                                    SELECT * FROM {row['SOURCE']} 
                                    WHERE RECORD like '{row['TICKER_REGEX_PATTERN']}' AND EVENT_TIMESTAMP > (SELECT MAX(EVENT_TIMESTAMP) FROM {row['TARGET_TABLE']});
                                '''
                    print(sf_query)
                    obj_sf.execute_query(sf_query)
                else:
                    log.error("Data is missing from the vendor side")
                    raise ValueError("Data is missing from the vendor side")
        
        obj_sf.execute_query(f"CREATE OR REPLACE TABLE JG_TPICAP.EOD.FIELD_GROUP AS SELECT * from TPICAP.INFO.FIELD_GROUP;")
        obj_sf.execute_query(f"CREATE OR REPLACE TABLE JG_TPICAP.EOD.RECORDS AS SELECT * from TPICAP.INFO.RECORDS;")

    except Exception as e:
        log.error("Unexpected error on inserting the data: {}".format(str(e)))
        raise e  