#!/bin/bash

CONFIG_FILE="/config/config.yaml"
PROM_FILE="/etc/node_exporter/script_process_check.prom"
SLEEP_INTERVAL=60

while true; do
    NOW=$(date +%s)
    TEMP_FILE="/tmp/script_process_check.prom.$$"

    # Write Prometheus headers only to temp file
    {
        echo "# HELP custom_script_process_up Whether the monitoring script is running"
        echo "# TYPE custom_script_process_up gauge"
        echo "# HELP custom_script_last_checked_timestamp_seconds Last time the watchdog ran"
        echo "# TYPE custom_script_last_checked_timestamp_seconds gauge"
    } > "$TEMP_FILE"

    echo "=== $(date): Checking script processes ==="

    # Read and process each command line from YAML
    awk '
      BEGIN { in_commands=0 }
      /^commands:/ { in_commands=1; next }
      /^[^ ]/ && in_commands { exit }
      in_commands && /^[ -]/ { gsub(/^[ -]+/, "", $0); print }
    ' "$CONFIG_FILE" | sed 's/^"//; s/"$//' | while read -r full_cmd; do

        echo "🔍 Checking command: $full_cmd"

        # Extract script path (if any)
        script_path=$(echo "$full_cmd" | grep -Eo '/[^ ]+\.(sh|py)$')
        [[ -z "$script_path" ]] && script_path="$full_cmd"

        # Normalize label for Prometheus
        label_name=$(basename "$script_path" | sed 's/[^a-zA-Z0-9]/_/g')

        # Check process status
        if pgrep -f "$full_cmd" > /dev/null; then
            echo "✅ Script $label_name is running."
            echo "custom_script_process_up{script=\"$label_name\"} 1" >> "$TEMP_FILE"
        else
            echo "❌ Script $label_name is NOT running."
            echo "custom_script_process_up{script=\"$label_name\"} 0" >> "$TEMP_FILE"
        fi

        echo "custom_script_last_checked_timestamp_seconds{script=\"$label_name\"} $NOW" >> "$TEMP_FILE"
    done

    # Move the temp file atomically
    mv "$TEMP_FILE" "$PROM_FILE"
    chmod 644 "$PROM_FILE"
    echo "📦 Metrics written to $PROM_FILE"
    echo

    sleep "$SLEEP_INTERVAL"
done
