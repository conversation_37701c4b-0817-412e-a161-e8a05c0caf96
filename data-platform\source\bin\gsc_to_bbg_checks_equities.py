import logging
import pandas as pd
import os, sys, argparse
from glob import glob
import snowflake.connector
from datetime import datetime, timedelta
import tempfile
import time
import re

logger = logging.getLogger()
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root_temp = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.append(project_root_temp)
logging.basicConfig(encoding='utf-8', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',datefmt='%Y-%m-%d %H:%M:%S')

from strunner import *
setupEnvironment()
config_root= os.environ.get('JGDATA_PATH')
sys.path.append(config_root)
import jgdata
import jglib.infra.python.fileio as fio
from jgdata.datasets.bloomberg.util.parser import parse
from stcommon.email_util_k8s import EmailUtility
from stcommon.infra.python.fileio import read_toml
from jgdata.datasets.bloomberg import getPath

def sf_query(conn,query):
    logging.getLogger('snowflake.connector').setLevel(logging.WARNING)
    cs = conn.cursor()
    cs.execute(query)
    cs.close()

def loadBloombergListings(conn,DATABASE,SCHEMA,filename):
    stage_name = f'@{DATABASE}.{SCHEMA}.BBG_LISTINGS'
    target_table = f'{DATABASE}.{SCHEMA}.BBG_LISTINGS'
    put_command = f"PUT file://{filename} {stage_name} AUTO_COMPRESS=TRUE"
    logger.info(f"Uploading: {filename}")
    sf_query(conn,put_command)
    # Step 2: Copy from stage into table
    copy_command = f"""
        COPY INTO {target_table}
        FROM {stage_name}
        FILE_FORMAT = (TYPE = 'CSV',SKIP_HEADER=1,FIELD_OPTIONALLY_ENCLOSED_BY='"');
    """
    sf_query(conn,copy_command)
    logger.info("Data loaded successfully.")


TABLE_STYLE = """
                <style>
                    table {
                        border-collapse: collapse;
                        width: auto; /* Auto width based on content */
                        max-width: 100%; /* Ensures it doesn't overflow */
                        font-size: 12px; /* Small font */
                    }
                    th {
                        background-color: #4CAF50; /* Green header */
                        color: white;
                        padding: 5px;
                        text-align: left;
                        border: 1px solid #ddd;
                    }
                    td {
                        padding: 5px;
                        text-align: left;
                        # border: 1px solid #ddd;
                        border: 1px solid black; 
                    }
                    tr:nth-child(odd) {
                        background-color: #3bdbed; 
                    }
                    tr:nth-child(even) {
                        background-color: #7ae8f5; 
                    }
                </style>
                """
                

def format_dataframe_html(df, title):
    return f"<strong>{title}:</strong><br>" + TABLE_STYLE + df.to_html(index=False, escape=False) + "<br><br>"

def raise_exceptions_alerts(conn,DATABASE_GSC,DATABASE_ST,date,email):
    ##TODO: Put the exceptions in the exceptions table once they stabilize. For now just the plain query
    sql = f"""
            SELECT 'MISSING RECORDS' EXCEPTION, bbg.TICKERANDEXCHCODE, IDSEDOL1, IDISIN, IDMICLOCALEXCH, IDBBGLOBAL, NULL AS GSC_VALUE 
            FROM   {DATABASE_ST}.ST_CHECK.bbg_LISTINGS bbg
            WHERE  bbg.FILE_DATE = '{date}'
            AND    bbg.TICKERANDEXCHCODE NOT IN (SELECT COALESCE(mkch.BB_EXCHANGE_TICKER_ID,'') 
                                                FROM   {DATABASE_GSC}.refined.gsc_financial_instrument inst
                                                JOIN   {DATABASE_GSC}.refined.gsc_market_instrument_characteristics mkch 
                                                ON     inst.instrument_key_sok = mkch.instrument_key_sok
                                                WHERE  mkch.data_warehouse_status_num        = 1
                                                AND    inst.data_warehouse_status_num        = 1)
            AND   NULLIF(TRIM(IDMICLOCALEXCH),'') IS NOT NULL
            UNION ALL
            SELECT 'DUPLICATE BB_EXCHANGE_TICKER_ID(TICKERANDEXCHCODE)' EXCEPTION, bbg.TICKERANDEXCHCODE, IDSEDOL1, IDISIN, IDMICLOCALEXCH, IDBBGLOBAL, NULL AS GSC_VALUE
            FROM   {DATABASE_ST}.ST_CHECK.bbg_LISTINGS bbg
            WHERE  bbg.FILE_DATE = '{date}'
            AND    TICKERANDEXCHCODE IN (SELECT mkch.BB_EXCHANGE_TICKER_ID
                                        FROM   {DATABASE_GSC}.refined.gsc_financial_instrument inst
                                        JOIN   {DATABASE_GSC}.refined.gsc_market_instrument_characteristics mkch 
                                        ON     inst.instrument_key_sok = mkch.instrument_key_sok
                                        WHERE  mkch.data_warehouse_status_num        = 1
                                        AND    inst.data_warehouse_status_num        = 1
                                        GROUP BY mkch.BB_EXCHANGE_TICKER_ID
                                        HAVING COUNT(*) > 1)
            AND   NULLIF(TRIM(IDMICLOCALEXCH),'') IS NOT NULL
            UNION ALL
            SELECT 'SEDOL DIFFS' EXCEPTION, bbg.TICKERANDEXCHCODE, IDSEDOL1, IDISIN, IDMICLOCALEXCH, IDBBGLOBAL, COALESCE(gsc.SEDOL_ID,'') AS GSC_VALUE
            FROM   {DATABASE_ST}.ST_CHECK.bbg_LISTINGS bbg
            JOIN   {DATABASE_GSC}.refined.gsc_market_instrument_characteristics gsc 
            ON     bbg.TICKERANDEXCHCODE = gsc.BB_EXCHANGE_TICKER_ID
            WHERE  bbg.FILE_DATE = '{date}'
            AND    gsc.data_warehouse_status_num        = 1
            AND    COALESCE(NULLIF(bbg.IDSEDOL1,'N.A.'),'') <> COALESCE(gsc.SEDOL_ID,'')
            AND    TICKERANDEXCHCODE NOT IN (SELECT COALESCE(mkch.BB_EXCHANGE_TICKER_ID,'')
                                            FROM   {DATABASE_GSC}.refined.gsc_financial_instrument inst
                                            JOIN   {DATABASE_GSC}.refined.gsc_market_instrument_characteristics mkch 
                                            ON     inst.instrument_key_sok = mkch.instrument_key_sok
                                            WHERE  mkch.data_warehouse_status_num        = 1
                                            AND    inst.data_warehouse_status_num        = 1
                                            GROUP BY mkch.BB_EXCHANGE_TICKER_ID
                                            HAVING COUNT(*) > 1)
            AND   NULLIF(TRIM(IDMICLOCALEXCH),'') IS NOT NULL
            UNION ALL
            SELECT 'ISIN DIFFS' EXCEPTION, bbg.TICKERANDEXCHCODE, IDSEDOL1, IDISIN, IDMICLOCALEXCH, IDBBGLOBAL, COALESCE(inst.ISIN_ID,'') AS GSC_VALUE
            FROM   {DATABASE_ST}.ST_CHECK.bbg_LISTINGS bbg
            JOIN   {DATABASE_GSC}.refined.gsc_market_instrument_characteristics gsc
            ON     bbg.TICKERANDEXCHCODE = gsc.BB_EXCHANGE_TICKER_ID
            JOIN   {DATABASE_GSC}.refined.gsc_financial_instrument inst
            ON     inst.instrument_key_sok = gsc.instrument_key_sok
            WHERE  bbg.FILE_DATE = '{date}'
            AND    gsc.data_warehouse_status_num        = 1
            AND    inst.data_warehouse_status_num        = 1
            AND    COALESCE(NULLIF(bbg.IDISIN,'N.A.'),'') <> COALESCE(inst.ISIN_ID,'')
            AND    TICKERANDEXCHCODE NOT IN (SELECT COALESCE(mkch.BB_EXCHANGE_TICKER_ID,'')
                                            FROM   {DATABASE_GSC}.refined.gsc_financial_instrument inst
                                            JOIN   {DATABASE_GSC}.refined.gsc_market_instrument_characteristics mkch 
                                            ON     inst.instrument_key_sok = mkch.instrument_key_sok
                                            WHERE  mkch.data_warehouse_status_num        = 1
                                            AND    inst.data_warehouse_status_num        = 1
                                            GROUP BY mkch.BB_EXCHANGE_TICKER_ID
                                            HAVING COUNT(*) > 1)
            AND   NULLIF(TRIM(IDMICLOCALEXCH),'') IS NOT NULL
            ;
    """
    logger.info(sql)
    df = pd.read_sql(sql, conn)
    # print(df)

    email_util = EmailUtility()
    body = (
        "Hi team,<br><br>Here are the BBG To GSC Comparison Results.<br>"
    )

    if df is not None and not df.empty:
        # body += format_dataframe_html(df, "Exception Details")
        with tempfile.NamedTemporaryFile(mode='w+', suffix='.csv', delete=False) as tmp_file:
            temp_path = tmp_file.name  # full path to the temp CSV
            df.to_csv(temp_path, index=False)
            logger.info(f"Saved file {temp_path}")

        email_util.send_email(
            to_recipient=email.split(','),
            subject=f"[BBG_TO_GSC_CHECKS][ERROR] Exception Report for {DATABASE_GSC} {date}",
            body=body,
            df=None,
            attachment=temp_path
        )

if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog=__name__, description='ST Checks')
    parser.add_argument('-d', '--date', required=True, help="date in YYYYMMDD format")
    parser.add_argument('-c', '--config', required=True, help="config should uat or prod")
    parser.add_argument('--nowait', required=False, action='store_true', help="Don't wait for NA files to be available")
    args = parser.parse_args()

    objconfig = {}
    objconfig = fio.read_config_secrets()
    config_path = config_root + "/conf/sources/st_check.toml"
    config = read_toml(config_path)

    # config = toml.load(config_path)
    config_curr = config['config'][args.config]

    email=config_curr['email_bbg']

    DATABASE_ST=config_curr['database_st']
    DATABASE_GSC=config_curr['database_gsc']
    DATABASE_SM=config_curr['database_sec_mast']
    SCHEMA_ST="ST_CHECK"
    SCHEMA_GSC_ENRICHMENT="GSC_ENRICHMENT"
    current_year_str = str(datetime.now().year)
    
    conn = snowflake.connector.connect(
        user = objconfig['sf_user'],
        password = objconfig['sf_password'],
        account = objconfig['sf_account'],
        warehouse = objconfig['sf_data_platform_wh'],
        database = DATABASE_ST,
        schema = SCHEMA_ST,
        role = objconfig['sf_data_platform_role']
    )

    dir="equityEuroPricing"
    file_prefix = "equity_euro.px.gz"

    dir_with_path=getPath(dir)
    files = [f for f in os.listdir(dir_with_path) if f.startswith(file_prefix + ".")]
    dates = [f.replace(file_prefix + ".", "") for f in files if re.fullmatch(r"\d{8}", f.replace(file_prefix + ".", ""))]
    latest_date = max(dates) if dates else None

    file="equity_euro.px.gz."+latest_date
    logger.info(f"Getting Bloomberg data for {latest_date}")
    df = parse(dir,file)
    latest_date_str=datetime.strptime(latest_date, "%Y%m%d").strftime("%Y-%m-%d")
    df['FILE_DATE']=latest_date_str

    with tempfile.TemporaryDirectory() as temp_dir:
        df.to_csv(temp_dir+'/equity_euro.px.'+latest_date+'.csv',index=False)
        loadBloombergListings(conn,DATABASE_ST,SCHEMA_ST,temp_dir+'/equity_euro.px.'+latest_date+'.csv')
    
    raise_exceptions_alerts(conn,DATABASE_GSC,DATABASE_ST,latest_date_str,email)
    conn.close()
