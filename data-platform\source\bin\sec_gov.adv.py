import os
import time
import requests
import argparse
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging
from strunner import *
setupEnvironment()
import stcommon.tools.aws as aws
import datetime as dt
import pytz

logger = logging.getLogger()
logging.basicConfig(level=logging.INFO)


PAGE_URL = "https://www.sec.gov/foia-services/frequently-requested-documents/form-adv-data"
REPORTS_JSON_URL = "https://reports.adviserinfo.sec.gov/reports/foia/reports_metadata.json"
DOWNLOAD_PATH = "/jfs/tech1/apps/rawdata/sec_gov/adv/1.0"
S3_PATH  = "s3://jg-data-dp-vendor-data/sec_gov/adv/1.0"
ADV_URLS = {
    "advFilingData":"https://reports.adviserinfo.sec.gov/reports/foia/advFilingData/{current_year}/{file_name}",
    "advBrochures":"https://reports.adviserinfo.sec.gov/reports/foia/advBrochures/{current_year}/{file_name}",
    "advFirmCRS":"https://reports.adviserinfo.sec.gov/reports/foia/advFirmCRS/{current_year}/{file_name}",
    "advFirmCRSDocs":"https://reports.adviserinfo.sec.gov/reports/foia/advFirmCRSDocs/{current_year}/{file_name}",
    "advW":"https://reports.adviserinfo.sec.gov/reports/foia/advW/{current_year}/{file_name}",
}

HEADERS = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'en-US,en;q=0.9,en-IN;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-CH-UA': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
        'Sec-CH-UA-Mobile': '?0',
        'Sec-CH-UA-Platform': '"Linux"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1'
    }
    

def parse_adv_urls(url,date,file_name):

    current_year = str(date.year)

    return url.format(current_year=current_year, file_name=file_name)

def download_month_files(urls,date,download_folder):

    response = requests.get(REPORTS_JSON_URL, headers=HEADERS)

    reports_metadata = response.json()

    for report, url in urls.items():

        report_files = reports_metadata.get(report).get(str(date.year)).get('files')

        month_name = date.strftime("%B")

        for file in report_files:
            if month_name in file.get('displayName'): ## Somemetimes dont have only month name on displayName
                logger.info(f"Found file for {report} in {month_name}: {file.get('displayName')}")
                file_name = file.get('fileName')

                url = parse_adv_urls(url, date,file_name)
                download_zip_files(url, download_folder, HEADERS)
    
  
def download_all_hist(page_url, download_folder):

    response = requests.get(page_url, headers=HEADERS)

    if response.status_code != 200:
        logger.error("Error fetching page: Status Code {}".format(response.status_code))
        return 

    soup = BeautifulSoup(response.text, "html.parser")
    
    zip_links = [
                    urljoin(page_url, a["href"]) for a in soup.find_all("a", href=True)
                        if (a["href"].lower().endswith(".zip") or a["href"].lower().endswith(".csv") )
                 ] 
                 
    logger.info(f"found {len(zip_links)} zip and csv files.")
    
    for zip_url in zip_links:
        download_zip_files(zip_url, download_folder, HEADERS)
        time.sleep(0.5)
        
def download_zip_files(zip_url, download_folder, headers):
    
    filename = zip_url.split("/")[-1]
    file_path = os.path.join(download_folder, filename)

    if os.path.exists(file_path):
        logger.info(f"File already exists: {filename}")
        return
    logger.info(f"Downloading {zip_url} to {file_path}")
    response = requests.get(zip_url, headers=headers, stream=True)
    if response.status_code == 200:
        with open(file_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)
        logger.info(f"Downloaded: {filename}")
     
    else:
        logger.info(f"failed to download {zip_url} - status_code: {response.status_code}")

def send_to_s3(download_folder, s3_path):
    logger.info(f"Checking files to send to s3 {s3_path}")
    bucket, *target_list = s3_path.split('s3://')[1].split('/')
    for file in os.listdir(download_folder):  # Corrected variable name from 'files' to 'file'
        local_file_path = os.path.join(download_folder, file)  # Use download_folder for full path
        s3_file_path = os.path.join(*target_list, file)
        if aws.s3exists(bucket, s3_file_path):
            continue
        logger.info(f"Uploading {local_file_path} to s3://{bucket}/{s3_file_path}")
        aws.s3put(local_file_path, bucket, s3_file_path)
        logger.info(f"Uploaded {file} to S3")

def range_type(min_value, max_value):
    def range_checker(value):
        ivalue = int(value)
        if ivalue < min_value or ivalue > max_value:
            raise argparse.ArgumentTypeError(f"Value must be between {min_value} and {max_value}")
        return ivalue
    return range_checker
   
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    group = parser.add_mutually_exclusive_group()
    group.add_argument("--month", type=range_type(1, 12),help="month to current year to make download (1 to 12)")
    group.add_argument("--init", help="should dataset be initialized", action="store_true")
    options = parser.parse_args()
    os.makedirs(DOWNLOAD_PATH, exist_ok=True)

    if options.init:
        download_all_hist(PAGE_URL, DOWNLOAD_PATH)
    else:
        if not options.month:
            logger.error("Please provide a --month argument between 1 and 12 or use --init to download all historical files.")
            raise parser.error("Please provide a --month argument between 1 and 12 or use --init to download all historical files.")
        

        tz = pytz.timezone('America/New_York')
        now = dt.datetime.now(tz)
        if now.month == 1 and options.month == 12:
            year = now.year - 1
        else:
            year = now.year

        download_month_files(ADV_URLS, dt.datetime(year, options.month, 1), DOWNLOAD_PATH)
  
    send_to_s3(DOWNLOAD_PATH, S3_PATH)
    logger.info(f"All files downloaded and uploaded to S3")

