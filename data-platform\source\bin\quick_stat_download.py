import argparse
import requests
from bs4 import BeautifulSoup
import html
import csv
from strunner import *

setupEnvironment() 
from stcommon.infra.rds.snowflake_bulk_loader import SnowflakeBulkLoader

parser = argparse.ArgumentParser(description="Process previous date")

parser.add_argument("--date", required=True, help="Specify the processing date in format YYYYMMDD")
args = parser.parse_args()
current_date = args.date
path="/jfs/tech1/apps/rawdata/usda_agri"
url="https://www.nass.usda.gov/datasets/"

import gzip 
import shutil

response= requests.get(url)
if response.status_code==200:
    clean_text=response.text.encode().decode('unicode_escape')
    clean_text=html.unescape(clean_text)
    soup=BeautifulSoup(clean_text,'html.parser')
    raw_db="vendor_raw"
    commod_schema="usda_commod"
    divs = soup.find_all('div', class_='block')
    data=[]
    conn = SnowflakeBulkLoader(warehouse="DATA_PLATFORM_WH",database=raw_db,schema=f'{raw_db}.{commod_schema}',role="FR_DATA_PLATFORM")
    cur = conn.cursor
    for div in divs[1:]:
        div_text=div.get_text(strip=True)
        # CHeck if not exists in table
        file_name=div_text.split("Size")[0]
        date_str=div_text.split("modified:")[1]
        if "census" not in file_name:
            query=f"select * from {raw_db}.{commod_schema}.QUICKSTATS_METADATA where file_name_qs='{file_name}' and date_str='{date_str}'"
            res=cur.execute(query)
            if not res.fetchall():
                data.append({"file_name":file_name,'modified':date_str})
                domain="https://www.nass.usda.gov/datasets/"+file_name
                domain_res=requests.get(domain, stream=True)
                with gzip.GzipFile(fileobj=domain_res.raw) as gz_file:
                    with open(f"{path}/{file_name[:-7]}_{current_date}.txt",'wb') as f:
                        shutil.copyfileobj(gz_file,f)

                print(f"Saved: {path}/{file_name[:-7]}_{current_date}.txt")


    print(data)
else: print(f'failed to retrieve the page')

if data:
    with open(f'{path}/quickstats_metadata_{current_date}.csv', 'w', newline='', encoding='utf-8') as output_file:

        dict_writer = csv.DictWriter(output_file, fieldnames=data[0].keys())
        dict_writer.writeheader()
        dict_writer.writerows(data)

    print(f"Data saved to {path}/quickstats_metadata_{current_date}.csv")