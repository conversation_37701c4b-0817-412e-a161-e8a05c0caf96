use role ACCOUNTADMIN;

-- Database
create database COMMOD
use COMMOD

-- Warehouse
CREATE WAREHOUSE IF NOT EXISTS COMMOD_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 300         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;
use warehouse COMMOD_WH

-- Roles
CREATE ROLE IF NOT EXISTS DR_COMMOD_READER;
CREATE ROLE IF NOT EXISTS DR_COMMOD_WRITER;
CREATE ROLE IF NOT EXISTS DR_COMMOD_OWNER;

-- Grant usage on warehouse to roles
GRANT USAGE ON WAREHOUSE COMMOD_WH TO ROLE DR_COMMOD_READER;
GRANT USAGE ON WAREHOUSE COMMOD_WH TO ROLE DR_COMMOD_WRITER;
GRANT USAGE ON WAREHOUSE COMMOD_WH TO ROLE DR_COMMOD_OWNER;

-- Grant usage on database to roles
GRANT USAGE ON DATABASE COMMOD TO ROLE DR_COMMOD_READER;
GRANT USAGE ON DATABASE COMMOD TO ROLE DR_COMMOD_WRITER;
GRANT USAGE ON DATABASE COMMOD TO ROLE DR_COMMOD_OWNER;

GRANT OWNERSHIP ON SCHEMA COMMOD.PUBLIC TO ROLE DR_COMMOD_OWNER; 
GRANT USAGE ON SCHEMA COMMOD.PUBLIC TO ROLE JG_GENERAL; 

GRANT OWNERSHIP ON VIEW COMMOD.PUBLIC.PRICES_EOD TO ROLE DR_COMMOD_OWNER; 
GRANT SELECT ON VIEW COMMOD.PUBLIC.PRICES_EOD TO ROLE JG_GENERAL; 

GRANT OWNERSHIP ON VIEW COMMOD.PUBLIC.PRICES_RT TO ROLE DR_COMMOD_OWNER; 
GRANT SELECT ON VIEW COMMOD.PUBLIC.PRICES_RT TO ROLE JG_GENERAL; 

-- Service account for automation
CREATE USER SVC_COMMOD DISPLAY_NAME = 'SVC_COMMOD' DEFAULT_ROLE = DR_COMMOD_WRITER
GRANT ROLE DR_COMMOD_WRITER TO USER SVC_COMMOD;
ALTER USER SVC_COMMOD SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzK+5z/T3bvfrgYPfj9Z5
vGf/X/kf5VOJ5cprJrB0oqNvKzhibn9YglmtbOndmNbbZlgoRCaTULDnQzIicnhW
p7Do4kAg31OS26Y+3sBxhjgju+KLmrejRcjbenqY+ogPFIxWstEtJE5iwsMqQTov
TLlCrlqA5Ak+XBMIBnNz1wNNKAk+HpBxvZXLNEfJ4mmgG3duwtpjwbbLyLRQBE2R
46pHSdJG6UK1Qo/x6WOLPwLLkmCk/fmkivYauS7ebHfzMODsTPkFMEW073Bj9dFb
RJlLlK/t5N8N0bBGQILA8o51y8fHzfnojlqu5cCpkHeaHA7x423uHyDY2dZlQUy2
RwIDAQAB
-----END PUBLIC KEY-----';

CREATE ROLE IF NOT EXISTS FR_COMMOD_PM;
GRANT ROLE DR_ICE_READER TO ROLE FR_COMMOD_PM;
GRANT ROLE DR_BBG_DLPLUS_READER TO ROLE FR_COMMOD_PM;
GRANT ROLE DR_SPG_PLATTS_READER TO ROLE FR_COMMOD_PM;

-- Add members to functional role
GRANT ROLE FR_COMMOD_PM TO USER RAJIVGUPTA;
GRANT ROLE FR_COMMOD_PM TO USER RONBENCHETRIT;
GRANT ROLE FR_COMMOD_PM TO USER MANISHKUMAR;
GRANT ROLE FR_COMMOD_PM TO USER PULKITVORA;
GRANT ROLE FR_COMMOD_PM TO USER ANDRELEITHAEUSER;

-- Validations
SELECT * FROM COMMOD.information_schema.object_privileges WHERE object_name = 'PRICING';
SHOW GRANTS OF ROLE FR_COMMOD_PRICING;

CREATE SCHEMA COMMOD.AGRI_USDA;
CREATE SCHEMA COMMOD.AGRI_BRIDGETON;
CREATE SCHEMA COMMOD.AGRI_SNP;
CREATE SCHEMA COMMOD.AGRI_SUGAR;
CREATE SCHEMA COMMOD.AGRI_QUICKSTATS;
CREATE SCHEMA COMMOD.STAGE;
CREATE SCHEMA COMMOD.INTEGRATION;

GRANT ROLE DR_COMMOD_OWNER TO ROLE FR_DATA_PLATFORM_LEADS;
GRANT ROLE DR_COMMOD_WRITER TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_COMMOD_READER TO ROLE FR_COMMOD_PM;
GRANT ROLE DR_COMMOD_READER TO ROLE FR_COMMOD_AGRI_PM;

GRANT USAGE ON SCHEMA COMMOD.AGRI_USDA TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_USDA TO DR_COMMOD_READER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_USDA TO DR_COMMOD_WRITER;

GRANT USAGE ON SCHEMA COMMOD.AGRI_BRIDGETON TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_BRIDGETON TO DR_COMMOD_READER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_BRIDGETON TO DR_COMMOD_WRITER;

GRANT USAGE ON SCHEMA COMMOD.AGRI_SNP TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_SNP TO DR_COMMOD_READER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_SNP TO DR_COMMOD_WRITER;

GRANT USAGE ON SCHEMA COMMOD.AGRI_SUGAR TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_SUGAR TO DR_COMMOD_READER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_SUGAR TO DR_COMMOD_WRITER;

GRANT USAGE ON SCHEMA COMMOD.AGRI_QUICKSTATS TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_QUICKSTATS TO DR_COMMOD_READER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_QUICKSTATS TO DR_COMMOD_WRITER;

GRANT OWNERSHIP ON ALL TABLES IN SCHEMA COMMOD.AGRI_USDA TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON ALL TABLES IN SCHEMA COMMOD.AGRI_BRIDGETON TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON ALL TABLES IN SCHEMA COMMOD.AGRI_SNP TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON ALL TABLES IN SCHEMA COMMOD.AGRI_SUGAR TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON ALL TABLES IN SCHEMA COMMOD.AGRI_QUICKSTATS TO DR_COMMOD_OWNER;

GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_USDA TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_BRIDGETON TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_SNP TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_SUGAR TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_QUICKSTATS TO DR_COMMOD_OWNER;

GRANT OWNERSHIP ON ALL VIEWS IN SCHEMA COMMOD.AGRI_USDA TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON ALL VIEWS IN SCHEMA COMMOD.AGRI_BRIDGETON TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON ALL VIEWS IN SCHEMA COMMOD.AGRI_SNP TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON ALL VIEWS IN SCHEMA COMMOD.AGRI_SUGAR TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON ALL VIEWS IN SCHEMA COMMOD.AGRI_QUICKSTATS TO DR_COMMOD_OWNER;

GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA COMMOD.AGRI_USDA TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA COMMOD.AGRI_BRIDGETON TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA COMMOD.AGRI_SNP TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA COMMOD.AGRI_SUGAR TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA COMMOD.AGRI_QUICKSTATS TO DR_COMMOD_OWNER;

GRANT ALL PRIVILEGES on SCHEMA COMMOD.AGRI_USDA to role DR_COMMOD_OWNER;
GRANT ALL PRIVILEGES on SCHEMA COMMOD.AGRI_BRIDGETON to role DR_COMMOD_OWNER;
GRANT ALL PRIVILEGES on SCHEMA COMMOD.AGRI_SNP to role DR_COMMOD_OWNER;
GRANT ALL PRIVILEGES on SCHEMA COMMOD.AGRI_SUGAR  to role DR_COMMOD_OWNER;
GRANT ALL PRIVILEGES on SCHEMA COMMOD.AGRI_QUICKSTATS  to role DR_COMMOD_OWNER;

GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.AGRI_USDA TO DR_COMMOD_READER;
GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.AGRI_BRIDGETON TO DR_COMMOD_READER;
GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.AGRI_SNP TO DR_COMMOD_READER;
GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.AGRI_SUGAR TO DR_COMMOD_READER;
GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.AGRI_QUICKSTATS TO DR_COMMOD_READER;

GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.AGRI_USDA TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.AGRI_BRIDGETON TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.AGRI_SNP TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.AGRI_SUGAR TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.AGRI_QUICKSTATS TO DR_COMMOD_READER;

GRANT SELECT ON ALL TABLES  IN SCHEMA COMMOD.AGRI_USDA TO DR_COMMOD_READER;
GRANT SELECT ON ALL TABLES  IN SCHEMA COMMOD.AGRI_BRIDGETON TO DR_COMMOD_READER;
GRANT SELECT ON ALL TABLES  IN SCHEMA COMMOD.AGRI_SNP TO DR_COMMOD_READER;
GRANT SELECT ON ALL TABLES  IN SCHEMA COMMOD.AGRI_SUGAR TO DR_COMMOD_READER;
GRANT SELECT ON ALL TABLES  IN SCHEMA COMMOD.AGRI_QUICKSTATS TO DR_COMMOD_READER;

GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.AGRI_USDA TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.AGRI_BRIDGETON TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.AGRI_SNP TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.AGRI_SUGAR TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.AGRI_QUICKSTATS TO DR_COMMOD_READER;

GRANT ROLE DR_COMMOD_READER TO ROLE DR_COMMOD_WRITER;



CREATE SCHEMA COMMOD.AGRI_GTT;

GRANT USAGE ON SCHEMA COMMOD.AGRI_GTT TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_GTT TO DR_COMMOD_READER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_GTT TO DR_COMMOD_WRITER;

GRANT OWNERSHIP ON ALL TABLES IN SCHEMA COMMOD.AGRI_GTT TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_GTT TO DR_COMMOD_OWNER;
GRANT ALL PRIVILEGES on SCHEMA COMMOD.AGRI_GTT to role DR_COMMOD_OWNER;

GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.AGRI_GTT TO DR_COMMOD_READER;

GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.AGRI_GTT TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.AGRI_GTT TO DR_COMMOD_READER;


GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA COMMOD.AGRI_GTT TO ROLE DR_COMMOD_WRITER;
GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_GTT TO ROLE DR_COMMOD_WRITER;



GRANT USAGE ON SCHEMA COMMOD.STAGE TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.STAGE TO DR_COMMOD_READER;
GRANT USAGE ON SCHEMA COMMOD.STAGE TO DR_COMMOD_WRITER;

GRANT OWNERSHIP ON ALL TABLES IN SCHEMA COMMOD.STAGE TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.STAGE TO DR_COMMOD_OWNER;
GRANT ALL PRIVILEGES on SCHEMA COMMOD.STAGE to role DR_COMMOD_OWNER;

GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.STAGE TO DR_COMMOD_READER;

GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.STAGE TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.STAGE TO DR_COMMOD_READER;

GRANT USAGE ON SCHEMA COMMOD.INTEGRATION TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.INTEGRATION TO DR_COMMOD_READER;
GRANT USAGE ON SCHEMA COMMOD.INTEGRATION TO DR_COMMOD_WRITER;

GRANT OWNERSHIP ON ALL TABLES IN SCHEMA COMMOD.INTEGRATION TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.INTEGRATION TO DR_COMMOD_OWNER;
GRANT ALL PRIVILEGES on SCHEMA COMMOD.INTEGRATION to role DR_COMMOD_OWNER;

GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.INTEGRATION TO DR_COMMOD_READER;

GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.INTEGRATION TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.INTEGRATION TO DR_COMMOD_READER;

GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA COMMOD.INTEGRATION TO ROLE DR_COMMOD_WRITER;
GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA COMMOD.STAGE TO ROLE DR_COMMOD_WRITER;

create or replace table commod.integration.int_gtt_trade_details_export (
tradeflow VARCHAR,
ismirror VARCHAR,
periodtype VARCHAR,
year_start VARCHAR,
month_start VARCHAR,
year_end VARCHAR,
month_end VARCHAR,
year VARCHAR,
month VARCHAR,
reportercountryno VARCHAR,
reportercode VARCHAR,
reporterisoalpha3code VARCHAR,
reporterisonumeric3code VARCHAR,
reportername VARCHAR,
reporterdescription VARCHAR,
firstavailableperiod VARCHAR,
lastavailableperiod VARCHAR,
reportersource VARCHAR,
incoterm VARCHAR,
partnercountryno VARCHAR,
partnercode VARCHAR,
partnerisoalpha3code VARCHAR,
partnerisonumeric3code VARCHAR,
partnername VARCHAR,
hscode VARCHAR,
commoditydescriptionoriginal VARCHAR,
commoditydescriptiontranslation VARCHAR,
hs2code VARCHAR,
hs2descriptionoriginal VARCHAR,
hs2descriptiontranslation VARCHAR,
hs4code VARCHAR,
hs4descriptionoriginal VARCHAR,
hs4descriptiontranslation VARCHAR,
hs6code VARCHAR,
hs6descriptionoriginal VARCHAR,
hs6descriptiontranslation VARCHAR,
commodityexplodedhscode VARCHAR,
commodityexplodeddescriptionoriginal VARCHAR,
commodityexplodeddescriptiontranslation VARCHAR,
hseditionfrom VARCHAR,
hseditionto VARCHAR,
currency VARCHAR,
value VARCHAR,
unit1 VARCHAR,
quantity1 VARCHAR,
q1estimation VARCHAR,
unit2 VARCHAR,
quantity2 VARCHAR,
q2estimation VARCHAR,
priceunit1 VARCHAR,
price1 VARCHAR,
transport VARCHAR,
subdivision VARCHAR,
port VARCHAR,
priceunit2 VARCHAR,
price2 VARCHAR,
customsregime VARCHAR,
foreignport VARCHAR,
suppression VARCHAR,
usstate VARCHAR,
trade_date DATE
);

create or replace table commod.integration.int_gtt_trade_details_import (
tradeflow VARCHAR,
ismirror VARCHAR,
periodtype VARCHAR,
year_start VARCHAR,
month_start VARCHAR,
year_end VARCHAR,
month_end VARCHAR,
year VARCHAR,
month VARCHAR,
reportercountryno VARCHAR,
reportercode VARCHAR,
reporterisoalpha3code VARCHAR,
reporterisonumeric3code VARCHAR,
reportername VARCHAR,
reporterdescription VARCHAR,
firstavailableperiod VARCHAR,
lastavailableperiod VARCHAR,
reportersource VARCHAR,
incoterm VARCHAR,
partnercountryno VARCHAR,
partnercode VARCHAR,
partnerisoalpha3code VARCHAR,
partnerisonumeric3code VARCHAR,
partnername VARCHAR,
hscode VARCHAR,
commoditydescriptionoriginal VARCHAR,
commoditydescriptiontranslation VARCHAR,
hs2code VARCHAR,
hs2descriptionoriginal VARCHAR,
hs2descriptiontranslation VARCHAR,
hs4code VARCHAR,
hs4descriptionoriginal VARCHAR,
hs4descriptiontranslation VARCHAR,
hs6code VARCHAR,
hs6descriptionoriginal VARCHAR,
hs6descriptiontranslation VARCHAR,
commodityexplodedhscode VARCHAR,
commodityexplodeddescriptionoriginal VARCHAR,
commodityexplodeddescriptiontranslation VARCHAR,
hseditionfrom VARCHAR,
hseditionto VARCHAR,
currency VARCHAR,
value VARCHAR,
unit1 VARCHAR,
quantity1 VARCHAR,
q1estimation VARCHAR,
unit2 VARCHAR,
quantity2 VARCHAR,
q2estimation VARCHAR,
priceunit1 VARCHAR,
price1 VARCHAR,
transport VARCHAR,
subdivision VARCHAR,
port VARCHAR,
priceunit2 VARCHAR,
price2 VARCHAR,
customsregime VARCHAR,
foreignport VARCHAR,
suppression VARCHAR,
usstate VARCHAR,
trade_date date
);

create or replace table commod.stage.stg_gtt_trade_details_export (
tradeflow VARCHAR,
ismirror VARCHAR,
periodtype VARCHAR,
year_start VARCHAR,
month_start VARCHAR,
year_end VARCHAR,
month_end VARCHAR,
year VARCHAR,
month VARCHAR,
reportercountryno VARCHAR,
reportercode VARCHAR,
reporterisoalpha3code VARCHAR,
reporterisonumeric3code VARCHAR,
reportername VARCHAR,
reporterdescription VARCHAR,
firstavailableperiod VARCHAR,
lastavailableperiod VARCHAR,
reportersource VARCHAR,
incoterm VARCHAR,
partnercountryno VARCHAR,
partnercode VARCHAR,
partnerisoalpha3code VARCHAR,
partnerisonumeric3code VARCHAR,
partnername VARCHAR,
hscode VARCHAR,
commoditydescriptionoriginal VARCHAR,
commoditydescriptiontranslation VARCHAR,
hs2code VARCHAR,
hs2descriptionoriginal VARCHAR,
hs2descriptiontranslation VARCHAR,
hs4code VARCHAR,
hs4descriptionoriginal VARCHAR,
hs4descriptiontranslation VARCHAR,
hs6code VARCHAR,
hs6descriptionoriginal VARCHAR,
hs6descriptiontranslation VARCHAR,
commodityexplodedhscode VARCHAR,
commodityexplodeddescriptionoriginal VARCHAR,
commodityexplodeddescriptiontranslation VARCHAR,
hseditionfrom VARCHAR,
hseditionto VARCHAR,
currency VARCHAR,
value VARCHAR,
unit1 VARCHAR,
quantity1 VARCHAR,
q1estimation VARCHAR,
unit2 VARCHAR,
quantity2 VARCHAR,
q2estimation VARCHAR,
priceunit1 VARCHAR,
price1 VARCHAR,
transport VARCHAR,
subdivision VARCHAR,
port VARCHAR,
priceunit2 VARCHAR,
price2 VARCHAR,
customsregime VARCHAR,
foreignport VARCHAR,
suppression VARCHAR,
usstate VARCHAR,
filename VARCHAR,
start_scan_time TIMESTAMP_LTZ(9)
);

create or replace table commod.stage.stg_gtt_trade_details_import (
tradeflow VARCHAR,
ismirror VARCHAR,
periodtype VARCHAR,
year_start VARCHAR,
month_start VARCHAR,
year_end VARCHAR,
month_end VARCHAR,
year VARCHAR,
month VARCHAR,
reportercountryno VARCHAR,
reportercode VARCHAR,
reporterisoalpha3code VARCHAR,
reporterisonumeric3code VARCHAR,
reportername VARCHAR,
reporterdescription VARCHAR,
firstavailableperiod VARCHAR,
lastavailableperiod VARCHAR,
reportersource VARCHAR,
incoterm VARCHAR,
partnercountryno VARCHAR,
partnercode VARCHAR,
partnerisoalpha3code VARCHAR,
partnerisonumeric3code VARCHAR,
partnername VARCHAR,
hscode VARCHAR,
commoditydescriptionoriginal VARCHAR,
commoditydescriptiontranslation VARCHAR,
hs2code VARCHAR,
hs2descriptionoriginal VARCHAR,
hs2descriptiontranslation VARCHAR,
hs4code VARCHAR,
hs4descriptionoriginal VARCHAR,
hs4descriptiontranslation VARCHAR,
hs6code VARCHAR,
hs6descriptionoriginal VARCHAR,
hs6descriptiontranslation VARCHAR,
commodityexplodedhscode VARCHAR,
commodityexplodeddescriptionoriginal VARCHAR,
commodityexplodeddescriptiontranslation VARCHAR,
hseditionfrom VARCHAR,
hseditionto VARCHAR,
currency VARCHAR,
value VARCHAR,
unit1 VARCHAR,
quantity1 VARCHAR,
q1estimation VARCHAR,
unit2 VARCHAR,
quantity2 VARCHAR,
q2estimation VARCHAR,
priceunit1 VARCHAR,
price1 VARCHAR,
transport VARCHAR,
subdivision VARCHAR,
port VARCHAR,
priceunit2 VARCHAR,
price2 VARCHAR,
customsregime VARCHAR,
foreignport VARCHAR,
suppression VARCHAR,
usstate VARCHAR,
filename VARCHAR,
start_scan_time TIMESTAMP_LTZ(9)
);


CREATE OR REPLACE VIEW COMMOD.AGRI_GTT.EXPORT AS
SELECT * FROM COMMOD.INTEGRATION.INT_GTT_TRADE_DETAILS_EXPORT
;

CREATE OR REPLACE VIEW COMMOD.AGRI_GTT.IMPORT AS
SELECT * FROM COMMOD.INTEGRATION.INT_GTT_TRADE_DETAILS_IMPORT
;

CREATE SCHEMA COMMOD.AGRI_WEATHER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_WEATHER TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_WEATHER TO DR_COMMOD_READER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_WEATHER TO DR_COMMOD_WRITER;
GRANT OWNERSHIP ON ALL TABLES IN SCHEMA COMMOD.AGRI_WEATHER TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_WEATHER TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON ALL VIEWS IN SCHEMA COMMOD.AGRI_WEATHER TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA COMMOD.AGRI_WEATHER TO DR_COMMOD_OWNER;
GRANT ALL PRIVILEGES on SCHEMA COMMOD.AGRI_WEATHER to role DR_COMMOD_OWNER;
GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.AGRI_WEATHER TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.AGRI_WEATHER TO DR_COMMOD_READER;
GRANT SELECT ON ALL TABLES  IN SCHEMA COMMOD.AGRI_WEATHER TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.AGRI_WEATHER TO DR_COMMOD_READER;

CREATE SCHEMA COMMOD.AGRI_CFTC;

GRANT USAGE ON SCHEMA COMMOD.AGRI_CFTC TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_CFTC TO DR_COMMOD_READER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_CFTC TO DR_COMMOD_WRITER;

GRANT OWNERSHIP ON ALL TABLES IN SCHEMA COMMOD.AGRI_CFTC TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_CFTC TO DR_COMMOD_OWNER;

GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.AGRI_CFTC TO DR_COMMOD_READER;

GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.AGRI_CFTC TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.AGRI_CFTC TO DR_COMMOD_READER;


GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA COMMOD.AGRI_CFTC TO ROLE DR_COMMOD_WRITER;
GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_CFTC TO ROLE DR_COMMOD_WRITER;

CREATE SCHEMA COMMOD.AGRI_EMERGING_TEXTILES;

GRANT USAGE ON SCHEMA COMMOD.AGRI_EMERGING_TEXTILES TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_EMERGING_TEXTILES TO DR_COMMOD_READER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_EMERGING_TEXTILES TO DR_COMMOD_WRITER;

GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_EMERGING_TEXTILES TO DR_COMMOD_OWNER;

GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.AGRI_EMERGING_TEXTILES TO DR_COMMOD_READER;

GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.AGRI_EMERGING_TEXTILES TO DR_COMMOD_READER;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.AGRI_EMERGING_TEXTILES TO DR_COMMOD_READER;


GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA COMMOD.AGRI_EMERGING_TEXTILES TO ROLE DR_COMMOD_WRITER;
GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA COMMOD.AGRI_EMERGING_TEXTILES TO ROLE DR_COMMOD_WRITER;


CREATE SCHEMA COMMOD.KPLER_TRADES;

CREATE ROLE IF NOT EXISTS DR_KPLER_TRADES_READER;

GRANT ROLE DR_KPLER_TRADES_READER TO ROLE FR_COMMOD_QUANT_US;

-- Grant usage on warehouse to roles
GRANT USAGE ON WAREHOUSE COMMOD_WH TO ROLE DR_KPLER_TRADES_READER;

-- Grant usage on database to roles
GRANT USAGE ON DATABASE COMMOD TO ROLE DR_KPLER_TRADES_READER;


GRANT USAGE ON SCHEMA COMMOD.KPLER_TRADES TO DR_COMMOD_OWNER;
GRANT USAGE ON SCHEMA COMMOD.KPLER_TRADES TO DR_KPLER_TRADES_READER;
GRANT USAGE ON SCHEMA COMMOD.KPLER_TRADES TO DR_COMMOD_WRITER;
GRANT OWNERSHIP ON ALL TABLES IN SCHEMA COMMOD.KPLER_TRADES TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD.KPLER_TRADES TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON ALL VIEWS IN SCHEMA COMMOD.KPLER_TRADES TO DR_COMMOD_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA COMMOD.KPLER_TRADES TO DR_COMMOD_OWNER;
GRANT ALL PRIVILEGES on SCHEMA COMMOD.KPLER_TRADES to role DR_COMMOD_OWNER;
GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD.KPLER_TRADES TO DR_KPLER_TRADES_READER;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD.KPLER_TRADES TO DR_KPLER_TRADES_READER;
GRANT SELECT ON ALL TABLES  IN SCHEMA COMMOD.KPLER_TRADES TO DR_KPLER_TRADES_READER;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD.KPLER_TRADES TO DR_KPLER_TRADES_READER;