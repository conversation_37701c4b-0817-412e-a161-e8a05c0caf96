from airflow import DAG
from airflow.operators.bash import BashOperator
from datetime import timedelta
import os, sys, pendulum

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()

JGDATA_PATH = os.environ.get("JGDATA_PATH")
DATA_ACCT_LOG_DIR = "/opt/data/airflow/logs"

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 10, 1, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id='jg-archive-airflow-logs',
    default_args=default_args,
    description='This DAG archive the EKS Airflow logs from /opt/data/airflow/logs folder',
    schedule_interval='0 6 * * *',
    tags=["jgdata", "airflow", "misc"],
    catchup=False,
)

git_sync = BashOperator(
    task_id='jg-archive-airflow-logs',
    bash_command=f"{JGDATA_PATH}/automation-scripts/archive_airflow_logs.sh {DATA_ACCT_LOG_DIR}",
    dag=dag,
)

git_sync
