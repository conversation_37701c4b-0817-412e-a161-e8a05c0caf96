import sys
import os
import requests
import argparse
import logging
from datetime import datetime, timedelta
import time as tm
import gzip
from strunner import *


sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import stcommon.tools.aws as aws
from stcommon.infra.python.fileio import read_toml

setupEnvironment()  # sets up environment
# from stdata import *
from jgdata import *
import pytz


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def usage():
    """Print Usage"""
    print("Usage:")
    print("python3 ice_flash_downloader.py --start_date YYYY_MM_DD --end_date YYYY_MM_DD")
    print("Parameters:")
    print("--start_date: Start date in YYYY_MM_DD format")
    print("--end_date: End date in YYYY_MM_DD format (optional)")

def parse_args():
    parser = argparse.ArgumentParser(description='Download files from ICE Downloads API')
    parser.add_argument('--start_date', type=str, help='Start date in YYYY_MM_DD format (optional)')
    parser.add_argument('--end_date', type=str, help='End date in YYYY_MM_DD format (optional)')
    return parser.parse_args()

def validate_date(date_str):
    try:
        return datetime.strptime(date_str, '%Y_%m_%d')
    except ValueError:
        logging.error(f"Invalid date format for {date_str}. Please use YYYY_MM_DD.")
        sys.exit(1)

def download_file(url, local_path, cookies):
    response = requests.get(url, cookies=cookies)
    if response.status_code == 200:
        if 'Content-Type' in response.headers and response.headers['Content-Type'].startswith('text/html'):
            logging.error(f"File {url} is not available on the server yet.")
            return False
        try:
            with open(local_path, 'wb') as fd:
                for chunk in response.iter_content(100):
                    fd.write(chunk)
            logging.info(f"File downloaded successfully to {local_path}.")
            return True
        except Exception as exception:
            logging.error(f"Unexpected exception while attempting to create file {local_path}")
            logging.error(exception)
            sys.exit(1)
    else:
        logging.error(f"Failed to download file {url}. HTTP Status Code: {response.status_code}")
        return False

def decompress_gz(file_path):
    try:
        with gzip.open(file_path, 'rb') as f_in:
            with open(file_path[:-3], 'wb') as f_out:
                f_out.write(f_in.read())
        os.remove(file_path)
        logging.info(f"File {file_path} decompressed successfully.")
    except Exception as exception:
        logging.error(f"Unexpected exception while attempting to decompress file {file_path}")
        logging.error(exception)
        sys.exit(1)

def main():
    args = parse_args()
    logging.info("Arguments parsed successfully.")

    # Use current date as default if no date arguments are provided
    tz = pytz.timezone('America/New_York')
    now_date = datetime.now(tz)
    current_date_str = now_date.strftime('%Y_%m_%d')

    start_date = validate_date(args.start_date) if args.start_date else validate_date(current_date_str)
    end_date = validate_date(args.end_date) if args.end_date else start_date

    if start_date > end_date:
        logging.error("Start date cannot be after end date.")
        sys.exit(1)

    # Load config.toml using JGDATA_PATH environment variable
    jgdata_path = os.environ.get('JGDATA_PATH')
    if jgdata_path is None:
        logging.error("Missing JGDATA_PATH Environment Variable")
        sys.exit(1)

    config_path = os.path.join(jgdata_path, 'conf', 'sources', 'ice.flash.toml')
    config = read_toml(config_path)


    # Load secret.toml from the same directory as ice.flash.toml
    secret_path = os.path.join(os.path.dirname(config_path), '.secret.toml')
    secrets = secrets = read_toml(secret_path)
    userId = secrets['ice.flash.kwargs']['userId']
    password = secrets['ice.flash.kwargs']['password']

    base_path = config['paths']['base_path']
    file_patterns = config['file_patterns']['patterns']
    s3_bucket = config['s3']['bucket']
    prefix = config['s3']['prefix']

    LOGININFO = {'userId': userId, 'password': password, 'appKey': 'ICEDOWNLOADS'}
    RESPONSE = requests.post('https://sso.ice.com/api/authenticateTfa', LOGININFO)
    logging.info("Authentication request sent.")
    try:
        COOKIE = {
            'iceSsoCookie': RESPONSE.json()['result']['data']['token']
        }
        logging.info("Authentication successful.")
    except Exception as exception:
        logging.error("Unexpected exception while attempting to create a session on sso.theice.com.")
        logging.error(exception)
        sys.exit(1)

    current_date = start_date
    file_errors = []
    while current_date <= end_date:
        if current_date.weekday() >= 5:  # Skip weekends (Saturday=5, Sunday=6)
            current_date += timedelta(days=1)
            continue

        date_str = current_date.strftime('%Y_%m_%d')
        
        for pattern in file_patterns:
            FILEPATH = f'{pattern}_{date_str}.dat'
            FILEPATH_GZ = f'{pattern}_{date_str}.dat.gz'
            FILENAME = os.path.basename(FILEPATH)
            FILENAME_GZ = os.path.basename(FILEPATH_GZ)
            DIRNAME = os.path.join(base_path, os.path.dirname(FILEPATH))

            # Create directories if they don't exist
            if not os.path.exists(DIRNAME):
                os.makedirs(DIRNAME)

            local_path = os.path.join(DIRNAME, FILENAME)
            local_path_gz = os.path.join(DIRNAME, FILENAME_GZ)

            if os.path.exists(local_path):
                logging.info(f"File {FILENAME} already exists locally, skipping download.")
            elif os.path.exists(local_path_gz):
                logging.info(f"File {FILENAME_GZ} already exists locally, decompressing.")
                decompress_gz(local_path_gz)
            else:
                logging.info(f"Downloading file: {FILEPATH}")
                if download_file(f'https://downloads.ice.com/{FILEPATH}', local_path, COOKIE):
                    continue  # If the file was downloaded successfully, skip to the next pattern
                else:
                    logging.info(f"Trying to download file: {FILEPATH_GZ}")
                    if download_file(f'https://downloads.ice.com/{FILEPATH_GZ}', local_path_gz, COOKIE):
                        decompress_gz(local_path_gz)
                    else:
                        logging.error(f"Failed to download both {FILEPATH} and {FILEPATH_GZ}.")
                        file_errors.append((FILEPATH, FILEPATH_GZ))
                
                
                # Add a delay to avoid rate limiting
                tm.sleep(2)  # Adjust the delay as needed

            # S3 syncing logic using the aws module
            # if os.path.exists(local_path):
            #     obj_name = f'{prefix}/{pattern}/{FILENAME}'
            #     if aws.s3exists(s3_bucket, obj_name):
            #         logging.debug(f's3://{s3_bucket}/{obj_name} exists, skipping')
            #     else:
            #         logging.info(f"Syncing: s3://{s3_bucket}/{obj_name}, size: {os.path.getsize(local_path)}, updated at {datetime.fromtimestamp(os.path.getmtime(local_path))}")
            #         aws.s3put(local_path, s3_bucket, obj_name)


        current_date += timedelta(days=1)

    if file_errors and start_date==validate_date(current_date_str): # If there are file errors and the start date is today
        if (now_date.hour == 20 and now_date.minute >= 50) or now_date.hour > 20: # Check if it's after 8:50 PM
            logging.error("Files that failed to download:")
            for original, gz in file_errors:
                logging.error(f"Original: {original} or GZ: {gz}")
            logging.error(f"Files not available for {now_date.strftime('%Y-%m-%d %H:%M:%S')}. Please check the ICE Downloads API.")
            sys.exit(1)

    sys.exit(0)
if __name__ == '__main__':
    main()

