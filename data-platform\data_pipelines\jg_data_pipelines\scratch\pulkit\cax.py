import snowflake.connector
import pandas as pd
from dotenv import load_dotenv
import os
import datetime
load_dotenv()
 
conn = snowflake.connector.connect(
    user=os.getenv('SNOWFLAKE_USER'),
    password=os.getenv('SNOWFLAKE_PASSWORD'),
    account='byb06077.us-east-1',
    warehouse='REBAL_XS',
    database='POC_DB',
    schema='GSC_CAX',
    role='FR_DATA_PLATFORM_UAT'
)
 
def create_corporate_action_derived_table(conn):
    with conn.cursor() as cur:
        sql = f"""
        create or replace table POC_DB.GSC_CAX.CORPORATE_ACTION_DERIVED
        (
            CAX_Type VARCHAR(8) NOT NULL,
            Security_Before VARCHAR(256) NOT NULL,
            Security_After VARCHAR(256) NULL,
            Announcement_Date DATE NOT NULL,
            Effective_Date DATE NULL,
            CAX_Field VARCHAR(20) NOT NULL,
            CAX_Value NUMERIC(38, 11) NOT NULL,
            CAX_Value_Inputs VARCHAR(1024) NULL,
            Valid_From DATETIME NOT NULL,
            Valid_To DATETIME NULL,
            Composite_Key_For_Valid_To VARCHAR(512)
        );      
        """
        cur.execute(sql)
        return True
   
def insert_values_into_corporate_action_derived_table(conn):
    with conn.cursor() as cur:
        sql = f"""
        INSERT INTO POC_DB.GSC_CAX.CORPORATE_ACTION_DERIVED
        (
            CAX_Type,
            Security_Before,
            Security_After,
            Announcement_Date,
            Effective_Date,
            CAX_Field,
            CAX_Value,
            CAX_Value_Inputs,
            Valid_From,
            Valid_To,
            Composite_Key_For_Valid_To
        )
        VALUES
        ('DVCA', 'TICKER1', NULL, '2025-02-09', '2025-10-31', 'PRICE_ADJ', 0.99, 'Div Amount 5.3 millions, prev close price USD 8.0', '2025-02-09 10:00:00', NULL, 'DVCA|TICKER1|2025-02-09|PRICE_ADJ'),
        ('DVCA', 'TICKER1', NULL, '2025-02-09', '2025-10-31', 'SHARES_ADJ', 1.00, 'Div Amount 5.3 millions, prev close price USD 8.0', '2025-02-09 10:00:01', NULL, 'DVCA|TICKER1|2025-02-09|SHARES_ADJ')    
        """
        cur.execute(sql)
        return True
   
def select_values_from_corporate_action_derived_table(conn):
    with conn.cursor() as cur:
        sql = f"""
        select * from POC_DB.GSC_CAX.CORPORATE_ACTION_DERIVED  
        """
        cur.execute(sql)
        columns = [desc[0] for desc in cur.description]
        df = pd.DataFrame(cur.fetchall(), columns=columns)
        return df
   
def get_basic_corporate_action_data(conn, ex_date: datetime.datetime) -> pd.DataFrame:
    with conn.cursor() as cur:
        sql = f"""
        select gmic.bb_composite_global_id, gmic.bb_composite_ticker_id, gca.corporate_action_type, gca.announcement_datetime,
        gca.ex_datetime, gca.adjustment_factor_rate, gca.adjustment_date,
        gca.adjustment_operator_price_type, gca.adjustment_operator_shares_type,
        gca.adjustment_operator_per_share_type, gca.adjustment_operator_volume_type
        from
        GOLDENSOURCE.REFINED.GSC_CORPORATE_ACTION gca, GOLDENSOURCE.REFINED.GSC_MARKET_INSTRUMENT_CHARACTERISTICS gmic
        where
        gca.MARKET_INSTRUMENT_CHARACTERISTICS_SOK = gmic.MARKET_INSTRUMENT_CHARACTERISTICS_SOK
        and gmic.data_warehouse_status_num = 1
        and gca.data_warehouse_status_num = 1
        and gca.ex_datetime = '{ex_date.strftime('%Y-%m-%d')}'
        and gca.corporate_action_type in ('DVCA', 'DVSE', 'PPMT', 'RHTS', 'SOFF', 'SPLF', 'SPLR')
        """
        cur.execute(sql)
        columns = [desc[0] for desc in cur.description]
        df = pd.DataFrame(cur.fetchall(), columns=columns)
        return df
   
def generate_value_line_for_insert(dict_row: dict) -> str:
    """
    Generate a value line for insert into the corporate action derived table.
    """
    line = ""
    line += f"('{dict_row['CAX_TYPE']}', "
    line += f"'{dict_row['SECURITY_BEFORE']}', "
    line += f"'{dict_row['SECURITY_AFTER']}', " if dict_row['SECURITY_AFTER'] is not None else "NULL, "
    line += f"'{dict_row['ANNOUNCEMENT_DATE'].strftime('%Y-%m-%d')}', "
    line += f"'{dict_row['EFFECTIVE_DATE'].strftime('%Y-%m-%d')}', " if not pd.isnull(dict_row['EFFECTIVE_DATE']) else "NULL, "
    line += f"'{dict_row['CAX_FIELD']}', "
    line += f"'{dict_row['CAX_VALUE']}', "  
    line += f"'{dict_row['CAX_VALUE_INPUTS']}', "
    line += f"'{dict_row['VALID_FROM'].strftime('%Y-%m-%d %H:%M:%S')}', "
    line += f"'{dict_row['VALID_TO'].strftime('%Y-%m-%d %H:%M:%S')}', " if dict_row['VALID_TO'] is not None else "NULL, "
    line += f"'{dict_row['COMPOSITE_KEY_FOR_VALID_TO']}')"
    return line
 
def get_update_valid_to_sql(valid_from: datetime.datetime) -> str:
   
    return f"""
    UPDATE POC_DB.GSC_CAX.CORPORATE_ACTION_DERIVED
    SET Valid_To = '{valid_from.strftime('%Y-%m-%d %H:%M:%S')}'
    WHERE
    Valid_From < '{valid_from.strftime('%Y-%m-%d %H:%M:%S')}'
    and Valid_To is NULL
    and Composite_Key_For_Valid_To in
    (
        SELECT Composite_Key_For_Valid_To
        FROM POC_DB.GSC_CAX.CORPORATE_ACTION_DERIVED
        WHERE valid_From >= '{valid_from.strftime('%Y-%m-%d %H:%M:%S')}'
    )
    """
 
 
def generate_insert_sql_prefix() -> str:
    return """
    INSERT INTO POC_DB.GSC_CAX.CORPORATE_ACTION_DERIVED
    (
        CAX_Type,
        Security_Before,
        Security_After,
        Announcement_Date,
        Effective_Date,
        CAX_Field,
        CAX_Value,
        CAX_Value_Inputs,
        Valid_From,
        Valid_To,
        Composite_Key_For_Valid_To
    )
    VALUES
    """
   
def do_etl(conn, df_etl: pd.DataFrame, batch_size: int):
    values_list = []
    valid_from = datetime.datetime.now()
    print(f"Valid from: {valid_from}")
    for row in df_etl.itertuples():
        if row.CORPORATE_ACTION_TYPE in ('DVCA', 'PPMT', 'DVSE', 'RHTS', 'SOFF', 'SPLF', 'SPLR'):
            price_adj = 1.0
            if row.ADJUSTMENT_OPERATOR_PRICE_TYPE is not None and row.ADJUSTMENT_OPERATOR_PRICE_TYPE.lower() == 'multiply':
                price_adj = row.ADJUSTMENT_FACTOR_RATE
            elif row.ADJUSTMENT_OPERATOR_PRICE_TYPE is not None and row.ADJUSTMENT_OPERATOR_PRICE_TYPE.lower() == 'divide':
                price_adj = 1.0/row.ADJUSTMENT_FACTOR_RATE
            else:
                print(f"{row.CORPORATE_ACTION_TYPE} {row.BB_COMPOSITE_GLOBAL_ID} {row.EX_DATETIME} Invalid adjustment operator price type: {row.ADJUSTMENT_OPERATOR_PRICE_TYPE}; default as Multiply")
                price_adj = row.ADJUSTMENT_FACTOR_RATE
            m_dict = {
                'CAX_TYPE': row.CORPORATE_ACTION_TYPE,
                'SECURITY_BEFORE': row.BB_COMPOSITE_GLOBAL_ID,
                'SECURITY_AFTER': None,
                'ANNOUNCEMENT_DATE': row.ANNOUNCEMENT_DATETIME,
                'EFFECTIVE_DATE': row.EX_DATETIME,
            }
            m_dict['CAX_FIELD'] = 'PRICE_ADJ'
            m_dict['CAX_VALUE'] = price_adj
            m_dict['CAX_VALUE_INPUTS'] = f"CP_ADJ: {row.ADJUSTMENT_FACTOR_RATE}, CP_PRICE_ADJUSTMENT_OP: {row.ADJUSTMENT_OPERATOR_PRICE_TYPE}"
            m_dict['VALID_FROM'] = valid_from
            m_dict['VALID_TO'] = None
            m_dict['COMPOSITE_KEY_FOR_VALID_TO'] = f"{row.CORPORATE_ACTION_TYPE}|{row.BB_COMPOSITE_GLOBAL_ID}|{row.ANNOUNCEMENT_DATETIME}|{row.EX_DATETIME}|PRICE_ADJ"
            values_list.append(m_dict)
        if row.CORPORATE_ACTION_TYPE in ('DVSE', 'RHTS', 'SOFF', 'SPLF', 'SPLR'):
            shares_adj = 1.0
            if row.ADJUSTMENT_OPERATOR_SHARES_TYPE is not None and row.ADJUSTMENT_OPERATOR_SHARES_TYPE.lower() == 'multiply':
                shares_adj = row.ADJUSTMENT_FACTOR_RATE
            elif row.ADJUSTMENT_OPERATOR_SHARES_TYPE is not None and row.ADJUSTMENT_OPERATOR_SHARES_TYPE.lower() == 'divide':
                shares_adj = 1.0/row.ADJUSTMENT_FACTOR_RATE
            else:
                print(f"{row.CORPORATE_ACTION_TYPE} {row.BB_COMPOSITE_GLOBAL_ID} {row.EX_DATETIME} Invalid adjustment operator shares type: {row.ADJUSTMENT_OPERATOR_SHARES_TYPE}; default as Multiply")
                shares_adj = row.ADJUSTMENT_FACTOR_RATE
            m_dict = {
                'CAX_TYPE': row.CORPORATE_ACTION_TYPE,
                'SECURITY_BEFORE': row.BB_COMPOSITE_GLOBAL_ID,
                'SECURITY_AFTER': None,
                'ANNOUNCEMENT_DATE': row.ANNOUNCEMENT_DATETIME,
                'EFFECTIVE_DATE': row.EX_DATETIME,
            }
            m_dict['CAX_FIELD'] = 'SHARES_ADJ'
            m_dict['CAX_VALUE'] = shares_adj
            m_dict['CAX_VALUE_INPUTS'] = f"CP_ADJ: {row.ADJUSTMENT_FACTOR_RATE}, CP_FUNDAMENTAL_SHARES_ADJUST_OP: {row.ADJUSTMENT_OPERATOR_PRICE_TYPE}"
            m_dict['VALID_FROM'] = valid_from
            m_dict['VALID_TO'] = None
            m_dict['COMPOSITE_KEY_FOR_VALID_TO'] = f"{row.CORPORATE_ACTION_TYPE}|{row.BB_COMPOSITE_GLOBAL_ID}|{row.ANNOUNCEMENT_DATETIME}|{row.EX_DATETIME}|SHARES_ADJ"
            values_list.append(m_dict)
 
    # insert values into the corporate action derived table (in batches)
    for i in range(0, len(values_list), batch_size):
        print(f"Inserting batch {i//batch_size+1} of {len(values_list)//batch_size+1} ...")
        batch = values_list[i:min(i+batch_size, len(values_list))]
        sql = generate_insert_sql_prefix()
        for j in range(len(batch)):
            sql += generate_value_line_for_insert(batch[j])
            if j < len(batch) - 1:
                sql += ","
        sql += ";"
        with conn.cursor() as cur:
            cur.execute(sql)
   
    # update valid_to if applicable
    with conn.cursor() as cur:
        sql = get_update_valid_to_sql(valid_from)
        cur.execute(sql)
           
if __name__ == "__main__":
    df_etl = get_basic_corporate_action_data(conn, datetime.datetime(2025, 7, 1))
    do_etl(conn, df_etl, 100)