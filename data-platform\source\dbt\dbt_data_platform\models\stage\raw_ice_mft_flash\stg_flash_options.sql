{{ config(
    materialized='incremental',
    tags=['ice', 'options','flash'],
    cluster_by=['trade_date', 'contract', 'strip', 'contract_type', 'strike','start_scan_time'],
) }}

WITH source AS (
    SELECT
        trade_date,
        hub,
        product,
        strip,
        contract,
        contract_type,
        strike,
        settlement_price,
        net_change,
        expiration_date,
        product_id,
        option_volatility,
        delta_factor,
        filename,
        start_scan_time
    FROM {{ source('ice_mft_flash', 'OPTIONS_FLASH_RAW') }}
    WHERE contract_type != 'F'
)

SELECT * 
FROM source
{% if is_incremental() %}
    where start_scan_time >  (SELECT COALESCE(MAX(start_scan_time),'1900-01-01') FROM {{ this }} )
{% endif %}
