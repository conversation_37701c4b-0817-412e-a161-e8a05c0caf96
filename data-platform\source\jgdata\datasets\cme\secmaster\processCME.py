import pandas as pd
import numpy as np
import pandas_read_xml as pdx
import boto3
from pandas_read_xml import flatten, fully_flatten, auto_separate_tables
from io import BytesIO, StringIO

def processCME(raw_data_path, filename, filedate):
    return_dict = {}
    
    xml_file = f"{raw_data_path}/{filename}"
    data = f"{raw_data_path}/Data_Enum_by_XPath.xlsx"
    excel_df = pd.read_excel(data, sheet_name='47 Data Enumerations by XPath')
    excel_df['Field Value'] = excel_df['Field Value'].astype('Int64').astype(str)
    excel_df.rename(columns = {'Field Name':'Field_Name'}, inplace = True) 
    excel_df.rename(columns = {'Enumeration Key Name':'Enumeration_Key_Name'}, inplace = True) 
    excel_df.rename(columns = {'Field Value':'Field_Value'}, inplace = True) 
    
    return_dict['reference_data_enum'] = excel_df
    
    print("File being processed: "+ f"{raw_data_path}{filename}")
    
    df_init = pdx.read_xml(xml_file,['security_master','payload','instrument'])

    ##If there is just one instrument.. we will directly get the main sections
    if '@id' in df_init:
        df_main_sections = df_init
        df_main_sections.rename(columns = {'@id':'instrument_id'}, inplace = True) 
    else:
        df_stage = df_init.melt(var_name="ElementID", value_name="Details")
        #create separate sections
        df_main_sections = df_stage.pipe(flatten)
        
        df_main_sections.rename(columns = {'Details|@id':'instrument_id'}, inplace = True)
        df_main_sections.rename(columns = {'Details|master_information':'master_information'}, inplace = True)
        df_main_sections.rename(columns = {'Details|derivatives':'derivatives'}, inplace = True)

    df_master_information = df_main_sections[["instrument_id","master_information"]]

    ##Create separate sections of the master information
    df_master_information_sections = df_master_information.pipe(flatten).pipe(flatten).pipe(flatten)
    key_columns = ['instrument_id']
    tables_master_information = df_master_information_sections.pipe(auto_separate_tables, key_columns)
    tables_master_information.keys()


    df_market_master_all = tables_master_information['market_master|market']
    df_market_master_all.rename(columns = {'@id':'market_id'}, inplace = True) 

    df_final_instrument_master = tables_master_information['master_information']
    df_final_instrument_master_xref = tables_master_information['instrument_xref|xref']
    df_final_market_master = df_market_master_all[["instrument_id","market_id","country_of_quotation","mic"]].drop_duplicates()
    df_final_market_master_xref = df_market_master_all[list(set(list(df_market_master_all.columns)) - set(["instrument_id","country_of_quotation","mic"]))].drop_duplicates() 

    mapping_desc_by_xpath(df_final_instrument_master, "/security_master/payload/instrument/master_information/", excel_df)

    df_final_instrument_master.rename(columns = {'instrument_id':'INSTRUMENT_IDENTIFIER'}, inplace = True)
    df_final_instrument_master.rename(columns = {'instrument_master|primary_exchange':'PRIMARY_EXCHANGE'}, inplace = True)
    df_final_instrument_master.rename(columns = {'instrument_master|child_issue_ind':'CHILD_ISSUE_INDICATOR'}, inplace = True)
    df_final_instrument_master.rename(columns = {'instrument_master|apex_asset_type|@type':'APEX_ASSET_TYPE_DESCRIPTION'}, inplace = True)
    df_final_instrument_master.rename(columns = {'instrument_master|apex_asset_type':'APEX_ASSET_TYPE'}, inplace = True)
    df_final_instrument_master.rename(columns = {'instrument_master|instrument_type|@type':'INSTRUMENT_TYPE_DESCRIPTION'}, inplace = True)
    df_final_instrument_master.rename(columns = {'instrument_master|instrument_type':'INSTRUMENT_TYPE'}, inplace = True)
    df_final_instrument_master.rename(columns = {'instrument_master|apex_permission|@type':'PERMISSION_TYPE'}, inplace = True)
    df_final_instrument_master.rename(columns = {'instrument_master|apex_permission':'PERMISSION_ID'}, inplace = True)


    df_final_instrument_master_xref.rename(columns = {'instrument_id':'INSTRUMENT_IDENTIFIER'}, inplace = True)
    df_final_instrument_master_xref.rename(columns = {'@type':'IDENTIFIER_TYPE_ID_DESCRIPTION'}, inplace = True) 
    df_final_instrument_master_xref.rename(columns = {'@type_id':'IDENTIFIER_TYPE_ID'}, inplace = True) 
    df_final_instrument_master_xref.rename(columns = {'instrument_xref|xref':'IDENTIFIER'}, inplace = True) 
    df_final_instrument_master_xref.rename(columns = {'@rts_source_id':'RTS_SOURCE_ID'}, inplace = True)
    
    df_final_instrument_master_xref['RTS_SOURCE_ID'] = df_final_instrument_master_xref['RTS_SOURCE_ID'].astype('Int64')
    
    df_final_market_master.rename(columns = {'instrument_id':'INSTRUMENT_IDENTIFIER'}, inplace = True)
    df_final_market_master.rename(columns = {'market_id':'MARKET_ID'}, inplace = True)
    df_final_market_master.rename(columns = {'country_of_quotation':'COUNTRY_OF_QUOTATION'}, inplace = True)
    df_final_market_master.rename(columns = {'mic':'MARKET_CODE'}, inplace = True)

    # df_final_market_master_xref = df_final_market_master_xref.drop('xref|@type_id',axis=1).drop_duplicates()

    df_final_market_master_xref.rename(columns = {'market_id':'MARKET_ID'}, inplace = True) 
    df_final_market_master_xref.rename(columns = {'xref|@type': 'IDENTIFIER_TYPE_ID_DESCRIPTION'}, inplace = True) 
    df_final_market_master_xref.rename(columns = {'xref|@type_id': 'IDENTIFIER_TYPE_ID'}, inplace = True) 
    df_final_market_master_xref.rename(columns = {'xref':'IDENTIFIER'}, inplace = True) 
    df_final_market_master_xref.rename(columns = {'xref|@rts_source_id': 'RTS_SOURCE_ID'}, inplace = True) 
    df_final_market_master_xref.rename(columns = {'xref|@exch_code': 'BLOOMBERG_EXCHANGE_CODE'}, inplace = True) 
    df_final_market_master_xref.rename(columns = {'xref|@id_bb_sec_num': 'BLOOMBERG_SECURITY_ID_NUMBER'}, inplace = True) 
    df_final_market_master_xref.rename(columns = {'xref|@market_sector': 'BLOOMBERG_MARKET_SECTOR'}, inplace = True) 
    df_final_market_master_xref.rename(columns = {'xref|@security_typ': 'BLOOMBERG_SECURITY_TYPE'}, inplace = True) 
    df_final_market_master_xref.rename(columns = {'xref|@ticker': 'BLOOMBERG_TICKER'}, inplace = True) 
    df_final_market_master_xref.rename(columns = {'xref|@unique_id': 'BLOOMBERG_UNIQUE_ID'}, inplace = True) 
    
    
    df_derivatives = df_main_sections[["instrument_id","derivatives"]]
    df_derivatives_check_section = df_derivatives.pipe(flatten)
    
    derivatives_contract_col_header = ['INSTRUMENT_IDENTIFIER', 'CONTRACT_ROOT', 'LOCAL_SECTOR', 'PRICE_CURRENCY', 'CFI', 'PRIMARY_NAME', 'LOCAL_LONG_NAME', 'DERIVATIVE_SUBTYPE', 'DERIVATIVE_SUBTYPE_DESCRIPTION', 'LOCAL_ASSET_TYPE', 'LOCAL_ASSET_TYPE_SOURCE', 'LOCAL_ASSET_TYPE_2', 'LOCAL_ASSET_TYPE_2_SOURCE', 'MAX_TRADE_SIZE', 'MULTIPLIER', 'MULTIPLIER_LEVEL_INDICATOR', 'CONTRACT_SIZE', 'CONTRACT_SIZE_LEVEL_INDICATOR', 'UNIT_OF_MEASURE_DESCRIPTION', 'UNIT_OF_MEASURE', 'TICK_SIZE', 'TICK_SIZE_COUNT', 'TICK_SIZE_LEVEL_INDICATOR', 'TICK_VALUE', 'TICK_VALUE_COUNT', 'TICK_VALUE_LEVEL_INDICATOR', 'TRADE_MATCH_ALGO_ENUM', 'TRADE_MATCH_ALGO_ENUM_TYPE', 'TRADING_QUANTITY_INCREMENT', 'UNDERLYING_ASSET_TYPE', 'UNDERLYING_ASSET_TYPE_DESCRIPTION', 'DELIVERY_DESCRIPTION', 'DELIVERY_OR_SETTLEMENT_TYPE', 'UNDERLYING_INSTRUMENT_ID', 'OPTIONS_ON_FUTURE_DELIVERABLE_INDICATOR', 'UNDERLYING_INSTRUMENT_CROSS_REFERENCE_IDENTIFIER', 'EXERCISE_STYLE', 'EXERCISE_STYLE_DESCRIPTION', 'EXERCISE_STYLE_LEVEL_INDICATOR', 'STRIKE_CURRENCY', 'IDENTIFIER_TYPE_ID_DESCRIPTION', 'IDENTIFIER_TYPE_ID', 'FILE_NAME', 'FILE_DATE']
    derivatives_contract_series_col_header = ['INSTRUMENT_IDENTIFIER', 'MARKET_ID', 'EXPIRATION_DATE', '_DAY', '_MONTH', '_YEAR', 'FIRST_TRADE_DATE', 'LAST_TRADE_DATE', 'LISTING_STATUS', 'INSTRUMENT_DESCRIPTION', 'CALL_OR_PUT', 'CONTRACT_SIZE', '_NAME', 'STATUS', 'STRIKE_PRICE', 'TICK_VALUE', 'TICK_VALUE_COUNT', 'UNDERLYING_MARKET_ID', 'UNDERLYING_MARKET_CODE', 'UNDERLYING_MARKET_IDENTIFIER', 'UNDERLYING_IDENTIFIER_TYPE_DESCRIPTION', 'UNDERLYING_IDENTIFIER_TYPE_ID', 'FILE_NAME', 'FILE_DATE']
    
    exception_df_columns = ['INSTRUMENT_IDENTIFIER', 'CONTRACT_ROOT', 'MARKET_ID', 'EXPIRATION_DATE', 'CALL_OR_PUT', 'FILE_NAME', 'FILE_DATE']
    exception_df = pd.DataFrame(columns=exception_df_columns)
    return_dict["exception_df"] = upsertData(exception_df_columns, exception_df, filename, filedate)

    if 'derivatives|futures' in df_derivatives_check_section:
        df_derivatives_futures_all = df_derivatives.pipe(flatten).pipe(flatten).pipe(flatten).pipe(flatten)
        
        df_final_derivatives_futures_contract = df_derivatives_futures_all.drop('derivatives|futures|contract|series|expiry',axis=1).drop_duplicates()
        mapping_desc_by_xpath(df_final_derivatives_futures_contract, "/security_master/payload/instrument/", excel_df)
         
        df_final_derivatives_futures_contract.rename(columns = {'instrument_id':'INSTRUMENT_IDENTIFIER'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|@root':'CONTRACT_ROOT'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|local_sector':'LOCAL_SECTOR'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|currency':'PRICE_CURRENCY'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|cfi':'CFI'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|primary_name':'PRIMARY_NAME'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|local_long_name':'LOCAL_LONG_NAME'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|subtype':'DERIVATIVE_SUBTYPE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|subtype|@type':'DERIVATIVE_SUBTYPE_DESCRIPTION'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|local_asset_type':'LOCAL_ASSET_TYPE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|local_asset_type|@src':'LOCAL_ASSET_TYPE_SOURCE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|local_asset_type2':'LOCAL_ASSET_TYPE_2'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|local_asset_type2|@src':'LOCAL_ASSET_TYPE_2_SOURCE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|max_trade_size':'MAX_TRADE_SIZE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|multiplier':'MULTIPLIER'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|multiplier|@level':'MULTIPLIER_LEVEL_INDICATOR'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|contract_size':'CONTRACT_SIZE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|contract_size|@level':'CONTRACT_SIZE_LEVEL_INDICATOR'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|unit_of_measure':'UNIT_OF_MEASURE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|unit_of_measure|@type':'UNIT_OF_MEASURE_DESCRIPTION'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|tick_size':'TICK_SIZE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|tick_size|@count':'TICK_SIZE_COUNT'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|tick_size|@level':'TICK_SIZE_LEVEL_INDICATOR'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|tick_value':'TICK_VALUE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|tick_value|@count':'TICK_VALUE_COUNT'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|tick_value|@level':'TICK_VALUE_LEVEL_INDICATOR'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|trade_match_algo_enum':'TRADE_MATCH_ALGO_ENUM'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|trade_match_algo_enum|@type':'TRADE_MATCH_ALGO_ENUM_TYPE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|trading_quantity_increment':'TRADING_QUANTITY_INCREMENT'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|underlying':'UNDERLYING_ASSET_TYPE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|underlying|@type':'UNDERLYING_ASSET_TYPE_DESCRIPTION'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|delivery':'DELIVERY_OR_SETTLEMENT_TYPE'}, inplace = True)
        df_final_derivatives_futures_contract.rename(columns = {'derivatives|futures|contract|delivery|@type':'DELIVERY_DESCRIPTION'}, inplace = True)
        
        return_dict["derivatives_contract"] = upsertData(derivatives_contract_col_header,df_final_derivatives_futures_contract, filename, filedate)


        df_final_derivatives_futures_contract_series = df_derivatives_futures_all[[
            'instrument_id',
            'derivatives|futures|contract|series|expiry'
        ]].pipe(flatten).pipe(flatten)
        
        df_final_derivatives_futures_contract_series.rename(columns = {'instrument_id':'INSTRUMENT_IDENTIFIER'}, inplace = True)
        df_final_derivatives_futures_contract_series.rename(columns = {'derivatives|futures|contract|series|expiry|@date':'EXPIRATION_DATE'}, inplace = True)
        df_final_derivatives_futures_contract_series.rename(columns = {'derivatives|futures|contract|series|expiry|@day':'_DAY'}, inplace = True)
        df_final_derivatives_futures_contract_series.rename(columns = {'derivatives|futures|contract|series|expiry|@market_id':'MARKET_ID'}, inplace = True)
        df_final_derivatives_futures_contract_series.rename(columns = {'derivatives|futures|contract|series|expiry|@month':'_MONTH'}, inplace = True)
        df_final_derivatives_futures_contract_series.rename(columns = {'derivatives|futures|contract|series|expiry|@year':'_YEAR'}, inplace = True)
        df_final_derivatives_futures_contract_series.rename(columns = {'derivatives|futures|contract|series|expiry|first_trade_date':'FIRST_TRADE_DATE'}, inplace = True)
        df_final_derivatives_futures_contract_series.rename(columns = {'derivatives|futures|contract|series|expiry|last_trade_date':'LAST_TRADE_DATE'}, inplace = True)
        df_final_derivatives_futures_contract_series.rename(columns = {'derivatives|futures|contract|series|expiry|status':'LISTING_STATUS'}, inplace = True)
        df_final_derivatives_futures_contract_series.rename(columns = {'derivatives|futures|contract|series|expiry|name':'INSTRUMENT_DESCRIPTION'}, inplace = True)
    
        return_dict["derivatives_contract_series"] = upsertData(derivatives_contract_series_col_header,df_final_derivatives_futures_contract_series, filename, filedate)
        
    
    if 'derivatives|options' in df_derivatives_check_section:
        df_derivatives_options_all = df_derivatives.pipe(flatten).pipe(flatten).pipe(flatten).pipe(flatten)
        
        df_final_derivatives_options_contract = df_derivatives_options_all.drop('derivatives|options|contract|series|expiry',axis=1)
        df_final_derivatives_options_contract = df_final_derivatives_options_contract.pipe(flatten).pipe(flatten).pipe(flatten).pipe(flatten)
        
        mapping_desc_by_xpath(df_final_derivatives_options_contract, "/security_master/payload/instrument/", excel_df)
        columns_to_drop = ['derivatives|options|contract|deliverable|underlying|dup|xref|@type','derivatives|options|contract|deliverable|underlying|instrument_xref|xref|@type|@type', 'derivatives|options|contract|deliverable|underlying|instrument_xref|xref|@type']
        df_final_derivatives_options_contract = df_final_derivatives_options_contract.drop(columns=[col for col in columns_to_drop if col in df_final_derivatives_options_contract.columns],axis=1).drop_duplicates()
        
        df_final_derivatives_options_contract.rename(columns = {'instrument_id':'INSTRUMENT_IDENTIFIER'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|@root':'CONTRACT_ROOT'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|local_sector':'LOCAL_SECTOR'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|currency':'PRICE_CURRENCY'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|cfi':'CFI'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|primary_name':'PRIMARY_NAME'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|local_long_name':'LOCAL_LONG_NAME'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|subtype':'DERIVATIVE_SUBTYPE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|subtype|@type':'DERIVATIVE_SUBTYPE_DESCRIPTION'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|local_asset_type':'LOCAL_ASSET_TYPE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|local_asset_type|@src':'LOCAL_ASSET_TYPE_SOURCE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|local_asset_type2':'LOCAL_ASSET_TYPE_2'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|local_asset_type2|@src':'LOCAL_ASSET_TYPE_2_SOURCE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|max_trade_size':'MAX_TRADE_SIZE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|multiplier':'MULTIPLIER'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|multiplier|@level':'MULTIPLIER_LEVEL_INDICATOR'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|contract_size':'CONTRACT_SIZE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|contract_size|@level':'CONTRACT_SIZE_LEVEL_INDICATOR'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|unit_of_measure':'UNIT_OF_MEASURE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|unit_of_measure|@type':'UNIT_OF_MEASURE_DESCRIPTION'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|tick_size':'TICK_SIZE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|tick_size|@count':'TICK_SIZE_COUNT'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|tick_size|@level':'TICK_SIZE_LEVEL_INDICATOR'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|tick_value':'TICK_VALUE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|tick_value|@count':'TICK_VALUE_COUNT'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|tick_value|@level':'TICK_VALUE_LEVEL_INDICATOR'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|trade_match_algo_enum':'TRADE_MATCH_ALGO_ENUM'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|trade_match_algo_enum|@type':'TRADE_MATCH_ALGO_ENUM_TYPE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|trading_quantity_increment':'TRADING_QUANTITY_INCREMENT'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|underlying':'UNDERLYING_ASSET_TYPE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|underlying|@type':'UNDERLYING_ASSET_TYPE_DESCRIPTION'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|delivery':'DELIVERY_OR_SETTLEMENT_TYPE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|delivery|@type':'DELIVERY_DESCRIPTION'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|delivery':'DELIVERY_OR_SETTLEMENT_TYPE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|deliverable|underlying|@id':'UNDERLYING_INSTRUMENT_ID'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|deliverable|underlying|@oof':'OPTIONS_ON_FUTURE_DELIVERABLE_INDICATOR'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|deliverable|underlying|instrument_xref|xref':'UNDERLYING_INSTRUMENT_CROSS_REFERENCE_IDENTIFIER'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|exercise_style':'EXERCISE_STYLE'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|exercise_style|@type':'EXERCISE_STYLE_DESCRIPTION'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|exercise_style|@level':'EXERCISE_STYLE_LEVEL_INDICATOR'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|strike_currency':'STRIKE_CURRENCY'}, inplace = True)
        # df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|deliverable|underlying|instrument_xref|xref|@type':'IDENTIFIER_TYPE_ID_DESCRIPTION'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|deliverable|underlying|instrument_xref|xref|@type_id':'IDENTIFIER_TYPE_ID'}, inplace = True)
        df_final_derivatives_options_contract.rename(columns = {'derivatives|options|contract|deliverable|underlying|instrument_xref|xref|@type_id|@type':'IDENTIFIER_TYPE_ID_DESCRIPTION'}, inplace = True)
        
        exception_df = df_final_derivatives_options_contract.groupby('CONTRACT_ROOT').filter(lambda x:x['UNDERLYING_INSTRUMENT_ID'].nunique() > 1)
        df_final_derivatives_options_contract = df_final_derivatives_options_contract[~df_final_derivatives_options_contract.index.isin(exception_df.index)]
        return_dict['exception_df'] = upsertData(exception_df_columns,exception_df, filename, filedate)
        return_dict["derivatives_contract"] = upsertData(derivatives_contract_col_header,df_final_derivatives_options_contract, filename, filedate)
        
        # dropping rows in df_final_instrument_master which matches contracts in exception table
        common_rows_instrument_master = pd.merge(df_final_instrument_master, exception_df, on='INSTRUMENT_IDENTIFIER')
        df_final_instrument_master = df_final_instrument_master[~df_final_instrument_master['INSTRUMENT_IDENTIFIER'].isin(common_rows_instrument_master['INSTRUMENT_IDENTIFIER'])]
        
        df_final_derivatives_options_contract_series_calls = df_derivatives_options_all[[
            'instrument_id',
            'derivatives|options|contract|series|expiry'
        ]].pipe(flatten).pipe(flatten).pipe(flatten)
        
        df_final_derivatives_options_contract_series_calls = df_final_derivatives_options_contract_series_calls.drop(['derivatives|options|contract|series|expiry|puts|call_or_put','derivatives|options|contract|series|expiry|puts|strike_details'], axis=1)
        df_final_derivatives_options_contract_series_calls = df_final_derivatives_options_contract_series_calls.pipe(flatten).pipe(flatten).pipe(flatten).pipe(flatten)
        df_final_derivatives_options_contract_series_calls = df_final_derivatives_options_contract_series_calls.drop('derivatives|options|contract|series|expiry|deliverable|underlying|market|xref|@type_id', axis=1).drop_duplicates()
        
        df_final_derivatives_options_contract_series_calls.rename(columns = {'instrument_id':'INSTRUMENT_IDENTIFIER'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|@date':'EXPIRATION_DATE'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|@day':'_DAY'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|@market_id':'MARKET_ID'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|@month':'_MONTH'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|@year':'_YEAR'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|first_trade_date':'FIRST_TRADE_DATE'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|last_trade_date':'LAST_TRADE_DATE'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|status':'LISTING_STATUS'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|name':'INSTRUMENT_DESCRIPTION'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|calls|call_or_put':'CALL_OR_PUT'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|calls|strike_details|@market_id':'MARKET_ID'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|calls|strike_details|contract_size':'CONTRACT_SIZE'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|calls|strike_details|name':'_NAME'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|calls|strike_details|status':'STATUS'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|calls|strike_details|strike_prc':'STRIKE_PRICE'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|calls|strike_details|tick_value':'TICK_VALUE'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|calls|strike_details|tick_value|@count':'TICK_VALUE_COUNT'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|deliverable|underlying|market|@id':'UNDERLYING_MARKET_ID'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|deliverable|underlying|market|mic':'UNDERLYING_MARKET_CODE'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|deliverable|underlying|market|xref':'UNDERLYING_MARKET_IDENTIFIER'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|deliverable|underlying|market|xref|@type':'UNDERLYING_IDENTIFIER_TYPE_DESCRIPTION'}, inplace = True)
        df_final_derivatives_options_contract_series_calls.rename(columns = {'derivatives|options|contract|series|expiry|deliverable|underlying|market|xref|@type_id':'UNDERLYING_IDENTIFIER_TYPE_ID'}, inplace = True)
        
        
        df_final_derivatives_options_contract_series_puts = df_derivatives_options_all[[
            'instrument_id',
            'derivatives|options|contract|series|expiry'
        ]].pipe(flatten).pipe(flatten).pipe(flatten)

        df_final_derivatives_options_contract_series_puts = df_final_derivatives_options_contract_series_puts.drop(['derivatives|options|contract|series|expiry|calls|call_or_put','derivatives|options|contract|series|expiry|calls|strike_details'], axis=1)
        df_final_derivatives_options_contract_series_puts = df_final_derivatives_options_contract_series_puts.pipe(flatten).pipe(flatten).pipe(flatten).pipe(flatten)
        df_final_derivatives_options_contract_series_puts = df_final_derivatives_options_contract_series_puts.drop('derivatives|options|contract|series|expiry|deliverable|underlying|market|xref|@type_id', axis=1).drop_duplicates()

        df_final_derivatives_options_contract_series_puts.rename(columns = {'instrument_id':'INSTRUMENT_IDENTIFIER'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|@date':'EXPIRATION_DATE'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|@day':'_DAY'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|@market_id':'MARKET_ID'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|@month':'_MONTH'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|@year':'_YEAR'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|first_trade_date':'FIRST_TRADE_DATE'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|last_trade_date':'LAST_TRADE_DATE'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|status':'LISTING_STATUS'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|name':'INSTRUMENT_DESCRIPTION'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|deliverable|underlying|market|@id':'UNDERLYING_MARKET_ID'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|deliverable|underlying|market|mic':'UNDERLYING_MARKET_CODE'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|deliverable|underlying|market|xref':'UNDERLYING_MARKET_IDENTIFIER'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|deliverable|underlying|market|xref|@type':'UNDERLYING_IDENTIFIER_TYPE_DESCRIPTION'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|deliverable|underlying|market|xref|@type_id':'UNDERLYING_IDENTIFIER_TYPE_ID'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|puts|call_or_put':'CALL_OR_PUT'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|puts|strike_details|@market_id':'MARKET_ID'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|puts|strike_details|contract_size':'CONTRACT_SIZE'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|puts|strike_details|name':'_NAME'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|puts|strike_details|status':'STATUS'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|puts|strike_details|strike_prc':'STRIKE_PRICE'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|puts|strike_details|tick_value':'TICK_VALUE'}, inplace = True)
        df_final_derivatives_options_contract_series_puts.rename(columns = {'derivatives|options|contract|series|expiry|puts|strike_details|tick_value|@count':'TICK_VALUE_COUNT'}, inplace = True)
        
        df_final_derivatives_options_contract_series = pd.concat([df_final_derivatives_options_contract_series_calls,df_final_derivatives_options_contract_series_puts], axis=0).sort_values(by='EXPIRATION_DATE').reset_index(drop=True)
        
        series_exceptions_df = df_final_derivatives_options_contract_series.groupby('MARKET_ID').filter(lambda x:x['UNDERLYING_MARKET_ID'].nunique() > 1)
        df_final_derivatives_options_contract_series = df_final_derivatives_options_contract_series[~df_final_derivatives_options_contract_series.index.isin(series_exceptions_df.index)]
        
        df_final_derivatives_options_contract_series['LAST_TRADE_DATE'] = pd.to_datetime(df_final_derivatives_options_contract_series['LAST_TRADE_DATE'])
        latest_last_trade_dates = df_final_derivatives_options_contract_series.groupby(['INSTRUMENT_IDENTIFIER', 'MARKET_ID'])['LAST_TRADE_DATE'].transform('max')
        df_final_derivatives_options_contract_series = df_final_derivatives_options_contract_series[df_final_derivatives_options_contract_series['LAST_TRADE_DATE']==latest_last_trade_dates]
        
        if exception_df.empty:
            exception_df = series_exceptions_df.copy()
        else:
            exception_df = pd.concat([return_dict['exception_df'], series_exceptions_df],axis=0)
            exception_df = exception_df.drop_duplicates()
            
        return_dict['exception_df'] = (upsertData(exception_df_columns,exception_df, filename, filedate)).drop_duplicates()
        
        return_dict["derivatives_contract_series"] = upsertData(derivatives_contract_series_col_header,df_final_derivatives_options_contract_series, filename, filedate)
        
    
    instrument_master_col_header=['INSTRUMENT_IDENTIFIER',  'APEX_ASSET_TYPE', 'APEX_ASSET_TYPE_DESCRIPTION', 'INSTRUMENT_TYPE', 'INSTRUMENT_TYPE_DESCRIPTION', 'PRIMARY_EXCHANGE', 'PERMISSION_ID', 'PERMISSION_TYPE', 'CHILD_ISSUE_INDICATOR', 'FILE_NAME', 'FILE_DATE']
    return_dict["instrument_master"] = upsertData(instrument_master_col_header,df_final_instrument_master, filename, filedate)   
    
    instrument_master_xref_col_header=['INSTRUMENT_IDENTIFIER', 'IDENTIFIER_TYPE_ID', 'IDENTIFIER_TYPE_ID_DESCRIPTION', 'IDENTIFIER', 'RTS_SOURCE_ID', 'FILE_NAME', 'FILE_DATE']
    return_dict["instrument_master_xref"] = upsertData(instrument_master_xref_col_header, df_final_instrument_master_xref, filename, filedate)

    market_master_col_header=['INSTRUMENT_IDENTIFIER', 'MARKET_ID', 'COUNTRY_OF_QUOTATION', 'MARKET_CODE', 'FILE_NAME', 'FILE_DATE']
    return_dict["market_master"] = upsertData(market_master_col_header, df_final_market_master, filename, filedate)
    
    market_master_xref_col_header = ['MARKET_ID', 'IDENTIFIER_TYPE_ID_DESCRIPTION', 'IDENTIFIER_TYPE_ID', 'IDENTIFIER', 'RTS_SOURCE_ID', 'BLOOMBERG_TICKER', 'BLOOMBERG_EXCHANGE_CODE', 'BLOOMBERG_SECURITY_ID_NUMBER', 'BLOOMBERG_MARKET_SECTOR', 'BLOOMBERG_SECURITY_TYPE', 'BLOOMBERG_UNIQUE_ID', 'FILE_NAME', 'FILE_DATE']
    return_dict["market_master_xref"] = upsertData(market_master_xref_col_header, df_final_market_master_xref, filename, filedate)
        
    return return_dict

def mapping_desc_by_xpath(xml_df, prefix, excel_df):
    for col in xml_df.columns:
        column = prefix+col.replace('|','/')
        # Check for substring match with any XPath in excel_df
        for xpath in excel_df['Xpath'].unique():
            if column in xpath:
                xml_df[col+'|@type'] = xml_df[col].apply(replace_with_description, args=(xpath, excel_df))


def replace_with_description(x, xpath, excel_df):
    if(xpath=='derivatives|options|contract|deliverable|underlying|instrument_xref|xref'):
        print("xpath from replace func", xpath)
    match = excel_df[(excel_df['Xpath'] == xpath) & (excel_df['Field_Value'] == x)]
    if not match.empty:
        return f"{match.iloc[0]['Description']}"
    return x

def upsertData(desired_columns, df_source, filename, date_parameter):  
    missing_columns = set(desired_columns) - set(df_source.columns)
    for col in missing_columns:
        df_source[col] = None
    
    df_source['FILE_NAME'] = filename
    df_source["FILE_DATE"] = date_parameter
    df_source = df_source[desired_columns]
    df_source = df_source.where(pd.notna(df_source), None)
    df_source = df_source.where(pd.notnull(df_source), None)
    
    return df_source