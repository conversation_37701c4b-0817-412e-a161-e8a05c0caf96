from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
import os, sys, pendulum, subprocess, logging, pytz
from airflow.providers.http.operators.http import HttpOperator
from util.etl import check_for_anomalies
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup

# Setup environment
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()

# Environment paths
JGDATA_PATH = os.environ.get("JGDATA_PATH")
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)

# Filetypes to process
filetypes = [
    "fe_saf", "fe_af", "fe_qf", "fe_sec", "ff_sec", "ff_basic", "ff_advanced",
    "ff_basic_der", "ff_advanced_der", "sym_tkr_reg_hist", "sym_sed_hist", "sym_coverage", "sym_bbg"
]

# Default DAG arguments
default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.yesterday(tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

# Python callable to run factset process
def run_factset(filetype: str):

    command = f"python3 {DATA_PIPELINE_PATH}/factset_uat/factset_main.py --filetype {filetype}"

    try:
        logging.info(f"Running command: {command}")
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

# Define the DAG
with DAG(
    dag_id="jg-etl-factset-load-uat",
    description="This DAG runs ETL for Factset in UAT DB",
    default_args=default_args,
    schedule_interval='0 * * * *',
    max_active_runs = 1,
    catchup=True,
    tags=["jgdata", "factset"]
) as dag:
    filetype_tasks = []
    # Create one task per filetype with a custom task_id
    for ft in filetypes:
        task = PythonOperator(
            task_id=f"run_factset_uat_{ft}",
            python_callable=run_factset,
            op_kwargs={"filetype": ft}
        )
        filetype_tasks.append(task)

    validation_job = HttpOperator(
        task_id="call-daily-data-validation-api",
        http_conn_id="vendor_http_raw",
        endpoint="get_daily_data_validation_rule_based/?dataset_id=Factset",
        method="GET",
        headers={"Content-Type": "application/json"},
        response_check=check_for_anomalies,
        extra_options={"check_response": True},
        log_response=True
    )

    filetype_tasks >> validation_job
