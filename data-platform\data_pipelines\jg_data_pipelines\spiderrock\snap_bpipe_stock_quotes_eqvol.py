import os
import json
import time
import psycopg2
import logging
import pandas as pd
from io import StringIO
from utils.date_utils import get_now
from utils.postgres.adaptor import PostgresAdaptor
from bloomberg.bpipe_mktdata_snapshot import process_snapshot_request

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

base_path = os.path.dirname(__file__)
with open(f"{base_path}/conf/spiderrock_api.json", "r") as f:
    config = json.load(f)

snapshot_fields = [
    "LAST_PRICE",
    "BID",
    "BID_SIZE",
    "ASK",
    "ASK_SIZE",
]

def get_tickers_for_bbg_price_snap():
    loader = PostgresAdaptor(
        host=config["pg_host"],
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    df_securities = loader.execute_query(
        """select 
                CASE 
                WHEN std.symboltype in ('Equity', 'ETF', 'ADR') then TRIM(std.bbgcompositeticker) || ' Equity'
                WHEN std.symboltype = 'CashIndex' THEN TRIM(std.ticker) || ' Index'
                ELSE NULL
                end as bbg_ticker
            from eqvol.ticker_request_config trc join 
            eqvol.sr_ticker_definition std on trc.ticker = std.ticker
            where std.symboltype in ('Equity', 'ETF', 'CashIndex') and 
            trc.is_bbg_stock_quote_enabled = true;
        """
    )

    return df_securities["bbg_ticker"].unique().tolist()


if __name__ == "__main__":
    pg_host = config["pg_host"]
    pg_username = os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"]
    pg_password = os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"]
    dbname = "fe_risk"

    api_req_time = get_now('UTC').replace(second=0, microsecond=0)
    securities = get_tickers_for_bbg_price_snap()
    dict_eq_prices = process_snapshot_request(securities, snapshot_fields, 30)
    df = pd.DataFrame(dict_eq_prices)
    df["jg_api_req_timestamp"] = api_req_time
    df["ask_size"] = df["ask_size"].astype("Int64")
    df["bid_size"] = df["bid_size"].astype("Int64")
    
    buffer = StringIO()
    df.to_csv(buffer, index=False, header=True)
    
    start_time = time.time()
    buffer.seek(0)
    column_list = ", ".join(list(df.columns))
    conn_string = f"host={pg_host} dbname={dbname} user={pg_username} password={pg_password} port=5432"
    with psycopg2.connect(conn_string) as conn:
        with conn.cursor() as cursor:
            cursor.execute("SET work_mem TO '256MB'")
            copy_sql = f"""COPY eqvol.bbg_stock_quotes_exp ({column_list}) FROM STDIN WITH (FORMAT CSV, HEADER)"""
            cursor.copy_expert(copy_sql, buffer)

        conn.commit()
    end_time = time.time()
    logger.info(f"Time taken for saving bbg_stock_quotes_exp: {end_time - start_time:.4f} secs")

    
