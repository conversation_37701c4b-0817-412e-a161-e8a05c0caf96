#!/bin/bash

OUTPUT_FILE="/etc/node_exporter/server_service_status.prom"

# Function to check if cron expression matches current time
function cron_matches_now() {
    local cron_expr="$1"
    result=$(python3 -c "
from croniter import croniter, CroniterBadCronError
from datetime import datetime, timedelta
import sys

try:
    expr = sys.argv[1]
    now = datetime.now()
    cron = croniter(expr, now)
    cron_time = cron.get_next(datetime)

    if now.second > 0:
        now += timedelta(minutes=1)
    date_time = now.replace(second=0, microsecond=0)

    if date_time == cron_time:
        print('yes')
    else:
        print('no')
except CroniterBadCronError:
    print('invalid')
" "$cron_expr")
    echo "$result"
}

# Function to load servers from YAML
function read_config() {
    local file="$1"
    mapfile -t servers < <(yq eval -r '.server_ip[]' "$file")
}

CONFIG_FILE="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/conf/connectors_services_process.yaml"
SERVER_CONFIG_FILE="/config/config.yaml"

read_config "$SERVER_CONFIG_FILE"

if [[ ${#servers[@]} -eq 0 ]]; then
    echo "❌ ERROR: No servers found in config. Exiting."
    exit 1
fi

config_data=$(yq eval '.services' "$CONFIG_FILE")

while true; do
    echo "=== $(date): Checking systemd services ==="
    TEMP_FILE="/tmp/server_service_status.prom.$$"

    {
        echo "# HELP service_status Service status (1=up, 0=down)"
        echo "# TYPE service_status gauge"
        echo "# HELP node_textfile_mtime_seconds Timestamp of last update"
        echo "# TYPE node_textfile_mtime_seconds gauge"
    } > "$TEMP_FILE"

    service_count=$(echo "$config_data" | yq eval 'length' -)

    for ((i = 0; i < service_count; i++)); do
        service=$(echo "$config_data" | yq eval ".[$i].systemd_svc_name" -)
        cron_expr=$(echo "$config_data" | yq eval ".[$i].cron_expression" -)
        server=$(echo "$config_data" | yq eval ".[$i].service_server" -)

        if [[ -z "$cron_expr" || "$cron_expr" == "null" ]]; then
            echo "⚠️  Skipping $service: no cron_expression"
            continue
        fi

        match_result=$(cron_matches_now "$cron_expr")
        echo "🕒 Now: $(date '+%Y-%m-%d %H:%M:%S') | Cron: $cron_expr → Match: $match_result"

        if [[ "$match_result" != "yes" ]]; then
            echo "⏩ Skipping $service due to cron schedule mismatch"
            continue
        fi

        IFS=',' read -ra server_list <<< "$server"
        for server in "${server_list[@]}"; do
            echo "🔍 Checking $service on $server"

            if ssh -i ~/.ssh/id_rsa \
                -o StrictHostKeyChecking=no \
                -o UserKnownHostsFile=/dev/null \
                -o LogLevel=ERROR \
                "$server" "systemctl is-active --quiet $service"
            then
                echo "✅ Service $service is ACTIVE on $server"
                echo "service_status{server=\"$server\", service=\"$service\"} 1" >> "$TEMP_FILE"
            else
                echo "❌ Service $service is DOWN on $server"
                echo "service_status{server=\"$server\", service=\"$service\"} 0" >> "$TEMP_FILE"
            fi
        done
    done

    current_time=$(date +%s)
    echo "node_textfile_mtime_seconds{file=\"$OUTPUT_FILE\"} $current_time" >> "$TEMP_FILE"

    mv "$TEMP_FILE" "$OUTPUT_FILE"
    chmod 644 "$OUTPUT_FILE"
    echo "📦 Metrics updated → $OUTPUT_FILE"
    echo

    sleep 60
done
