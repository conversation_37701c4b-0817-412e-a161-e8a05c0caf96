SET search_path TO var;

------------------------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS stg_corp_issuer_recoveries (
	recovery_rate_rm_name varchar not NULL,
	px_date date not NULL,
	px_recovery_rate float8 not null,
	created_at timestamp not null,
	CONSTRAINT stg_corp_issuer_recoveries_pkey PRIMARY KEY(recovery_rate_rm_name, px_date)
);

CREATE TABLE IF NOT EXISTS corp_issuer_recoveries (
	recovery_rate_rm_name varchar not NULL,
    recovery_rate_rm_name_cleaned varchar not NULL,
	px_date date not NULL,
	px_recovery_rate float8 not null,
	created_at timestamp not null,
	CONSTRAINT corp_issuer_recoveries_pkey PRIMARY KEY(recovery_rate_rm_name, px_date)
);

CREATE INDEX idx_corp_issuer_recoveries_px_date ON corp_issuer_recoveries (px_date);

--corp
CREATE OR REPLACE VIEW var.latest_corp_issuer_recoveries
AS WITH latest_dates AS (
         SELECT corp_issuer_recoveries.recovery_rate_rm_name,
            max(corp_issuer_recoveries.px_date) AS max_date
           FROM var.corp_issuer_recoveries
          GROUP BY corp_issuer_recoveries.recovery_rate_rm_name
        ), latest_values AS (
         SELECT l.recovery_rate_rm_name,
            r.px_date,
            r.px_recovery_rate
           FROM var.corp_issuer_recoveries r
             JOIN latest_dates l ON r.recovery_rate_rm_name = l.recovery_rate_rm_name AND r.px_date = l.max_date
        )
 SELECT latest_values.recovery_rate_rm_name,
    latest_values.px_date,
    latest_values.px_recovery_rate
   FROM latest_values;
------------------------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS stg_mds_usd_issuer (
	mds_uid varchar not null,
	curve_description varchar not null,
	country varchar,
	currency varchar,
	sector varchar,
	rating varchar,
	pay_freq int,
    px_maturity varchar not null,
	px_date date not null,
	px_yield float8,
	px_spread_gov float8,
	created_at timestamp not null,
	CONSTRAINT stg_mds_usd_issuer_pkey primary key(mds_uid, px_maturity, px_date)
);

--CREATE INDEX IF NOT EXISTS idx_stg_usd_issuer_uid_date ON stg_mds_usd_issuer (mds_uid, px_date);

CREATE TABLE IF NOT EXISTS mds_usd_issuer (
	mds_uid varchar not null,
	curve_description varchar not null,
    curve_description_cleaned varchar not null,
	country varchar,
	currency varchar,
	sector varchar,
	rating varchar,
	pay_freq int,
	px_date date not null,
	created_at timestamp not null,
	CONSTRAINT mds_usd_issuer_pkey primary key(mds_uid, px_date)
);

CREATE INDEX idx_mds_usd_issuer_uid_date ON mds_usd_issuer (mds_uid, px_date);
CREATE INDEX idx_mds_usd_issuer_date ON mds_usd_issuer (px_date);


CREATE TABLE IF NOT EXISTS mds_issuer_pivot (
	mds_uid text NULL,
	px_date timestamp NULL,
	px_yield_1m float8 NULL,
	px_yield_3m float8 NULL,
	px_yield_6m float8 NULL,
	px_yield_12m float8 NULL,
	px_yield_24m float8 NULL,
	px_yield_36m float8 NULL,
	px_yield_48m float8 NULL,
	px_yield_60m float8 NULL,
	px_yield_72m float8 NULL,
	px_yield_84m float8 NULL,
	px_yield_96m float8 NULL,
	px_yield_108m float8 NULL,
	px_yield_120m float8 NULL,
	px_yield_180m float8 NULL,
	px_yield_240m float8 NULL,
	px_yield_300m float8 NULL,
	px_yield_360m float8 NULL,
	px_spread_gov_1m float8 NULL,
	px_spread_gov_3m float8 NULL,
	px_spread_gov_6m float8 NULL,
	px_spread_gov_12m float8 NULL,
	px_spread_gov_24m float8 NULL,
	px_spread_gov_36m float8 NULL,
	px_spread_gov_48m float8 NULL,
	px_spread_gov_60m float8 NULL,
	px_spread_gov_72m float8 NULL,
	px_spread_gov_84m float8 NULL,
	px_spread_gov_96m float8 NULL,
	px_spread_gov_108m float8 NULL,
	px_spread_gov_120m float8 NULL,
	px_spread_gov_180m float8 NULL,
	px_spread_gov_240m float8 NULL,
	px_spread_gov_300m float8 NULL,
	px_spread_gov_360m float8 NULL,
	CONSTRAINT mds_usd_pivot_pkey primary key(mds_uid, px_date)  
);

CREATE INDEX idx_mds_pivot_px_date ON mds_issuer_pivot (px_date);
CREATE INDEX idx_mds_pivot_uid ON mds_issuer_pivot (mds_uid, px_date);

-- mds_user
CREATE OR REPLACE VIEW var.latest_mds_usd_issuer
AS WITH latest_dates AS (
         SELECT mds_usd_issuer.mds_uid,
            max(mds_usd_issuer.px_date) AS max_date
           FROM var.mds_usd_issuer
          GROUP BY mds_usd_issuer.mds_uid
        ), latest_values AS (
         SELECT l.mds_uid,
            r.px_date,
            r.curve_description,
            r.country,
            r.currency,
            r.sector,
            r.rating,
            r.pay_freq
           FROM var.mds_usd_issuer r
             JOIN latest_dates l ON r.mds_uid = l.mds_uid AND r.px_date = l.max_date
        )
 SELECT latest_values.mds_uid,
    latest_values.px_date,
    latest_values.curve_description,
    latest_values.country,
    latest_values.currency,
    latest_values.sector,
    latest_values.rating,
    latest_values.pay_freq
   FROM latest_values;

------------------------------------------------------------------------------------------------


CREATE TABLE IF NOT EXISTS stg_mds_usd_issuer_constituents (
	mds_uid varchar not null,
	curve_description varchar,
	isin varchar not null,
	currency varchar,
	bond_description varchar,
	seniority varchar not null,
	obs_date date not null,
	recovery_rate_rm_name varchar not null,
	created_at timestamp not null,
	CONSTRAINT stg_mds_usd_issuer_constituents_pkey primary key(mds_uid, isin, obs_date)
);

CREATE TABLE IF NOT EXISTS mds_usd_issuer_constituents (
	mds_uid varchar not null,
	curve_description varchar,
	isin varchar not null,
	currency varchar,
	bond_description varchar,
	seniority varchar not null,
	obs_date date not null,
	recovery_rate_rm_name varchar,
    recovery_rate_rm_name_cleaned varchar,
	created_at timestamp not null,
	CONSTRAINT mds_usd_issuer_constituents_pkey primary key(mds_uid, isin, obs_date)
);

CREATE INDEX IF NOT EXISTS idx_constituents_uid_date ON mds_usd_issuer_constituents (mds_uid, obs_date);

--constituents

CREATE OR REPLACE VIEW var.latest_mds_usd_issuer_constituents
AS WITH latest_dates_cur AS (
         SELECT mds_usd_issuer_constituents.mds_uid,
            max(mds_usd_issuer_constituents.obs_date) AS max_date
           FROM var.mds_usd_issuer_constituents
          GROUP BY mds_usd_issuer_constituents.mds_uid
        ), latest_values_cur AS (
         SELECT r.mds_uid,
            r.obs_date,
            r.isin,
            r.bond_description
           FROM var.mds_usd_issuer_constituents r
             JOIN latest_dates_cur l ON r.mds_uid::text = l.mds_uid::text AND r.obs_date = l.max_date
        ), grouped_cur AS (
         SELECT latest_values_cur.mds_uid,
            array_agg(latest_values_cur.isin ORDER BY latest_values_cur.isin) AS isin_group,
            md5(array_agg(latest_values_cur.isin ORDER BY latest_values_cur.isin)::text) AS isin_group_md5,
            array_agg(latest_values_cur.bond_description ORDER BY latest_values_cur.bond_description) AS bond_description_group,
            md5(array_agg(latest_values_cur.bond_description ORDER BY latest_values_cur.bond_description)::text) AS bond_description_group_md5,
            max(latest_values_cur.obs_date) AS obs_date
           FROM latest_values_cur
          GROUP BY latest_values_cur.mds_uid
        )
 SELECT grouped_cur.mds_uid,
    grouped_cur.isin_group,
    grouped_cur.isin_group_md5,
    grouped_cur.bond_description_group,
    grouped_cur.bond_description_group_md5,
    grouped_cur.obs_date
   FROM grouped_cur;


------------------------------------------------------------------------------------------------


CREATE OR REPLACE PROCEDURE var.sp_run_all_data_ingestion()
LANGUAGE plpgsql
AS $$
DECLARE
    v_inserted_count INT := 0;
    v_upsert_corp_count INT := 0;
    v_upsert_mds_count INT := 0;
    v_upsert_pivot_count INT := 0;
BEGIN
    BEGIN
        -- =============================================
        -- 1. Insert mds_usd_issuer_constituents
        -- =============================================
        -- Step 1: Identify latest obs_date per mds_uid in staging
        WITH latest_dates_stg AS (
            SELECT mds_uid, MAX(obs_date) AS max_date
            FROM var.stg_mds_usd_issuer_constituents
            GROUP BY mds_uid
        ),
        latest_values_stg AS (
            SELECT r.*
            FROM var.stg_mds_usd_issuer_constituents r
            JOIN latest_dates_stg l ON r.mds_uid = l.mds_uid AND r.obs_date = l.max_date
            ORDER BY mds_uid, isin
        ),
        grouped_stg AS (
            SELECT
                mds_uid,
                ARRAY_AGG(isin ORDER BY isin) AS isin_group,
                MD5(CAST(ARRAY_AGG(isin ORDER BY isin) AS TEXT)) AS isin_group_md5,
                ARRAY_AGG(bond_description ORDER BY bond_description) AS bond_description_group,
                MD5(CAST(ARRAY_AGG(bond_description ORDER BY bond_description) AS TEXT)) AS bond_description_group_md5,
                MAX(obs_date) AS obs_date
            FROM latest_values_stg
            GROUP BY mds_uid
        ),
        diff AS (
            SELECT stg.*
            FROM grouped_stg stg
            LEFT JOIN var.latest_mds_usd_issuer_constituents cur ON stg.mds_uid = cur.mds_uid
            WHERE (
                stg.isin_group_md5 IS DISTINCT FROM COALESCE(cur.isin_group_md5, '') OR
                stg.bond_description_group_md5 IS DISTINCT FROM COALESCE(cur.bond_description_group_md5, '')
            )
            AND stg.obs_date > COALESCE(cur.obs_date, '-infinity')
        ),
        batch AS (
            SELECT l.*, 
              TRIM(
                REGEXP_REPLACE(
                    REGEXP_REPLACE(
                    REPLACE(
                        REPLACE(
                        REPLACE(
                            REPLACE(
                            REGEXP_REPLACE(l.recovery_rate_rm_name, '&', 'and', 'gi'),
                            ',', ''
                            ),
                            '''', ''
                        ),
                        '"', ''
                        ),
                        ' - UAT', ''
                    ),
                    'Ltd.', 'Limited', 'gi'
                    ),
                    E'\\s*\\([A-Z]{2}\\)', '', 'g'
                )
            ) AS recovery_rate_rm_name_cleaned
            FROM latest_values_stg l
            JOIN diff d ON l.mds_uid = d.mds_uid
        )
        -- Step 2: Insert new rows where group hash changed
        INSERT INTO var.mds_usd_issuer_constituents (
		  mds_uid, curve_description, isin, currency, bond_description,
		  seniority, obs_date, recovery_rate_rm_name, recovery_rate_rm_name_cleaned, created_at
		)
		SELECT 
		  mds_uid, curve_description, isin, currency, bond_description,
		  seniority, obs_date, recovery_rate_rm_name, recovery_rate_rm_name_cleaned, created_at
		FROM batch;

        GET DIAGNOSTICS v_inserted_count = ROW_COUNT;
        RAISE NOTICE 'MDS USD issuer constituents sparse insert: % rows', v_inserted_count;

        -- =============================================
        -- 2. Upsert into corp_issuer_recoveries
        -- =============================================
        WITH latest_dates_stg AS (
            SELECT recovery_rate_rm_name, MAX(px_date) AS max_date
            FROM var.stg_corp_issuer_recoveries
            GROUP BY recovery_rate_rm_name
        ),
        latest_values_stg AS (
            SELECT r.*
            FROM var.stg_corp_issuer_recoveries r
            JOIN latest_dates_stg l ON r.recovery_rate_rm_name = l.recovery_rate_rm_name AND r.px_date = l.max_date
        ),
        diff AS (
            SELECT stg.recovery_rate_rm_name, stg.px_date, stg.px_recovery_rate, stg.created_at,
            TRIM(
                REGEXP_REPLACE(
                    REGEXP_REPLACE(
                    REPLACE(
                        REPLACE(
                        REPLACE(
                            REPLACE(
                            REGEXP_REPLACE(stg.recovery_rate_rm_name, '&', 'and', 'gi'),
                            ',', ''
                            ),
                            '''', ''
                        ),
                        '"', ''
                        ),
                        ' - UAT', ''
                    ),
                    'Ltd.', 'Limited', 'gi'
                    ),
                    E'\\s*\\([A-Z]{2}\\)', '', 'g'
                )
            ) AS recovery_rate_rm_name_cleaned
            FROM latest_values_stg stg
            LEFT JOIN var.latest_corp_issuer_recoveries l ON stg.recovery_rate_rm_name = l.recovery_rate_rm_name
            WHERE stg.px_recovery_rate IS DISTINCT FROM COALESCE(l.px_recovery_rate, '-infinity')
              AND stg.px_date > COALESCE(l.px_date, '-infinity')
        )
        -- Step 2: Insert changes into corp_issuer_recoveries
        INSERT INTO var.corp_issuer_recoveries (
            recovery_rate_rm_name, recovery_rate_rm_name_cleaned, px_date, px_recovery_rate, created_at
        )
        SELECT
            recovery_rate_rm_name,
            recovery_rate_rm_name_cleaned,
            px_date,
            px_recovery_rate,
            created_at
        FROM diff;

        GET DIAGNOSTICS v_upsert_corp_count = ROW_COUNT;
        RAISE NOTICE 'Corp issuer upserted rows: %', v_upsert_corp_count;

        -- =============================================
        -- 3. Upsert into mds_usd_issuer
        -- =============================================
        -- Step 1: Identify latest px_date per mds_uid in staging
        WITH latest_dates_stg AS (
            SELECT mds_uid, MAX(px_date) AS max_date
            FROM var.stg_mds_usd_issuer
            GROUP BY mds_uid
        ),
        latest_values_stg AS (
            SELECT DISTINCT r.mds_uid, r.px_date, r.curve_description, r.country, r.currency, r.sector, r.rating, r.pay_freq, r.created_at
            FROM var.stg_mds_usd_issuer r
            JOIN latest_dates_stg l ON r.mds_uid = l.mds_uid AND r.px_date = l.max_date
        ),
        latest_target AS (
            SELECT DISTINCT ON (mds_uid)
                mds_uid, px_date, curve_description, country, currency, sector, rating, pay_freq
            FROM var.mds_usd_issuer
            ORDER BY mds_uid, px_date DESC
        ),
        diff AS (
            SELECT stg.mds_uid, stg.px_date, stg.curve_description, stg.country, stg.currency, stg.sector, stg.rating, stg.pay_freq, stg.created_at
            FROM latest_values_stg stg
            LEFT JOIN latest_target cur ON stg.mds_uid = cur.mds_uid
            WHERE (
                stg.curve_description IS DISTINCT FROM COALESCE(cur.curve_description, '') OR
                stg.country IS DISTINCT FROM COALESCE(cur.country, '') OR
                stg.currency IS DISTINCT FROM COALESCE(cur.currency, '') OR
                stg.sector IS DISTINCT FROM COALESCE(cur.sector, '') OR
                stg.rating IS DISTINCT FROM COALESCE(cur.rating, '') OR
                stg.pay_freq IS DISTINCT FROM COALESCE(cur.pay_freq, FLOAT8 '+infinity')
            )
            AND stg.px_date > COALESCE(cur.px_date, '-infinity')
        )
        -- Step 2: Insert changes into mds_usd_issuer
        INSERT INTO var.mds_usd_issuer
        (
            mds_uid, px_date, curve_description, curve_description_cleaned, country, currency, sector, rating, pay_freq, created_at
        )
        SELECT
            mds_uid, px_date, curve_description, 
            REGEXP_REPLACE(curve_description, E'([^\\s])\\(SP\\)', E'\\1 (SP)', 'g') as curve_description_cleaned,
            country, currency, sector, rating, pay_freq, created_at
        FROM diff;

        GET DIAGNOSTICS v_upsert_mds_count = ROW_COUNT;
        RAISE NOTICE 'MDS USD issuer upserted rows: %', v_upsert_mds_count;

        -- =============================================
        -- 4. Upsert into mds_issuer_pivot (simplified example)
        -- =============================================
        WITH pivoted_stg AS (
            SELECT
                mds_uid,
                px_date,
                MAX(CASE WHEN px_maturity = '1M' THEN px_yield END) AS px_yield_1m,
                MAX(CASE WHEN px_maturity = '3M' THEN px_yield END) AS px_yield_3m,
                MAX(CASE WHEN px_maturity = '6M' THEN px_yield END) AS px_yield_6m,
                MAX(CASE WHEN px_maturity = '12M' THEN px_yield END) AS px_yield_12m,
                MAX(CASE WHEN px_maturity = '24M' THEN px_yield END) AS px_yield_24m,
                MAX(CASE WHEN px_maturity = '36M' THEN px_yield END) AS px_yield_36m,
                MAX(CASE WHEN px_maturity = '48M' THEN px_yield END) AS px_yield_48m,
                MAX(CASE WHEN px_maturity = '60M' THEN px_yield END) AS px_yield_60m,
                MAX(CASE WHEN px_maturity = '72M' THEN px_yield END) AS px_yield_72m,
                MAX(CASE WHEN px_maturity = '84M' THEN px_yield END) AS px_yield_84m,
                MAX(CASE WHEN px_maturity = '96M' THEN px_yield END) AS px_yield_96m,
                MAX(CASE WHEN px_maturity = '108M' THEN px_yield END) AS px_yield_108m,
                MAX(CASE WHEN px_maturity = '120M' THEN px_yield END) AS px_yield_120m,
                MAX(CASE WHEN px_maturity = '180M' THEN px_yield END) AS px_yield_180m,
                MAX(CASE WHEN px_maturity = '240M' THEN px_yield END) AS px_yield_240m,
                MAX(CASE WHEN px_maturity = '300M' THEN px_yield END) AS px_yield_300m,
                MAX(CASE WHEN px_maturity = '360M' THEN px_yield END) AS px_yield_360m,
                MAX(CASE WHEN px_maturity = '1M' THEN px_spread_gov END) AS px_spread_gov_1m,
                MAX(CASE WHEN px_maturity = '3M' THEN px_spread_gov END) AS px_spread_gov_3m,
                MAX(CASE WHEN px_maturity = '6M' THEN px_spread_gov END) AS px_spread_gov_6m,
                MAX(CASE WHEN px_maturity = '12M' THEN px_spread_gov END) AS px_spread_gov_12m,
                MAX(CASE WHEN px_maturity = '24M' THEN px_spread_gov END) AS px_spread_gov_24m,
                MAX(CASE WHEN px_maturity = '36M' THEN px_spread_gov END) AS px_spread_gov_36m,
                MAX(CASE WHEN px_maturity = '48M' THEN px_spread_gov END) AS px_spread_gov_48m,
                MAX(CASE WHEN px_maturity = '60M' THEN px_spread_gov END) AS px_spread_gov_60m,
                MAX(CASE WHEN px_maturity = '72M' THEN px_spread_gov END) AS px_spread_gov_72m,
                MAX(CASE WHEN px_maturity = '84M' THEN px_spread_gov END) AS px_spread_gov_84m,
                MAX(CASE WHEN px_maturity = '96M' THEN px_spread_gov END) AS px_spread_gov_96m,
                MAX(CASE WHEN px_maturity = '108M' THEN px_spread_gov END) AS px_spread_gov_108m,
                MAX(CASE WHEN px_maturity = '120M' THEN px_spread_gov END) AS px_spread_gov_120m,
                MAX(CASE WHEN px_maturity = '180M' THEN px_spread_gov END) AS px_spread_gov_180m,
                MAX(CASE WHEN px_maturity = '240M' THEN px_spread_gov END) AS px_spread_gov_240m,
                MAX(CASE WHEN px_maturity = '300M' THEN px_spread_gov END) AS px_spread_gov_300m,
                MAX(CASE WHEN px_maturity = '360M' THEN px_spread_gov END) AS px_spread_gov_360m
            FROM var.stg_mds_usd_issuer
            GROUP BY mds_uid, px_date
            ),
            latest_target AS (
                SELECT DISTINCT ON (mds_uid)
                    mds_uid, px_date,
                    px_yield_1m, px_yield_3m, px_yield_6m, px_yield_12m, px_yield_24m,
                    px_yield_36m, px_yield_48m, px_yield_60m, px_yield_72m, px_yield_84m,
                    px_yield_96m, px_yield_108m, px_yield_120m, px_yield_180m, px_yield_240m,
                    px_yield_300m, px_yield_360m,
                    px_spread_gov_1m, px_spread_gov_3m, px_spread_gov_6m, px_spread_gov_12m,
                    px_spread_gov_24m, px_spread_gov_36m, px_spread_gov_48m, px_spread_gov_60m,
                    px_spread_gov_72m, px_spread_gov_84m, px_spread_gov_96m, px_spread_gov_108m,
                    px_spread_gov_120m, px_spread_gov_180m, px_spread_gov_240m,
                    px_spread_gov_300m, px_spread_gov_360m
                FROM var.mds_issuer_pivot
                ORDER BY mds_uid, px_date DESC
            ),
            diff AS (
                SELECT s.*
                FROM pivoted_stg s
                LEFT JOIN latest_target t ON s.mds_uid = t.mds_uid
                WHERE (
                    s.px_yield_1m IS DISTINCT FROM t.px_yield_1m OR
                    s.px_yield_3m IS DISTINCT FROM t.px_yield_3m OR
                    s.px_yield_6m IS DISTINCT FROM t.px_yield_6m OR
                    s.px_yield_12m IS DISTINCT FROM t.px_yield_12m OR
                    s.px_yield_24m IS DISTINCT FROM t.px_yield_24m OR
                    s.px_yield_36m IS DISTINCT FROM t.px_yield_36m OR
                    s.px_yield_48m IS DISTINCT FROM t.px_yield_48m OR
                    s.px_yield_60m IS DISTINCT FROM t.px_yield_60m OR
                    s.px_yield_72m IS DISTINCT FROM t.px_yield_72m OR
                    s.px_yield_84m IS DISTINCT FROM t.px_yield_84m OR
                    s.px_yield_96m IS DISTINCT FROM t.px_yield_96m OR
                    s.px_yield_108m IS DISTINCT FROM t.px_yield_108m OR
                    s.px_yield_120m IS DISTINCT FROM t.px_yield_120m OR
                    s.px_yield_180m IS DISTINCT FROM t.px_yield_180m OR
                    s.px_yield_240m IS DISTINCT FROM t.px_yield_240m OR
                    s.px_yield_300m IS DISTINCT FROM t.px_yield_300m OR
                    s.px_yield_360m IS DISTINCT FROM t.px_yield_360m OR
                    s.px_spread_gov_1m IS DISTINCT FROM t.px_spread_gov_1m OR
                    s.px_spread_gov_3m IS DISTINCT FROM t.px_spread_gov_3m OR
                    s.px_spread_gov_6m IS DISTINCT FROM t.px_spread_gov_6m OR
                    s.px_spread_gov_12m IS DISTINCT FROM t.px_spread_gov_12m OR
                    s.px_spread_gov_24m IS DISTINCT FROM t.px_spread_gov_24m OR
                    s.px_spread_gov_36m IS DISTINCT FROM t.px_spread_gov_36m OR
                    s.px_spread_gov_48m IS DISTINCT FROM t.px_spread_gov_48m OR
                    s.px_spread_gov_60m IS DISTINCT FROM t.px_spread_gov_60m OR
                    s.px_spread_gov_72m IS DISTINCT FROM t.px_spread_gov_72m OR
                    s.px_spread_gov_84m IS DISTINCT FROM t.px_spread_gov_84m OR
                    s.px_spread_gov_96m IS DISTINCT FROM t.px_spread_gov_96m OR
                    s.px_spread_gov_108m IS DISTINCT FROM t.px_spread_gov_108m OR
                    s.px_spread_gov_120m IS DISTINCT FROM t.px_spread_gov_120m OR
                    s.px_spread_gov_180m IS DISTINCT FROM t.px_spread_gov_180m OR
                    s.px_spread_gov_240m IS DISTINCT FROM t.px_spread_gov_240m OR
                    s.px_spread_gov_300m IS DISTINCT FROM t.px_spread_gov_300m OR
                    s.px_spread_gov_360m IS DISTINCT FROM t.px_spread_gov_360m
                )
                AND s.px_date > COALESCE(t.px_date, '-infinity')
            )
            INSERT INTO var.mds_issuer_pivot
            SELECT * FROM diff;

        GET DIAGNOSTICS v_upsert_pivot_count = ROW_COUNT;
        RAISE NOTICE 'MDS pivot upserted rows: %', v_upsert_pivot_count;

    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error occurred in sp_run_all_data_ingestion. Rolling back.';
        RAISE;
    END;
END;
$$;

call sp_run_all_data_ingestion();