USE DATABASE BLOOMBERG;
USE SCHEMA BBGH_ONDEMAND;

CREATE VIEW VW_BBG_EQVOL_DVD_HIST_EXP
AS
select AS_OF_DATE, BBG_TICKER, BC_EQY_DVD_HIST_ALL_ANN_DT, BC_EQY_DVD_HIST_ALL_REC_DT, BC_EQY_DVD_HIST_ALL_PAY_DT, DVD_FREQ, CP_DVD_TYP, BC_EQY_DVD_HIST_ALL_AMT, WHEN_UPDATED from BLOOMBERG.BBGH_ONDEMAND.BBG_EQVOL_DVD_HIST_EXP where AS_OF_DATE >= DATE('2025-06-16') 
union all 
select AS_OF_DATE, BBG_TICKER, BC_EQY_DVD_HIST_ALL_ANN_DT, BC_EQY_DVD_HIST_ALL_REC_DT, BC_EQY_DVD_HIST_ALL_PAY_DT, DVD_FREQ, CP_DVD_TYP, BC_EQY_DVD_HIST_ALL_AMT, WHEN_UPDATED from BLOOMBERG.BBGH_ONDEMAND.BBG_EQVOL_DVD_HIST where AS_OF_DATE < DATE('2025-06-16');


CREATE VIEW VW_BBG_EQVOL_DVD_PROJ_EXP
AS
select AS_OF_DATE, BBG_TICKER, DVD_EX_DT, DVD_DECLARED_DT, BC_BDVD_PER_SHARE_AMT, BC_BDVD_TREND, BC_BDVD_OPT_IMPLIED_RANGE_LOW, BC_BDVD_OPT_IMPLIED_RANGE_HIGH, WHEN_UPDATED from BLOOMBERG.BBGH_ONDEMAND.BBG_EQVOL_DVD_PROJ_EXP where AS_OF_DATE >= DATE('2025-06-16') 
union all 
select AS_OF_DATE, BBG_TICKER, DVD_EX_DT, DVD_DECLARED_DT, BC_BDVD_PER_SHARE_AMT, BC_BDVD_TREND, BC_BDVD_OPT_IMPLIED_RANGE_LOW, BC_BDVD_OPT_IMPLIED_RANGE_HIGH, WHEN_UPDATED from BLOOMBERG.BBGH_ONDEMAND.BBG_EQVOL_DVD_PROJ where AS_OF_DATE < DATE('2025-06-16'); 