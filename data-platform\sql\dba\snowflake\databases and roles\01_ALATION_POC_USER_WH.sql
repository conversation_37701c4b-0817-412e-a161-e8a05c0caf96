-- Create Role for Alation Service Account
USE ROLE SECURITYADMIN;
CREATE ROLE FR_DATACATALOG_ALATION;
GRANT ROLE FR_DATACATALOG_ALATION TO ROLE SYSADMIN;

-- Create Alation Service Account User
CREATE USER SVC_ALATION  DEFAULT_ROLE = FR_DATACATALOG_ALATION  TYPE='SERVICE';

ALTER USER SVC_ALATION SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwr0A5RAbeKnXhGmjKi7v
B6a6rN9nEfeaiZsnaGXXsCNQpCtIs4/5TEfW3NF+90CmLgK6uDdjZ1hYgysJa/mE
mywzZvHIhfqAjMs2qP0eJmu/v1SKyqMHEl8+5CmWRheNRRNpeuYYNhNmtc4m4Yxp
4Cl6O104DivTTHckevTfEUpYvuZnAJgGUO562jrysrh85n22Wh0mJEkj6ufmYPI8
IJUM/tK4WXAaJ226xJBGq9F986u3s3WofmBAUvE/UU9wy8s+OWd1MYXI483VSnK5
85wMk0zSa4ZK1J0mricB5f32SFKQl2I7Ta0KQH6MVw/n3pSfKBoWQycNDZL0HZPM
dQIDAQAB
-----END PUBLIC KEY-----';

GRANT ROLE FR_DATACATALOG_ALATION TO USER SVC_ALATION;
ALTER USER SVC_ALATION SET ROWS_PER_RESULTSET=0;
ALTER USER SVC_ALATION SET QUOTED_IDENTIFIERS_IGNORE_CASE = FALSE;

USE ROLE SYSADMIN ;
--create warehouse

-- Warehouse
CREATE WAREHOUSE IF NOT EXISTS ALATION_WH
    WAREHOUSE_SIZE = 'X-SMALL'  
    AUTO_SUSPEND = 60         	
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;
	
USE WAREHOUSE ALATION_WH ;


-- Set Default Warehouse

USE ROLE SECURITYADMIN;
ALTER USER SVC_ALATION SET DEFAULT_WAREHOUSE=ALATION_WH;


-- Grant Access to Warehouse

USE ROLE SYSADMIN;
GRANT USAGE ON WAREHOUSE ALATION_WH TO ROLE FR_DATACATALOG_ALATION;

-- Grant Access to ACCOUNT_USAGE

USE ROLE ACCOUNTADMIN;
USE SNOWFLAKE;
GRANT DATABASE ROLE OBJECT_VIEWER TO ROLE FR_DATACATALOG_ALATION;
GRANT DATABASE ROLE GOVERNANCE_VIEWER TO ROLE FR_DATACATALOG_ALATION;
