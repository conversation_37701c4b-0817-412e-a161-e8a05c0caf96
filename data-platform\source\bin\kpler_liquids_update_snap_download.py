#LIQUIDS

from datetime import datetime, timedelta
from kpler.sdk import Platform
from kpler.sdk.configuration import Configuration
from kpler.sdk.resources.trades_snapshot import TradesSnapshot
from kpler.sdk.resources.trades_updates import TradesUpdates
import pandas as pd
import os, json
import urllib3
from strunner import *
import logging

logging.getLogger().setLevel(logging.INFO)


setupEnvironment()
from stcommon.infra.rds.snowflake_bulk_loader import SnowflakeBulkLoader
from stcommon.infra.python.fileio import read_toml


parser = argparse.ArgumentParser(description="Process previous date")
parser.add_argument("--key", required=True)
parser.add_argument("--env", required=True)

args = parser.parse_args()

key=args.key
env=args.env

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

configPath = os.environ.get('CONFIG_PATH', os.getcwd())
with open(f'{configPath}/config.json', 'r') as f:
    config = json.load(f)

def jg_data_path():
    return config["JG_DATA_PATH"]

def jg_config_path():
    return config["JG_CONFIG_PATH"]

def read_config_secrets():
    config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
    with open(config_secret_path, 'r') as f:
        config_secret = json.load(f)
    return config_secret

config_secret=read_config_secrets()

config=read_toml(f'{configPath}/conf/sources/kpler_liquids.toml')

commod_db=config["config"][env]["database"]
commod_schema=config["config"][env]["schema"]
file_prefix=config["config"][env]["file_prefix"]

loader= SnowflakeBulkLoader(warehouse="DATA_PLATFORM_WH",database=commod_db,schema=f"{commod_db}.{commod_schema}",role="FR_DATA_PLATFORM")

query=f'select * from {commod_db}.{commod_schema}.KPLER_PRODUCTS'
cur=loader.cursor
cur.execute(query)

columns=[col[0] for col in cur.description]
results=[dict(zip(columns,row)) for row in cur.fetchall()]
 
# Configure Kpler API
config = Configuration(Platform.Liquids, config_secret["kpler_liq_user"], config_secret["kpler_liq_pass"], verify=False)
trades_snapshot_client = TradesSnapshot(config)
trades_updates_client = TradesUpdates(config)

 
# Set snapshot_date to today
snapshot_date = datetime.utcnow()
snapshot_date_str = snapshot_date.strftime('%Y%m%d')

output_folder = '/jfs/tech1/apps/rawdata/kpler/'
# output_folder = 'temp/'

text_file_path = os.path.join(output_folder, 'lastAvailableDate.txt')
 
# Retrieve snapshot data 

for res in results:
    product=res['PRODUCTS']

    if key=='HIST':
        snapshots = trades_snapshot_client.get(
            snapshot_date=snapshot_date,
            products=product,
            start_date=datetime.strptime('2020/01/01', '%Y/%m/%d'),
            columns="all"
        )
        file_name=f'liquids_snap_{res["SOURCE"]}_{key}_{file_prefix}_{snapshot_date_str}.csv'
    if key=="DAILY":
        snapshots = trades_updates_client.get(
            products=[str(product)],
            start_date=datetime.strptime(str(res['LAST_UPDATED']), '%Y-%m-%d %H:%M:%S'),
            columns="all",
            return_columns_ids="true"
        )
        file_name=f'liquids_snap_{res["SOURCE"]}_{key}_{file_prefix}_{datetime.now().strftime("%Y%m%dT%H%M%S")}.csv'

       
    output = pd.DataFrame(snapshots)
    file_path = os.path.join(output_folder, file_name)
    output.to_csv(file_path, index=False)
    logging.info(f'saved: {file_path}')
    
    # Save lastAvailableDate to text file
    last_available_date = snapshots.headers.get('lastAvailableDate')
    if last_available_date:
        date=datetime.strptime(last_available_date,'%Y-%m-%dT%H%M%S').strftime('%Y-%m-%d %H:%M:%S')
        query=f"update {commod_db}.{commod_schema}.kpler_products set LAST_UPDATED='{date}' where  Products='{product}'"
        cur.execute(query)

    logging.info(f"Saved data for today's date ({snapshot_date_str}) to {file_path}")