import snowflake.connector
import pandas as pd
from snowflake.connector.pandas_tools import write_pandas
import os
import tempfile
from typing import Dict, List
import logging

class SnowflakeAdaptor:

    __SF_ACCOUNT = "byb06077.us-east-1"
    __SF_WAREHOUSE = None
    __SF_DATABASE = None
    __SF_ROLE = None

    def __init__(self, database=None, warehouse=None, role=None):
        """
        Initialize Snowflake connection parameters
        """
        if database:
            self.__SF_DATABASE = database

        if warehouse:
            self.__SF_WAREHOUSE = warehouse

        if role:
            self.__SF_ROLE = role

        self.conn = None
        self.setup_logging()
        
    def setup_logging(self):
        """Configure basic logging"""
        # logging.basicConfig(
        #     level=logging.WARNING,
        #     format='%(asctime)s - %(levelname)s - %(message)s'
        # )
        
        self.logger = logging.getLogger('snowflake.connector')

    def connect(self, schema, role=None):
        """Establish connection to Snowflake"""
        try:
            if not role:
                role = self.__SF_ROLE
            sf_pk_file = os.getenv('SF_PK_FILE')
            sf_pk_password = os.getenv('SF_PK_PASSWORD')
            sf_username = os.getenv('SF_USERNAME')
            sf_password = os.getenv('SF_PASSWORD')
            pk_file_auth = sf_username is not None and sf_pk_file is not None \
                and sf_pk_password is not None
            user_pass_auth = sf_username is not None \
                and sf_password is not None
            if not pk_file_auth and not user_pass_auth:
                    raise ValueError('You must either provide (SF_USERNAME, SF_PK_FILE, SF_PK_PASSWORD)' \
                                     'or (SF_USERNAME, SF_PASSWORD) env vars for Snowflake auth')
            elif pk_file_auth:
                logging.info("Snowflake authentication using Private Key File...")
                sf_password = None
            elif user_pass_auth:
                # logging.info("Snowflake authentication using User/Password...")
                sf_pk_file = None
                sf_pk_password = None
            self.conn = snowflake.connector.connect( 
                user = sf_username,
                password = sf_password,
                private_key_file=sf_pk_file,
                private_key_file_pwd=sf_pk_password,
                account = self.__SF_ACCOUNT,
                warehouse = self.__SF_WAREHOUSE,
                database = self.__SF_DATABASE,
                schema = schema,
                role = role)
            self.logger.info("Successfully connected to Snowflake")
        except Exception as e:
            self.logger.error(f"Error connecting to Snowflake: {str(e)}")
            raise

    def close(self):
        """Close Snowflake connection"""
        if self.conn:
            self.conn.close()
            self.logger.info("Snowflake connection closed")

    def write_pandas_dataframe(self, schema, df: pd.DataFrame, table_name: str):
        """
        Write pandas DataFrame to Snowflake
        
        Args:
            df: Pandas DataFrame containing the data
            table_name: Target table name
        """
        try:
            self.close()
            self.connect(schema)
            success, nchunks, nrows, _ = write_pandas(
                self.conn,
                df,
                table_name,
                quote_identifiers=False
            )
            
            if success:
                self.logger.info(f"Successfully wrote {nrows} rows in {nchunks} chunks to {table_name}")
            else:
                raise Exception("Failed to write DataFrame to Snowflake")
                
        except Exception as e:
            self.logger.error(f"Error writing DataFrame: {str(e)}")
            raise

    def read_data(self, schema, query, role=None):
        try:
            self.close()
            self.connect(schema, role)
            df = pd.read_sql(query, self.conn)
            return df
        except Exception as e:
            self.logger.error(f"Error reading data from to Snowflake: {str(e)}")
            raise
    
    def execute_query(self, schema, query, role=None):
        try:
            self.close()
            self.connect(schema, role)
            cursor = self.conn.cursor()
            cursor.execute(query)
        except Exception as e:
            self.logger.error(f"Error executing query in Snowflake: {str(e)}")
            raise e
        
    def upsert_df(self, df, schema_name, table_name, match_columns):
        try:
            
            column_with_nulls = df.columns[df.isnull().any()].tolist()
            
            self.close()
            self.connect(schema_name)
            temp_table = f"{table_name}_temp"
            cursor = self.conn.cursor()
            cursor.execute(f"DROP TABLE IF EXISTS {temp_table}")
            self.conn.commit()
            
            # Creating a temp table with the same structure as the target table
            create_temp_table_query = f"CREATE TEMPORARY TABLE {temp_table} LIKE {table_name}"
            cursor.execute(create_temp_table_query)
            
            self.logger.info("Copying data from dataframe into the temporary table")
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file_name = temp_file.name
                
                if os.path.exists(temp_file_name):
                    os.remove(temp_file_name)
                df.to_csv(temp_file.name, index=False)
                
                cursor.execute(f"USE SCHEMA {schema_name}")
                
                cursor.execute(f"""
                                        CREATE OR REPLACE STAGE temp
                                        FILE_FORMAT = (TYPE = 'CSV' FIELD_OPTIONALLY_ENCLOSED_BY='"')
                                    """)
                cursor.execute(f"PUT file://{temp_file_name} @temp/tmp_data.csv")
                
                headers= tuple(df.columns.values)
                columns = f"({', '.join(headers)})"

                self.conn.cursor().execute(f"""
                    COPY INTO {temp_table} {columns}
                    FROM @temp/tmp_data.csv
                    FILE_FORMAT = (TYPE = 'CSV' FIELD_OPTIONALLY_ENCLOSED_BY = '"', SKIP_HEADER=1)
                """)

                self.logger.info("Data loaded into the temporary table successfully.")
                
                if os.path.exists(temp_file_name):
                    os.remove(temp_file_name)
                    self.logger.info(f"Temporary file {temp_file_name} has been deleted.") 
            
            set_clause = ', '.join([f"T.{col} = S.{col}" for col in df.columns if col not in match_columns])
            match_condition = ' AND '.join([f"T.{col} = S.{col}" for col in match_columns])
            
            merge_query = f"""
                MERGE INTO {table_name} AS T
                USING {temp_table} AS S
                ON {match_condition}
                WHEN MATCHED THEN
                    UPDATE SET {set_clause}
                WHEN NOT MATCHED THEN
                    INSERT ({', '.join(df.columns)}) 
                    VALUES ({', '.join(['S.' + col for col in df.columns])});
            """
            
            cursor.execute(merge_query)
            self.conn.commit()
            self.logger.info(f"Upsert operation completed successfully for table {table_name}.")
            
        except Exception as e:
            self.logger.error(f"Error during upsert operation: {e}")
            raise
                    
    def load_df(self, df, filename, schema_name, table_name, stage_name, overwrite=False):
        try:
            column_with_nulls = df.columns[df.isnull().any()].tolist()\
            
            self.close()
            self.connect(schema_name)
            temp_table = f"{table_name}_temp"
            cursor = self.conn.cursor()
            print(schema_name, table_name, stage_name)
            
            print("Copying data from dataframe into the temporary file")
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.name = filename
                temp_file_name = temp_file.name
                
                if os.path.exists(temp_file_name):
                    os.remove(temp_file_name)
                df.to_csv(temp_file.name, index=False)
        
                cursor.execute(f"USE SCHEMA {schema_name}")        
                put_query = f"PUT file://{temp_file_name} @{stage_name} AUTO_COMPRESS=TRUE OVERWRITE={overwrite};"
                cursor.execute(put_query)
                
                headers= tuple(df.columns.values)
                columns = f"({', '.join(headers)})"
                
                cursor.execute(f"""
                    COPY INTO {table_name} {columns}
                    FROM @{stage_name}/{temp_file_name}
                    FILE_FORMAT = (TYPE = 'CSV' FIELD_OPTIONALLY_ENCLOSED_BY = '"', SKIP_HEADER=1)
                """)
                
                if os.path.exists(temp_file_name):
                    os.remove(temp_file_name)
                    print(f"Temporary file {temp_file_name} has been deleted.") 
                    
            print(f"Data is loaded successfully to table {table_name}.")
            
        except Exception as e:
            print(f"Error during upsert operation: {e}")
            raise
        
# Example usage
if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER")
    df_data = sf_adaptor.read_data("BBGH_FUTURES", "select * from FUTURE_SERIES")
    print(df_data)