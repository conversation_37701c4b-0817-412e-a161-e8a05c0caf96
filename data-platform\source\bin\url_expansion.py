import argparse
import json
import os
import requests
import csv
from strunner import *
from tqdm import tqdm
import yaml
import importlib
setupEnvironment() 
from jgdata import jgdata
from stcommon.infra.rds.snowflake_bulk_loader import SnowflakeBulkLoader
from stcommon.infra.python.module import function_from_path


def update_status(cur, raw_db, commod_schema,source,url):
    update_query= f"UPDATE {raw_db}.{commod_schema}.USDA_RUN_HISTORY SET status='DONE'  , time_stamp = current_timestamp where status=  'NEW' and source='{source}' and url='{url}' "        
    cur.execute(update_query)

def get_config_for_source(source):
    ymml_file = f'{jgdata()}/conf/sources/agri_config.yaml'
    with open(ymml_file, 'r') as file:
        config_file = yaml.safe_load(file)
    config_file=config_file["Sources"][source]
    return config_file

def execute_procedure(cur, raw_db, commod_schema, procedure):
    cur.callproc(f'{raw_db}.{commod_schema}.{procedure}',[])
    result=cur.fetchone()
    print(f"Procedure_executed {procedure} :",result[0])

def get_new_urls_query_for_source(raw_db,commod_schema,source):
    query=f"select * from {raw_db}.{commod_schema}.USDA_RUN_HISTORY  where status=  'NEW' and source ='{source}'"
    return query

def get_table_names(rows):
    tables_names=rows.column("TABLE_NAME").to_pylist()
    tables_names=list(set(tables_names))
    return tables_names

def get_urls_list(results):
    return results.column("URL").to_pylist()


if __name__=="__main__":

    parser = argparse.ArgumentParser(description="Process previous date")
    parser.add_argument("--date", required=True, help="Specify the processing date in format YYYYMMDD")
    parser.add_argument("--source", required=True, help="Specify the processing date in format YYYYMMDD")

    args = parser.parse_args()
    current_date = args.date
    agri_source=args.source
    config_file=get_config_for_source(agri_source)

    raw_db=config_file["db_name"]
    commod_schema=config_file["schema_name"]
    procedure=config_file["procedure_name"]
    path=config_file["rawdata_location"]

    conn = SnowflakeBulkLoader(warehouse="DATA_PLATFORM_WH",database=raw_db,schema=f'{raw_db}.{commod_schema}',role="FR_DATA_PLATFORM")
    cur = conn.cursor
    execute_procedure(cur, raw_db,commod_schema,procedure)
    
    query1=get_new_urls_query_for_source(raw_db,commod_schema,agri_source)
    res=cur.execute(query1)
    rows=res.fetch_arrow_all()
    tables_names=[]
    if rows:
        tables_names=get_table_names(rows)

    prefix='jgdata.datasets'
    dataset='agri'

    for table in tables_names:
        print("Table Name:", table)
        query_new=query1+f" and table_name='{table}'"
        print("Query:", query_new)
        tables_url=cur.execute(query_new)
        results=tables_url.fetch_arrow_all()
        if results:
            urls= get_urls_list(results)
        
            final_data=[]
            for url in tqdm(urls):

                try:
                    funct = function_from_path(f"{prefix}.{dataset}.{agri_source}","get_response")
                    data=funct(url)
                    final_data.extend(data)
                except ModuleNotFoundError as e:
                    print(f"No get_response function found for {agri_source}")
                    url_res=requests.get(url)
                    final_data.extend(url_res.json())

                update_status(cur, raw_db, commod_schema,agri_source,url)


        if final_data :
            with open(f'{path}/{table}_{current_date}.csv', 'w', newline='', encoding='utf-8') as output_file:
                    dict_writer = csv.DictWriter(output_file, fieldnames=final_data[0].keys())
                    dict_writer.writeheader()
                    dict_writer.writerows(final_data)
            print(f"DATA SAVED: {path}/{table}_{current_date}.csv")