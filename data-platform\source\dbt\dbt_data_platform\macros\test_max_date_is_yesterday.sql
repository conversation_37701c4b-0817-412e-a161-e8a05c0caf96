-- Custom dbt test: Asserts that the max date in the model is yesterday (or Friday if today is Monday), with an option to ignore weekends


{% test test_max_date_is_yesterday(model, column_name,ignore_weekend=false) %}

    with check_date as (
    select
            max({{column_name}}) as max_date,
            case 
                when extract(dow from current_date) = 1 and {{ignore_weekend}} then current_date - interval '3 day'  
                else  current_date 
            end compare_date,
            datediff('day',max_date,compare_date) as diff
        from {{ model }}
    )
    Select
    *
    from check_date 
    where diff > 1


{% endtest %}