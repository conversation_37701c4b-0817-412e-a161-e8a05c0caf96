{{ config(
    materialized='incremental',
    unique_key=['trade_date','contract', 'strip', 'contract_type', 'product_id'],
    tags=["ice","flash","futures"]
) }}

WITH staged AS (
    SELECT
        trade_date,
        hub,
        product,
        strip,
        contract,
        contract_type,
        strike,
        settlement_price,
        net_change,
        expiration_date,
        product_id,
        filename,
        start_scan_time,
        ROW_NUMBER() OVER (PARTITION BY trade_date, contract, strip, contract_type, product_id ORDER BY start_scan_time DESC) AS row_num
    FROM {{ ref('stg_flash_futures') }}
)

SELECT
        trade_date,
        hub,
        product,
        strip,
        contract,
        contract_type,
        strike,
        settlement_price,
        net_change,
        expiration_date,
        product_id,
        filename,
        start_scan_time,
FROM staged
WHERE row_num = 1
{% if is_incremental() %}
AND start_scan_time >= (SELECT COALESCE(MAX(start_scan_time),'1900-01-01') FROM {{ this }} )
{% endif %}
