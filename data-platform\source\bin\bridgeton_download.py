import argparse
from urllib.error import HTTPError
import pandas as pd
import requests
import io
 

parser = argparse.ArgumentParser(description="Process previous date")
parser.add_argument("--date", required=True, help="Specify the processing date in format YYYYMMDD")
parser.add_argument("--key", required=True, help="Specify the processing date in format YYYYMMDD")
args = parser.parse_args()
current_date = args.date
key=args.key
path="/jfs/tech1/apps/rawdata/usda_agri"

# REST API using "connect"
def connect(url):
    try:
        response = requests.get(url)
        response.raise_for_status()
        print('HTTP connection successful')
        return response
    except HTTPError as http_err:
        print(f'HTTP error occurred: {http_err}')
    except Exception as err:
        print(f'Other error occurred: {err}')
 
 
# Configure URL variables for API request
url_api = 'https://www.bridgetongroup.com/data/apidata'
token = 'token=Z6YN2pvy'
"""
Current set up will get CTA positions. You can change the "req" variable to retrieve the following files:
    - CTA top day levels file:'req=levels'
    - CTA top day positions file: 'req=positions'  
"""


if key=='BRG':
    ##### Input request type here #####
    req_positions = 'req=positions'
    req_levels    = 'req=levels'
    #Configure URL for API request
    data_request_positions = f'{url_api}?{token}&{req_positions}&mode=csv&target=download'
    data_request_levels = f'{url_api}?{token}&{req_levels}&mode=csv&target=download'
    #print(data_request)
    
    # Connect and pull data

    urlData_positions = requests.get(data_request_positions).content
    rawData_positions = pd.read_csv(io.StringIO(urlData_positions.decode('utf-8')))
    rawData_positions["Date"]=pd.to_datetime(rawData_positions["Date"])
    rawData_positions=rawData_positions[rawData_positions["Date"]==pd.Timestamp(current_date)]
    if not rawData_positions.empty:
        rawData_positions.to_csv(f"{path}/BRG_POS_{current_date}.csv",index=False)
        print(f"File Saved : BRG_POS_{current_date}.csv")

    
    urlData_levels = requests.get(data_request_levels).content
    rawData_levels = pd.read_csv(io.StringIO(urlData_levels.decode('utf-8')))
    rawData_levels["Report Date"]=pd.to_datetime(rawData_levels["Report Date"])
    rawData_levels=rawData_levels[rawData_levels["Report Date"]==pd.Timestamp(current_date)]
    if not rawData_levels.empty:
        rawData_levels.to_csv(f"{path}/BRG_LEVEL_{current_date}.csv",index=False)
        print(f"File Saved : BRG_LEVEL_{current_date}.csv")

if key=='COT':

    ##### Input request type here #####
    # Daily COT Data
    req_cot_lots = 'req=COT_BRG_Lots_Energy_Ags.csv'     # Lots
    req_cot_notional = 'req=COT_BRG_Notional_Energy_Ags.csv' # Notional

    #Configure URL for API request
    data_request_cot_lots = f'{url_api}?{token}&{req_cot_lots}&mode=csv&target=download'
    data_request_cot_notional = f'{url_api}?{token}&{req_cot_notional}&mode=csv&target=download'
    #print(data_request)
    
    # Connect and pull data
    
    urlData_cots_lots = requests.get(data_request_cot_lots).content
    rawData_cots_lots = pd.read_csv(io.StringIO(urlData_cots_lots.decode('utf-8')))
    rawData_cots_lots["Date"]=pd.to_datetime(rawData_cots_lots["Date"])
    rawData_cots_lots=rawData_cots_lots[rawData_cots_lots["Date"]==pd.Timestamp(current_date)]
    if not rawData_cots_lots.empty:
        rawData_cots_lots.to_csv(f"{path}/COTS_LOTS_{current_date}.csv",index=False)
        print(f"File Saved : COTS_LOTS_{current_date}.csv")
    
    urlData_cot_notional = requests.get(data_request_cot_notional).content
    rawData_cot_notional = pd.read_csv(io.StringIO(urlData_cot_notional.decode('utf-8')))
    rawData_cot_notional["Date"]=pd.to_datetime(rawData_cot_notional["Date"])
    rawData_cot_notional=rawData_cot_notional[rawData_cot_notional["Date"]==pd.Timestamp(current_date)]
    if not rawData_cot_notional.empty:
        rawData_cot_notional.to_csv(f"{path}/COTS_Notional_{current_date}.csv",index=False)
        print(f"File Saved : COTS_Notional_{current_date}.csv")





