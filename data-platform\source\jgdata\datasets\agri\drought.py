
import pandas as pd
import requests
import yaml
from jgdata import jgdata
from stcommon.infra.rds.snowflake_bulk_loader import SnowflakeBulkLoader

def get_config_for_source(source):
    ymml_file = f'{jgdata()}/conf/sources/agri_config.yaml'
    with open(ymml_file, 'r') as file:
        config_file = yaml.safe_load(file)
    config_file=config_file["Sources"][source]
    return config_file

def get_max_date():
    config_file=get_config_for_source("drought")

    raw_db=config_file["db_name"]
    commod_schema=config_file["schema_name"]

    conn = SnowflakeBulkLoader(warehouse="DATA_PLATFORM_WH",database=raw_db,schema=f'{raw_db}.{commod_schema}',role="FR_DATA_PLATFORM")
    cur = conn.cursor

    max_date_query=f'select max(map_date) from {raw_db}.{commod_schema}.climate_division_statistics_raw'
    max_date=cur.execute(max_date_query).fetchone()[0]
    return max_date

max_date=get_max_date() 


def get_response(url):
    global max_date

    url_res=requests.get(url,headers={'Accept':"application/json"})
    data=url_res.json()
    data_df=pd.DataFrame(data)
    df1=data_df[pd.to_datetime(data_df["mapDate"]).dt.date>max_date]
    data=df1.to_dict(orient='records')
    data=[{**item, 'Area_type': url[-1] } for  item in  data]
    return data
