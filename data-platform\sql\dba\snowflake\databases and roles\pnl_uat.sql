use role SYSADMIN;

CREATE DATABASE PNL_UAT;

USE DATABASE PNL_UAT;

-- Warehouse
CREATE WAREHOUSE IF NOT EXISTS PNL_UAT_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60           -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

use warehouse PNL_UAT_WH ;

USE ROLE SECURITYADMIN ;

CREATE ROLE IF NOT EXISTS DR_PNL_UAT_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_PNL_UAT_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_PNL_UAT_DB_OWNER;

GRANT ROLE DR_PNL_UAT_DB_OWNER TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_PNL_UAT_READ_WRITE TO ROLE DR_PNL_UAT_DB_OWNER;
GRANT ROLE DR_PNL_UAT_READ_ONLY TO ROLE DR_PNL_UAT_READ_WRITE;

USE ROLE SYSADMIN;

GRANT USAGE ON WAREHOUSE PNL_UAT_WH TO ROLE DR_PNL_UAT_READ_WRITE;
GRANT USAGE ON WAREHOUSE PNL_UAT_WH TO ROLE DR_PNL_UAT_DB_OWNER;

GRANT OWNERSHIP ON DATABASE PNL_UAT TO ROLE DR_PNL_UAT_DB_OWNER;

GRANT USAGE ON DATABASE PNL_UAT TO ROLE DR_PNL_UAT_READ_ONLY;
GRANT USAGE ON DATABASE PNL_UAT TO ROLE DR_PNL_UAT_READ_WRITE;

GRANT ROLE DR_PNL_UAT_DB_OWNER TO ROLE  FR_FINANCE_IT;
GRANT ROLE DR_PNL_UAT_READ_WRITE TO USER SVC_PNL ;

CREATE SCHEMA PNL_UAT.FINANCE;

GRANT OWNERSHIP ON SCHEMA PNL_UAT.FINANCE to ROLE DR_PNL_UAT_DB_OWNER;

GRANT USAGE ON SCHEMA PNL_UAT.FINANCE TO ROLE DR_PNL_UAT_READ_WRITE;
GRANT USAGE ON SCHEMA PNL_UAT.FINANCE TO ROLE DR_PNL_UAT_READ_ONLY;

GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA PNL_UAT.FINANCE TO ROLE DR_PNL_UAT_DB_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS  IN SCHEMA PNL_UAT.FINANCE TO ROLE DR_PNL_UAT_DB_OWNER;

GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA PNL_UAT.FINANCE TO ROLE DR_PNL_UAT_READ_WRITE;
GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE VIEWS  IN SCHEMA PNL_UAT.FINANCE TO ROLE DR_PNL_UAT_READ_WRITE;

