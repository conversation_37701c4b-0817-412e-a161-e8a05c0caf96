from jgdata.datasets.spglobal import *

SCHEMA = SchemaRegistry.register('spglobal.markit',{
  'sf_early':{
    'date':primarykey(np.datetime64),
    'availDate':primarykey(np.datetime64),
    'datadate':primarykey(np.datetime64),
    'sedol':primarykey(str),
    'dxlid':primarykey(str),
    'isin':str,
    'cusip':str,
    'quick':str,
    'instrumentname':str,
    'marketarea':str,
    'valueonloan':np.float64,
    'quantityonloan':np.float64,
    'lendablevalue':np.float64,
    'lendablequantity':np.float64,
    'lendervalueonloan':np.float64,
    'lenderquantityonloan':np.float64,
    'utilisation':np.float64,
    'averagetenure':np.float64,
    'transactioncount':np.float64,
    'activelendablevalue':np.float64,
    'activelendablequantity':np.float64,
    'activeavailablevalue':np.float64,
    'activeavailablequantity':np.float64,
    'activeutilisation':np.float64,
    'activeutilisationbyquantity':np.float64,
    'utilisationbyquantity':np.float64,
    'dcbs':np.float64,
    'dns':np.float64,
    'dips':np.float64,
    'dimv':np.float64,
    'dps':np.float64,
    'dss':np.float64,
    'lenderconcentration':np.float64,
    'lendermarketshare1':np.float64,
    'lendermarketshare2':np.float64,
    'inventoryconcentration':np.float64,
    'inventorymarketshare1':np.float64,
    'inventorymarketshare2':np.float64,
    'borrowerconcentration':np.float64,
    'borrowermarketshare1':np.float64,
    'borrowermarketshare2':np.float64,
    'shortloanquantity':np.float64,
    'shortloanvalue':np.float64,
    'indicativefee':np.float64,
    'indicativerebate':np.float64,
    'bbgid':str,
    'bb_ticker':str,
    'available_quantity_stability':np.float64,
    'available_value_stability':np.float64,
    'lendable_quantity_stability':np.float64,
    'lendable_value_stability':np.float64,
    'lender_quantity_on_loan_stability':np.float64,
    'lender_value_on_loan_stability':np.float64,
    'indicativefee1day':np.float64,
    'indicativefee7day':np.float64,
    'indicativerebate1day':np.float64,
    'indicativerebate7day':np.float64
  },

  'sf_final':{
    'date':primarykey(np.datetime64),
    'availDate':primarykey(np.datetime64),
    'datadate':primarykey(np.datetime64),
    'sedol':primarykey(str),
    'dxlid':primarykey(str),
    'isin':str,
    'cusip':str,
    'quick':str,
    'instrumentname':str,
    'marketarea':str,
    'valueonloan':np.float64,
    'quantityonloan':np.float64,
    'lendablevalue':np.float64,
    'lendablequantity':np.float64,
    'lendervalueonloan':np.float64,
    'lenderquantityonloan':np.float64,
    'utilisation':np.float64,
    'averagetenure':np.float64,
    'transactioncount':np.float64,
    'activelendablevalue':np.float64,
    'activelendablequantity':np.float64,
    'activeavailablevalue':np.float64,
    'activeavailablequantity':np.float64,
    'activeutilisation':np.float64,
    'activeutilisationbyquantity':np.float64,
    'utilisationbyquantity':np.float64,
    'dcbs':np.float64,
    'dns':np.float64,
    'dips':np.float64,
    'dimv':np.float64,
    'dps':np.float64,
    'dss':np.float64,
    'lenderconcentration':np.float64,
    'lendermarketshare1':np.float64,
    'lendermarketshare2':np.float64,
    'inventoryconcentration':np.float64,
    'inventorymarketshare1':np.float64,
    'inventorymarketshare2':np.float64,
    'borrowerconcentration':np.float64,
    'borrowermarketshare1':np.float64,
    'borrowermarketshare2':np.float64,
    'shortloanquantity':np.float64,
    'shortloanvalue':np.float64,
    'indicativefee':np.float64,
    'indicativerebate':np.float64,
    'bbgid':str,
    'bb_ticker':str,
    'available_quantity_stability':np.float64,
    'available_value_stability':np.float64,
    'lendable_quantity_stability':np.float64,
    'lendable_value_stability':np.float64,
    'lender_quantity_on_loan_stability':np.float64,
    'lender_value_on_loan_stability':np.float64,
    'indicativefee1day':np.float64,
    'indicativefee7day':np.float64,
    'indicativerebate1day':np.float64,
    'indicativerebate7day':np.float64
  },

  'im_early':{
    'date':primarykey(np.datetime64),
    'datadate':primarykey(np.datetime64),
    'availDate':primarykey(np.datetime64),
    'sedol':primarykey(str),
    'dxlid':str,
    'isin':str,
    'cusip':str,
    'quick':str,
    'instrumentname':str,
    'marketarea':str,
    'saf':np.float64,
    'sar':np.float64,
  },

  'im_final':{
    'date':primarykey(np.datetime64),
    'datadate':primarykey(np.datetime64),
    'availDate':primarykey(np.datetime64),
    'sedol':primarykey(str),
    'dxlid': str,
    'isin':str,
    'cusip':str,
    'quick':str,
    'instrumentname':str,
    'marketarea':str,
    'saf':np.float64,
    'sar':np.float64,
  },
})
