show grants on table META.JOBS;
show grants of role DR_BBG_DLPLUS_READER;
show grants to role DR_BBG_DLPLUS_READER;
show grants of role DR_REFINITIV_READER;

show users
show roles
show schemas
show tables
show grants to user ANKITJAIN;

Hello, a Snowflake account has been created for you: 
https://byb06077.us-east-1.snowflakecomputing.com/

--- === --- === --- === --- === --- === --- === --- === --- === --- === --- === --- === --- === --- === --- ===
--- === --- === --- === --- === --- === --- === --- === --- === --- === --- === --- === --- === --- === --- ===

CREATE USER ADAMWANGNER PASSWORD='JG_Wangner2024' DISPLAY_NAME = '<PERSON>' FIRST_NAME = 'Adam' LAST_NAME = 'Wangner' EMAIL = 'Adam.<PERSON>@jainglobal.com' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_RISK TO USER ADAMWANGNER;
GRANT ROLE FR_RISK TO USER OCEANSALAZAR;
GRANT ROLE FR_RISK TO USER GEORGEBONNE;


CREATE USER YANGZHANG PASSWORD='JG_Yang2024' DISPLAY_NAME = 'Yang Zhang' FIRST_NAME = 'Yang' LAST_NAME = 'Zhang' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;

CREATE USER MAHENDRACHANDRASEKHAR PASSWORD='JG_Chandrasekhar2024' DISPLAY_NAME = 'Mahendra Chandrasekhar' FIRST_NAME = 'Mahendra' LAST_NAME = 'Chandrasekhar' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;

CREATE USER ARJUNNEHRU PASSWORD='JG_Nehru2024' DISPLAY_NAME = 'Arjun Nehru' FIRST_NAME = 'Arjun' LAST_NAME = 'Nehru' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ARJUNNEHRU;

CREATE USER ANDREWROSSLEE PASSWORD='JG_Rosslee2024' DISPLAY_NAME = 'Andrew Rosslee' FIRST_NAME = 'Andrew' LAST_NAME = 'Rosslee' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ANDREWROSSLEE;

CREATE USER OLIVIAALPERIN PASSWORD='JG_Alperin2024' DISPLAY_NAME = 'Olivia Alperin' FIRST_NAME = 'Olivia' LAST_NAME = 'Alperin' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER OLIVIAALPERIN;

CREATE USER ANKITJAIN PASSWORD='JG_Jain2024' DISPLAY_NAME = 'Ankit Jain' FIRST_NAME = 'Ankit' LAST_NAME = 'Jain' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ANKITJAIN;

CREATE USER SAMEERGUPTA PASSWORD='JG_Gupta2024' DISPLAY_NAME = 'Sameer Gupta' FIRST_NAME = 'Sameer' LAST_NAME = 'Gupta' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER SAMEERGUPTA;

CREATE USER SUJAYSAVAND PASSWORD='JG_Savand2024' DISPLAY_NAME = 'Sujay Savand' FIRST_NAME = 'Sujay' LAST_NAME = 'Savand' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER SUJAYSAVAND;

CREATE USER VASANTPATIL PASSWORD='JG_Patil2024' DISPLAY_NAME = 'Vasant Patil' FIRST_NAME = 'Vasant' LAST_NAME = 'Patil' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER VASANTPATIL;

CREATE USER VARUNCHHABRA PASSWORD='JG_Chhabra2024' DISPLAY_NAME = 'Varun Chhabra' FIRST_NAME = 'Varun' LAST_NAME = 'Chhabra' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER VARUNCHHABRA;

CREATE USER SOURAVSIVADASAN PASSWORD='JG_Sivadasan2024' DISPLAY_NAME = 'Sourav Sivadasan' FIRST_NAME = 'Sourav' LAST_NAME = 'Sivadasan' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER VARUNCHHABRA;

CREATE USER HARIKRISHNABANDEPALLI PASSWORD='JG_Bandepalli2024' DISPLAY_NAME = 'Harikrishna Bandepalli' FIRST_NAME = 'Harikrishna' LAST_NAME = 'Bandepalli' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER HARIKRISHNABANDEPALLI;

CREATE USER ANDREGIORDANI PASSWORD='JG_GIORDANI2024' DISPLAY_NAME = 'Andre Giordani' FIRST_NAME = 'Andre' LAST_NAME = 'Giordani' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ANDREGIORDANI;

CREATE USER SHEMARANDERSON PASSWORD='JG_ANDERSON2024' DISPLAY_NAME = 'Shemar Anderson' FIRST_NAME = 'Shemar' LAST_NAME = 'Anderson' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER SHEMARANDERSON;

CREATE USER PHILIPPSCHOENBAUER PASSWORD='JG_Phillip2024' DISPLAY_NAME = 'Philipp Schoenbauer' FIRST_NAME = 'Philipp' LAST_NAME = 'Schoenbauer' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER PHILIPPSCHOENBAUER;
GRANT ROLE FR_QUANTMODELING TO USER PHILIPPSCHOENBAUER;

CREATE USER ALEXANDERKOLB PASSWORD='JG_Alex2024' DISPLAY_NAME = 'Alexander Kolb' FIRST_NAME = 'Alexander' LAST_NAME = 'Kolb' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ALEXANDERKOLB;
GRANT ROLE FR_QUANTMODELING TO USER ALEXANDERKOLB;

GRANT ROLE DR_TPICAP_READER TO ROLE FR_QUANTMODELING;

CREATE ROLE IF NOT EXISTS FR_CREDIT;
GRANT ROLE DR_BBG_DLPLUS_READER TO ROLE FR_CREDIT;
CREATE USER GEORGEEDWARDS PASSWORD='JG_George2024' DISPLAY_NAME = 'George Edwards' FIRST_NAME = 'George' LAST_NAME = 'Edwards' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_CREDIT TO USER GEORGEEDWARDS;

CREATE ROLE IF NOT EXISTS FR_RISK_IT;
GRANT ROLE JG_GENERAL TO ROLE FR_RISK_IT;
CREATE USER CHARLESPEHLIVANIAN PASSWORD='JG_Charles2024' DISPLAY_NAME = 'Charles Pehlivanian' FIRST_NAME = 'Charles' LAST_NAME = 'Pehlivanian' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_RISK_IT TO USER CHARLESPEHLIVANIAN;

CREATE USER EUGENEOFRIEL PASSWORD='JG_Eugene2024' DISPLAY_NAME = 'Eugene OFriel' FIRST_NAME = 'Eugene' LAST_NAME = 'OFriel' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_RISK_IT TO USER EUGENEOFRIEL;

CREATE USER OONTONGTAN PASSWORD='JG_OonTong2024' DISPLAY_NAME = 'Oon Tong Tan' FIRST_NAME = 'Oon Tong' LAST_NAME = 'Tan' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_PLATFORM TO USER OONTONGTAN;

CREATE USER NITISHKUMAR PASSWORD='JG_Nitish2025' DISPLAY_NAME = 'Nitish Kumar' FIRST_NAME = 'Nitish' LAST_NAME = 'Kumar' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_PLATFORM TO USER NITISHKUMAR;

CREATE WAREHOUSE IF NOT EXISTS CREDIT_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 300         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE CREDIT_WH TO ROLE FR_CREDIT;


CREATE USER SVC_DATAIT
GRANT ROLE DR_BLOOMBERG_DB_READ_WRITE to USER SVC_DATAIT
ALTER USER SVC_DATAIT SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmdGl1A2E0oLIB963TG+w
fIT9Br8KWBUO/JpGPfYmmXMD29deaS5B9kWi7RIGmo3AKDbPeVvCVdmZbWmlXZXt
J5yzSjJGj9Ps/GeJES3lmo7B+CSg0tY0oFl41rLPNK3JaEavDK32ts8hSMFE3uyg
jduds/VBu+U5vq4XpbBm3vweo7h7GfF3SXVIHVBuK4zniRBgFESQUkxjXthCkAfz
fHMGcLrT5kdCD79dqHT3efGZmFM1XzYqhaVUoRrYQNHFgPs5CQa0xY7rTR9/sUKJ
crT6hHEw37UTwGsqnowsAKOCaW3G3dRXYYmhMlw6JqIfKgd1wZ9iA98pz9+YmEq0
fQIDAQAB
-----END PUBLIC KEY-----'

CREATE USER JOHNMORLEY PASSWORD='JG_Morley2024' DISPLAY_NAME = 'John Morley' FIRST_NAME = 'John' LAST_NAME = 'Morley' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL, RL_SPG_PLATTS TO USER JOHNMORLEY;

CREATE USER BENWOODS PASSWORD='JG_Woods2024' DISPLAY_NAME = 'Ben Woods' FIRST_NAME = 'Ben' LAST_NAME = 'Woods' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL, RL_SPG_PLATTS TO USER BENWOODS;

CREATE USER ANDRELEITHAEUSER PASSWORD='JG_Leithaeuser2024' DISPLAY_NAME = 'Andre Leithaeuser' FIRST_NAME = 'Andre' LAST_NAME = 'Leithaeuser' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL, RL_SPG_PLATTS TO USER ANDRELEITHAEUSER;

CREATE USER AYMENSALAH PASSWORD='JG_Salah2024' DISPLAY_NAME = 'Aymen Salah' FIRST_NAME = 'Aymen' LAST_NAME = 'Salah' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL, RL_SPG_PLATTS TO USER AYMENSALAH;

CREATE USER STEPHENALLEN PASSWORD='JG_Allen2024' DISPLAY_NAME = 'Stephen Allen' FIRST_NAME = 'Stephen' LAST_NAME = 'Allen' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL, RL_SPG_PLATTS TO USER STEPHENALLEN;

CREATE USER PAULJEFFREYS PASSWORD='JG_Jefferys2024' DISPLAY_NAME = 'Paul Jefferys' FIRST_NAME = 'Paul' LAST_NAME = 'Jefferys' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER PAULJEFFREYS;

CREATE USER JAMESOCALLAGHAN PASSWORD='JG_Ocallaghan2024' DISPLAY_NAME = 'James Ocallaghan' FIRST_NAME = 'James' LAST_NAME = 'Ocallaghan' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER JAMESOCALLAGHAN;

CREATE USER SAMCRAMER PASSWORD='JG_Cramer2024' DISPLAY_NAME = 'Sam Cramer' FIRST_NAME = 'Sam' LAST_NAME = 'Cramer' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER SAMCRAMER;

CREATE USER FERNANDOSIEVERS PASSWORD='JG_Sievers2024' DISPLAY_NAME = 'Fernando Sievers' FIRST_NAME = 'Fernando' LAST_NAME = 'Sievers' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_PLATFORM TO USER FERNANDOSIEVERS;
GRANT ROLE JG_GENERAL TO USER FERNANDOSIEVERS;
ALTER USER FERNANDOSIEVERS SET DISABLE_MFA = TRUE;

GRANT ROLE DR_SPG_XPRESSFEED_READER TO ROLE FR_DATA_PLATFORM;
GRANT ROLE FR_DATA_PLATFORM TO USER NEALACHORD;
GRANT ROLE FR_DATA_PLATFORM TO USER JUANRUBIO;
GRANT ROLE FR_DATA_PLATFORM TO USER SHEMARANDERSON;
GRANT ROLE FR_DATA_PLATFORM TO USER GABUBELLON;
GRANT ROLE FR_DATA_PLATFORM TO USER ANDREGIORDANI;


CREATE USER SVC_EU_COMMOD_RESEARCH
GRANT ROLE JG_GENERAL, RL_SPG_PLATTS to USER SVC_EU_COMMOD_RESEARCH
ALTER USER SVC_EU_COMMOD_RESEARCH SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqctER3Zzqf3huF0CrSVD
q5PF1wn5wBVHRV8ua3vmhDhyLWZ75yaLl2ivA/sJk9qeoIi1uX8gJZQ+i0ZjzYsh
LJVR8RDHmQKfh47eK77XXchmt8ABXwizD0Fo9Yq057h3x7CzHkzBNVnp0ONIaBAN
WLfOB4cICvuDjPg2JwiZlzOFah8Oaz1pAfcM1oR6OqK10CJYM2xCdohMdemOCzOD
PV34lYP8uQGnGVtrZP1+pKb9rETQmMaT+4X8N5nztw7f6OTKrcxqM2PnjbEChcZ6
EMyU/TrwJ/kD/L449KIwv6Zpd3d3vIvPOcXPHK1hpLkhIiqRLkQ7JSqzlcHP0LtO
lwIDAQAB
-----END PUBLIC KEY-----
'

CREATE USER EFREMSTERNBACH PASSWORD='JG_Sternbach2024' DISPLAY_NAME = 'Efrem Sternbach' FIRST_NAME = 'Efrem' LAST_NAME = 'Sternbach' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER EFREMSTERNBACH;
GRANT ROLE DR_ARC TO USER EFREMSTERNBACH;



CREATE ROLE IF NOT EXISTS FR_JG_INTERCONNECT_PROD; -- PROD-only Functional Role
CREATE ROLE IF NOT EXISTS FR_JG_INTERCONNECT_NONPROD; -- has access to PROD & NON-PROD data, for testing

GRANT ROLE FR_JG_INTERCONNECT_PROD TO USER SVC_CORE;
GRANT ROLE FR_JG_INTERCONNECT_PROD TO USER SVC_CORE_UAT;
GRANT ROLE FR_JG_INTERCONNECT_NONPROD TO USER SVC_CORE_UAT;

CREATE WAREHOUSE IF NOT EXISTS JG_INTERCONNECT_PROD_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60           -- Suspends after 1 minute of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE JG_INTERCONNECT_PROD_WH TO USER SVC_CORE; -- Don't grant usage to FR_JG_INTERCONNECT_PROD otherwise NONPROD users can use the WH too!

CREATE WAREHOUSE IF NOT EXISTS JG_INTERCONNECT_NONPROD_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60           -- Suspends after 1 minute of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE JG_INTERCONNECT_NONPROD_WH TO ROLE FR_JG_INTERCONNECT_PROD;
GRANT USAGE ON WAREHOUSE JG_INTERCONNECT_NONPROD_WH TO ROLE FR_JG_INTERCONNECT_NONPROD;

CREATE USER PARISPAPAKON PASSWORD='JG_Papakon2024' DISPLAY_NAME = 'Paris Papakon' FIRST_NAME = 'Paris' LAST_NAME = 'Papakon' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER PARISPAPAKON;
GRANT ROLE FR_SECURITY_MASTER_USER TO USER PARISPAPAKON;
GRANT ROLE FR_JG_INTERCONNECT_PROD TO USER PARISPAPAKON;
GRANT ROLE FR_JG_INTERCONNECT_NONPROD TO USER PARISPAPAKON;


CREATE USER ALIRAUF PASSWORD='JG_Rauf2024' DISPLAY_NAME = 'Ali Rauf' FIRST_NAME = 'Ali' LAST_NAME = 'Rauf' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ALIRAUF;

CREATE USER RAHULDESHPANDE PASSWORD='JG_Deshpande2024' DISPLAY_NAME = 'Rahul Deshpande' FIRST_NAME = 'Rahul' LAST_NAME = 'Deshpande' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER RAHULDESHPANDE;

GRANT ROLE FR_COMMOD_PM_EU TO USER SVC_EU_COMMOD_RESEARCH;

CREATE ROLE IF NOT EXISTS FR_BI_DEVELOPER;
GRANT ROLE DR_BBG_DLPLUS_READER TO ROLE FR_BI_DEVELOPER;
GRANT ROLE DR_SPG_CDS_PRICING_READER TO ROLE FR_BI_DEVELOPER;

CREATE USER ARTEMINDJIKIAN PASSWORD='JG_Artem2024' DISPLAY_NAME = 'Artem Indjikian' FIRST_NAME = 'Artem' LAST_NAME = 'Indjikian' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_BI_DEVELOPER TO USER ARTEMINDJIKIAN;
GRANT ROLE JG_GENERAL TO USER ARTEMINDJIKIAN;

CREATE ROLE IF NOT EXISTS DR_YESENERGY_READER;

GRANT ROLE DR_BBGH_FUTURES_PREMIUM TO ROLE FR_RISK;
GRANT ROLE DR_BBGH_FUTURES_BASIC TO ROLE FR_RISK;
GRANT ROLE DR_BBGH_CUSTOM TO ROLE FR_RISK;

CREATE USER SVC_DBT_PROD;
ALTER USER SVC_DBT_PROD SET RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAupBdcIGKhTllQjc675CT
BiZ4YQX2TcSxSxg48KOL/d0p79PHZpUCiqt9XH+zTgvrb/q4gdM9njWejjvVedGR
aZp0wux4YMtsxS4FDWr0mWq14B3hsMGIPJgJCC+ednAOUK85EIKV4fCmSM3+cBk8
AGpMuyzGIMdUbjgsPjv1f57E0Y55zfFRE8UFyp6I7RBm0tprXlUKIn6f6oOlaDEv
xyMrY0QcLsJODJHWcDrYPe82DjvTvD6QVuOSru37Kd8YlYwrQsg1esKsDwBc5ZPz
PbwNqS5cSG2d2eGYc6u2/P1VjxKNPTrx36pheR82ssDokSBfkJrW0q2mO4ag9xMo
6QIDAQAB';

CREATE USER SVC_DBT_UAT;
ALTER USER SVC_DBT_UAT SET RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApMxClFfiL56Qfpus+fOu
j10ihgBceWVnWflPGt60NJAdeGAQnwZD1Q6wXDgSEh1OyFq8vJtT3b3R9pjXVfwF
VTcaPfX4hZPt52tRYTEaI8Va5Kohq5qewSZLLr2XiiSyBeZj7IxGYHoNsK417i32
nbr1fUh690Oa6WU8GJHRmtLyVXGJbNOIKlWhK89Rxzn143zize3Blh9m+kURvYxp
dhfhaSG9hNVXrkdGDPgWRO+tnulp9wVuq84c//ZP4gRaPeZBFLSgwgpnuyZwIoJU
xY2i7G4M+MFJZoq9zrifsAdqsR38ZfNYlnuptMPqQ2PNnFZcmXa9Tynsby9w41Yt
9wIDAQAB';

GRANT ROLE DR_YESENERGY_READER TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_YESENERGY_READER TO ROLE FR_COMMOD_PM;

USE ROLE ACCOUNTADMIN;
GRANT ROLE FR_DATA_PLATFORM_UAT TO USER SVC_DBT_UAT;
GRANT ROLE FR_DATA_PLATFORM TO USER SVC_DBT_PROD;

CREATE USER SOPONGKIM PASSWORD='JG_Kim2025' DISPLAY_NAME = 'Sopong Kim' FIRST_NAME = 'Sopong' LAST_NAME = 'Kim' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER SOPONGKIM;
GRANT ROLE FR_RISK TO USER SOPONGKIM;

CREATE USER JONATHANKARANIAN PASSWORD='JG_Karanian2025' DISPLAY_NAME = 'Jonathan Karanian' FIRST_NAME = 'Jonathan' LAST_NAME = 'Karanian' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER JONATHANKARANIAN;
GRANT ROLE FR_QUANTMODELLING TO USER JONATHANKARANIAN;

CREATE USER TATIANAAVANESYAN PASSWORD='JG_Tatiana2025' DISPLAY_NAME = 'Tatiana Avanesyan' FIRST_NAME = 'Tatiana' LAST_NAME = 'Avanesyan' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER TATIANAAVANESYAN;

CREATE USER TRISTANFABER PASSWORD='JG_Faber2025' DISPLAY_NAME = 'Tristan Faber' FIRST_NAME = 'Tristan' LAST_NAME = 'Faber' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
ALTER USER TRISTANFABER SET RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAx3m36KAxoprh1EQFUkxn/lCHyoyeey4rKsGQX6L0lkoqcgERopxrdJVTuCLvgVBj+8Uz6svqjlOYIFll4M7aIJvV7G2K6u2qT0QB2CnhrDvnFQRir04QejxCT+LNG08x5qfJilVSufojlEzewRuzC8HP1fmdLwZLPCMGPsmQGrb1UcCeecnUkmLDO8k3B6HBctY/cDMAzqxTX1X39HA4+12i4BdAvuT70j586p2cc+YOh6RA9lbTJWU1owNSHfe+uAjxxqRjBCdjjcVbztjFjEyp6x6vCt9nqdRxO/gQ5RGXITmtletQr6wvMMdEG+mJ5IvMeLwu2BYQPalRzo+VtQIDAQAB';
GRANT ROLE JG_GENERAL TO USER TRISTANFABER;


CREATE ROLE IF NOT EXISTS FR_COMPLIANCE_IT; -- PROD-only Functional Role
CREATE ROLE IF NOT EXISTS FR_COMPLIANCE_IT_NONPROD; -- has access to PROD & NON-PROD data, for testing

CREATE USER ANACHISHOLM PASSWORD='JG_Chisholm2025' DISPLAY_NAME = 'Ana Chisholm' FIRST_NAME = 'Ana' LAST_NAME = 'Chisholm' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ANACHISHOLM;
GRANT ROLE FR_COMPLIANCE_IT TO USER ANACHISHOLM;
GRANT ROLE FR_COMPLIANCE_IT_NONPROD TO USER ANACHISHOLM;

CREATE USER SVC_COMPLIANCE;
GRANT ROLE JG_GENERAL TO USER SVC_COMPLIANCE;
GRANT ROLE FR_COMPLIANCE_IT TO USER SVC_COMPLIANCE;
ALTER USER SVC_COMPLIANCE SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsa0N9prAtUha+xtEX44/
5tsQLI3RNiJ6O4278e0b6nRP/h4BS1sCh1/3EJU/+5c6IgO17TqBKcoVzau+/x9R
aog1TT0a1S/8LnblPqI2C5UlGqjqtgtv8jEhmVr/Y3fd+gfalpxwD3zQ1HsLjfpl
QXRvk5d3x8DYFVgeMUxGfXRTnlIzFvzHq571DtG4tkP2uC3M5LAV48FtZS1IOPV6
gmaf07O3bxIzRSpVr8iKnwR6mSmcHG/SlA+BcJRkB9rYuz+OZyPR4VwHdJET+SAO
P/EzXlTPLeTZ0/IGVpHPWQ1Kb6ORa427H/JijSFbZ11JnC0ZsywhgJ3eH+91vf14
UwIDAQAB
-----END PUBLIC KEY-----
';

CREATE USER SVC_COMPLIANCE_NONPROD TYPE = SERVICE;
GRANT ROLE JG_GENERAL TO USER SVC_COMPLIANCE_NONPROD;
GRANT ROLE FR_COMPLIANCE_IT_NONPROD TO USER SVC_COMPLIANCE_NONPROD;
GRANT ROLE FR_COMPLIANCE_IT TO USER SVC_COMPLIANCE_NONPROD;
ALTER USER SVC_COMPLIANCE_NONPROD SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp1TW0D9TigAbVNiwmhl+
MCWW6Kjc+teph3n45qzUL2KMs/kI2oFxcfYRUJfdxQxDVLrrzYLs3/Mu7bBCtn6o
u/8m5q8FRN49ghwk+deoR4hq9XKmx0eH9EY120eBFn65DDkQ5wKKrwpjWvtGCRZF
WVlh4oH93He55sMjX6Jhzg48EgQ4rrFPW767BXV/VpTDj2T5vqfQ+8uh0L6QWyWc
YS6R1STcZv9Y3yQg8oBxHvtdycVkR4xNAuC67O9AjOINHARue+zft11HCV+MSkYx
/e/ggGadIWK8Kj1+uOvGuVffEWr+86ZcJ+A7YpLI0FJjZDHykMDl5jU74eQB2MCC
ywIDAQAB
-----END PUBLIC KEY----';

CREATE WAREHOUSE IF NOT EXISTS COMPLIANCE_PROD_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60           -- Suspends after 1 minute of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE COMPLIANCE_PROD_WH TO USER SVC_COMPLIANCE; -- Don't grant usage to FR_COMPLIANCE_IT otherwise NONPROD users can use the WH too!

CREATE WAREHOUSE IF NOT EXISTS COMPLIANCE_NONPROD_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60           -- Suspends after 1 minute of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE COMPLIANCE_NONPROD_WH TO ROLE FR_COMPLIANCE_IT_NONPROD;
GRANT USAGE ON WAREHOUSE COMPLIANCE_NONPROD_WH TO ROLE FR_COMPLIANCE_IT;




CREATE ROLE IF NOT EXISTS FR_JANAK;
CREATE ROLE IF NOT EXISTS FR_CRB_TRADING;
GRANT ROLE FR_JANAK TO USER TATIANAAVANESYAN;
GRANT ROLE DR_BBGH_FUTURES_BASIC TO ROLE FR_JANAK;
CREATE USER VIJAYVASUDEVAN PASSWORD='JG_Vijay2025' DISPLAY_NAME = 'Vijay Vasudevan' FIRST_NAME = 'Vijayanand' LAST_NAME = 'Vasudevan' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER VIJAYVASUDEVAN;
GRANT ROLE FR_CRB_TRADING TO USER VIJAYVASUDEVAN;
GRANT ROLE DR_BBGH_FUTURES_BASIC TO ROLE FR_CRB_TRADING;
GRANT ROLE DR_BBGH_FUTURES_PREMIUM TO ROLE FR_CRB_TRADING;

GRANT ROLE FR_DATA_PLATFORM TO USER TRISTANFABER;

CREATE ROLE IF NOT EXISTS AR_GRANT_ADMIN; 
GRANT MANAGE GRANTS ON ACCOUNT TO ROLE AR_GRANT_ADMIN;

GRANT ROLE FR_COMMOD_TECH TO USER SVC_PMD;

CREATE USER KHALEDRHIMA PASSWORD='JG_Rhima2025' DISPLAY_NAME = 'Khaled Rhima' FIRST_NAME = 'Khaled' LAST_NAME = 'Rhima' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER KHALEDRHIMA;

GRANT ROLE DR_INDEX_COMP_READER TO ROLE FR_QUANTMODELING;
GRANT ROLE JG_GENERAL TO ROLE FR_QUANTMODELING;
GRANT ROLE DR_BBG_DLPLUS_READER TO ROLE FR_QUANTMODELING

CREATE ROLE IF NOT EXISTS FR_APAC_RISK;
GRANT ROLE JG_GENERAL TO ROLE FR_APAC_RISK;
GRANT ROLE DR_ICE_READER TO ROLE FR_APAC_RISK;

CREATE USER ALEXFEHONSMITH PASSWORD='JG_Alex2025' DISPLAY_NAME = 'Alex Fehon-Smith' FIRST_NAME = 'Alex' LAST_NAME = 'Fehon-Smith' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_APAC_RISK TO USER ALEXFEHONSMITH;
GRANT ROLE FR_APAC_RISK TO USER RAJIVGUPTA;
GRANT ROLE FR_APAC_RISK TO USER PULKITVORA;
GRANT ROLE FR_APAC_RISK TO USER OONTONGTAN;

CREATE ROLE IF NOT EXISTS FR_MAXLEE;
CREATE USER KRISHNAGOEL PASSWORD='JG_Krishna2024' DISPLAY_NAME = 'Krishna Goel' FIRST_NAME = 'Krishna' LAST_NAME = 'Goel' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER KRISHNAGOEL;
GRANT ROLE FR_MAXLEE TO USER KRISHNAGOEL;
GRANT ROLE DR_BBG_DLPLUS_READER TO ROLE FR_MAXLEE;
GRANT ROLE DR_BBGH_FUTURES_BASIC TO ROLE FR_MAXLEE;
GRANT ROLE DR_BBGH_FUTURES_PREMIUM TO ROLE FR_MAXLEE;

CREATE ROLE IF NOT EXISTS FR_GGRIGOROV;
CREATE USER ALEXSAMUEL PASSWORD='JG_Alex2025' DISPLAY_NAME = 'Alex Samuel' FIRST_NAME = 'Alex' LAST_NAME = 'Samuel' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ALEXSAMUEL;
GRANT ROLE FR_GGRIGOROV TO USER ALEXSAMUEL;
GRANT ROLE DR_IVYDBUS_READER TO USER ALEXSAMUEL;

CREATE USER LULU PASSWORD='JG_LuLu2025' DISPLAY_NAME = 'Lu Lu' FIRST_NAME = 'Lu' LAST_NAME = 'Lu' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER LULU;
GRANT ROLE FR_RISK TO USER LULU;
GRANT ROLE DR_ARAUF_READER TO ROLE FR_RISK;
GRANT ROLE FR_RISK TO USER PULKITVORA;

CREATE USER ZOHREHEBADI PASSWORD='JG_Zohreh2025' DISPLAY_NAME = 'Zohreh Ebadi' FIRST_NAME = 'Zohreh' LAST_NAME = 'Ebadi' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ZOHREHEBADI;
GRANT ROLE FR_MAXLEE TO USER ZOHREHEBADI;

CREATE USER MAXLEE PASSWORD='JG_Max2025' DISPLAY_NAME = 'Max Lee' FIRST_NAME = 'Max' LAST_NAME = 'Lee' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER MAXLEE;
GRANT ROLE FR_MAXLEE TO USER MAXLEE;

CREATE USER JACKGUAN PASSWORD='JG_Jack2025' DISPLAY_NAME = 'Jack Guan' FIRST_NAME = 'Jack' LAST_NAME = 'Guan' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER JACKGUAN;
GRANT ROLE FR_MAXLEE TO USER JACKGUAN;

CREATE ROLE IF NOT EXISTS FR_COMMOD_QUANT_US;
CREATE ROLE IF NOT EXISTS FR_AMIRRAVAN;
CREATE ROLE IF NOT EXISTS FR_EQDELTAONE;


========================== PV 15-May-2025 ===========================

GRANT ROLE DR_ICIS_HEREN_READER TO USER JONATHANKARANIAN;
GRANT ROLE DR_INDEX_COMP_READER TO ROLE FR_APAC_RISK;

CREATE ROLE IF NOT EXISTS DR_N2EX_PRICE_READER;
GRANT ROLE DR_N2EX_PRICE_READER TO ROLE FR_COMMOD_PM_EU;
GRANT ROLE DR_N2EX_PRICE_READER TO ROLE FR_QUANTMODELING;

GRANT ROLE DR_COPPCLARK_DB_READ_ONLY TO ROLE FR_QUANTMODELING;
GRANT ROLE DR_SWAPSMON_READER TO ROLE FR_QUANTMODELING;
GRANT ROLE DR_MACQUARIE_UAT_DB_READ_ONLY TO ROLE FR_QUANTMODELING;

CREATE USER MUHAMMADABDULLAH PASSWORD='JG_Abdullah2025' DISPLAY_NAME = 'Muhammad Abdullah' FIRST_NAME = 'Muhammad' LAST_NAME = 'Abdullah' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER MUHAMMADABDULLAH;
 
CREATE USER OWENLOGAN PASSWORD='JG_Logan2025' DISPLAY_NAME = 'Owen Logan' FIRST_NAME = 'Owen' LAST_NAME = 'Logan' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER OWENLOGAN;
    
=====================================================================

================== DATADEV-497 START  =================

GRANT ROLE DR_SSNC_UAT_DB_READ_ONLY TO ROLE FR_RISK;

GRANT ROLE FR_DATA_PLATFORM_UAT TO USER DAVIDMENG;

GRANT ROLE DR_JG_SNP_DB_READ_ONLY TO ROLE FR_EQVOL;

GRANT ROLE DR_JG_SNP_DB_READ_ONLY TO ROLE FR_FETECH;

CREATE USER RICHARDGOLDMAN PASSWORD='JG_Richard2025' DISPLAY_NAME = 'Richard Goldman' FIRST_NAME = 'Richard' LAST_NAME = 'Goldman' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER RICHARDGOLDMAN;
GRANT ROLE FR_FETECH TO USER RICHARDGOLDMAN;

CREATE USER RAVPRITPALKOHLI PASSWORD='JG_Ravi2025' DISPLAY_NAME = 'Ravpritpal Kohli' FIRST_NAME = 'Ravpritpal' LAST_NAME = 'Kohli' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER RAVPRITPALKOHLI;
GRANT ROLE FR_RISK TO USER RAVPRITPALKOHLI;

REVOKE ROLE DR_TPICAP_READER FROM ROLE FR_DELMEDICO;

GRANT ROLE DR_BBGH_FUTURES_PREMIUM TO ROLE FR_APAC_MACRO1;

CREATE USER RYANERICKSON PASSWORD='JG_Ryan2025' DISPLAY_NAME = 'Ryan Erickson' FIRST_NAME = 'Ryan' LAST_NAME = 'Erickson' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE FR_EQDELTAONE TO USER RYANERICKSON; GRANT ROLE JG_GENERAL TO USER RYANERICKSON;

GRANT ROLE DR_BBGH_SAMPLE TO ROLE FR_JANAK;

================== DATADEV-497 END  =================


CREATE USER SEBASTIANPANCRATZ PASSWORD='JG_Sebastian2025' DISPLAY_NAME = 'Sebastian Pancratz' FIRST_NAME = 'Sebastian' LAST_NAME = 'Pancratz' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE FR_QUANTMODELING TO USER SEBASTIANPANCRATZ; GRANT ROLE JG_GENERAL TO USER SEBASTIANPANCRATZ;
CREATE USER MARTINWOOD PASSWORD='JG_Martin2025' DISPLAY_NAME = 'Martin Wood' FIRST_NAME = 'Martin' LAST_NAME = 'Wood' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE FR_QUANTMODELING TO USER MARTINWOOD; GRANT ROLE JG_GENERAL TO USER MARTINWOOD;
CREATE USER DAVEHEREL PASSWORD='JG_Dave2025' DISPLAY_NAME = 'Dave Herel' FIRST_NAME = 'Dave' LAST_NAME = 'Herel' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE FR_COMMOD_QUANT_US TO USER DAVEHEREL; GRANT ROLE JG_GENERAL TO USER DAVEHEREL;
CREATE USER FANGWEISHI PASSWORD='JG_Fangwei2025' DISPLAY_NAME = 'Fangwei Shi' FIRST_NAME = 'Fangwei' LAST_NAME = 'Shi' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE FR_AMIRRAVAN TO USER FANGWEISHI; GRANT ROLE JG_GENERAL TO USER FANGWEISHI;
CREATE USER NOSAGIEASAOLU PASSWORD='JG_Nosagie2025' DISPLAY_NAME = 'Nosagie Asaolu' FIRST_NAME = 'Nosagie' LAST_NAME = 'Asaolu' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE FR_EQDELTAONE TO USER NOSAGIEASAOLU; GRANT ROLE JG_GENERAL TO USER NOSAGIEASAOLU;
CREATE USER KEVINSALMON PASSWORD='JG_Kevin2025' DISPLAY_NAME = 'Kevin Salmon' FIRST_NAME = 'Kevin' LAST_NAME = 'Salmon' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE FR_EQDELTAONE TO USER KEVINSALMON; GRANT ROLE JG_GENERAL TO USER KEVINSALMON;

GRANT ROLE DR_IVYDBUS_READER TO ROLE FR_EQDELTAONE;
GRANT ROLE DR_APACMETALS_PROD_WRITER TO ROLE FR_AMIRRAVAN;
GRANT ROLE FR_EQDELTAONE TO USER PULKITVORA;
GRANT ROLE FR_AMIRRAVAN TO USER PULKITVORA;
GRANT ROLE FR_AMIRRAVAN TO USER OONTONGTAN;
GRANT ROLE FR_COMMOD_QUANT_US TO USER RAJIVGUPTA;
GRANT ROLE FR_COMMOD_QUANT_US TO USER SAMUELLASKER;
GRANT ROLE FR_COMMOD_QUANT_US TO USER PULKITVORA;

CREATE WAREHOUSE IF NOT EXISTS COMMOD_QUANT_US_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 300         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE COMMOD_QUANT_US_WH TO ROLE FR_COMMOD_QUANT_US;

CREATE WAREHOUSE IF NOT EXISTS EQDELTAONE_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 300         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE EQDELTAONE_WH TO ROLE FR_EQDELTAONE;

CREATE WAREHOUSE IF NOT EXISTS APACMETALS_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 300         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE APACMETALS_WH TO ROLE FR_AMIRRAVAN;


-- FR_APAC_OPS_DEV Development
CREATE ROLE IF NOT EXISTS FR_APAC_OPS_DEV;
CREATE USER EMILLAU PASSWORD='JG_EMIL2025' DISPLAY_NAME = 'Emil Lau' FIRST_NAME = 'Emil' LAST_NAME = 'Lau' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_APAC_OPS_DEV TO USER EMILLAU;
GRANT ROLE DR_APAC_OPS_DB_READ_WRITE_DEV TO ROLE FR_APAC_OPS_DEV;


GRANT ROLE DR_CME_SECURITY_MASTER_DB_READ_ONLY to ROLE FR_QUANTMODELING;
GRANT ROLE DR_ICE_SECURITY_MASTER_DB_READ_ONLY TO ROLE FR_QUANTMODELING;

-- Accounts for Beacon to read Commodities ICE pricing data from Snowflake ICE.CTF.PRICES
CREATE USER ALEXCHEUNG PASSWORD='JG_Cheung2025' DISPLAY_NAME = 'Alex Cheung' FIRST_NAME = 'Alex' LAST_NAME = 'Cheung' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ALEXCHEUNG;
GRANT ROLE DR_ICE_READER TO USER ALEXCHEUNG;

CREATE USER SVC_BEACON;
GRANT ROLE JG_GENERAL TO USER SVC_BEACON;
GRANT ROLE DR_ICE_READER TO USER SVC_BEACON;
ALTER USER SVC_BEACON SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApP/pUKfw01p27I9gesmY
S3d4u5QvQs6Pfm5y9tYuyUuhbzx5gZmMGyFDMHM2dy1h/on4BpBe/lnY2ZD7DrxD
Efs7N0mTBw261jz/llM91wZ9yhIfcu9Yf1C/XpY8qmB/N4bEQxHlfcvOqTUmUqZ+
wTdctFSTeW+xpGyYuII/PpRzl4BwAJN9n7ZU4BJZOU9YGForpV/GNzAK+UwsXl7D
kZ02JaC3UdO0ImKkwCH/fqoTkqeHh2L+qcgh+cI7QUobGbdBQss5FuKPVx7+hFpn
3MD7MhLTIZRBLgFs6I6JoMVXTI77otm2JTWU9RH0FwU4zE5QlQa+q2HcM8rR9bQc
nwIDAQAB
-----END PUBLIC KEY-----'

CREATE ROLE IF NOT EXISTS FR_FETECH;

CREATE USER JOSHTENNEFOSS PASSWORD='JG_JOSH2025' DISPLAY_NAME = 'Josh Tennefoss' FIRST_NAME = 'Josh' LAST_NAME = 'Tennefoss' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_FETECH TO USER JOSHTENNEFOSS;
CREATE USER MARKWOLFE PASSWORD='JG_MARK2025' DISPLAY_NAME = 'Mark Wolfe' FIRST_NAME = 'Mark' LAST_NAME = 'Wolfe' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_FETECH TO USER MARKWOLFE;
CREATE USER NICKIRWIN PASSWORD='JG_NICK2025' DISPLAY_NAME = 'Nick Irwin' FIRST_NAME = 'Nick' LAST_NAME = 'Irwin' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_FETECH TO USER NICKIRWIN;
CREATE USER REIDHARTMAN PASSWORD='JG_REID2025' DISPLAY_NAME = 'Reid Hartman' FIRST_NAME = 'Reid' LAST_NAME = 'Hartman' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_FETECH TO USER REIDHARTMAN;
CREATE USER ALFREDORAMIREZ PASSWORD='JG_Alfredo2025!!!' DISPLAY_NAME = 'Alfredo Ramirez' FIRST_NAME = 'Alfredo' LAST_NAME = 'Ramirez' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_FETECH TO USER ALFREDORAMIREZ;

-- for testing access to FR_FETECH
GRANT ROLE FR_FETECH TO USER TRISTANFABER;

GRANT ROLE DR_JG_SNP_DB_READ_ONLY TO ROLE REBAL_READONLY;

CREATE USER GRIGORGRIGOROV PASSWORD='JG_Grigor2025' DISPLAY_NAME = 'Grigor Grigorov' FIRST_NAME = 'Grigor' LAST_NAME = 'Grigorov' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE FR_GGRIGOROV TO USER GRIGORGRIGOROV; GRANT ROLE JG_GENERAL TO USER GRIGORGRIGOROV;
CREATE USER NATHANAELRINGER PASSWORD='JG_Nathanael2025' DISPLAY_NAME = 'Nathanael Ringer' FIRST_NAME = 'Nathanael' LAST_NAME = 'Ringer' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE FR_GGRIGOROV TO USER NATHANAELRINGER; GRANT ROLE JG_GENERAL TO USER NATHANAELRINGER;

CREATE ROLE IF NOT EXISTS DR_QA_DIRECT_READER;

CREATE ROLE IF NOT EXISTS FR_COMMOD_AGRI_PM;
CREATE USER NICHOLASMASSARO PASSWORD='JG_Nick2025' DISPLAY_NAME = 'Nicholas Massaro' FIRST_NAME = 'Nicholas' LAST_NAME = 'Massaro' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
CREATE USER AURASALAZAR PASSWORD='JG_Aura2025' DISPLAY_NAME = 'Aura Salazar' FIRST_NAME = 'Aura' LAST_NAME = 'Salazar' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_COMMOD_AGRI_PM TO USER NICHOLASMASSARO;
GRANT ROLE FR_COMMOD_AGRI_PM TO USER AURASALAZAR;
GRANT ROLE FR_COMMOD_AGRI_PM TO USER PULKITVORA;
GRANT ROLE FR_COMMOD_AGRI_PM TO USER RAJIVGUPTA;
GRANT ROLE FR_COMMOD_AGRI_PM TO USER TRISTANFABER;

GRANT ROLE JG_GENERAL TO ROLE FR_COMMOD_AGRI_PM;
GRANT ROLE DR_USDA_DB_READ_ONLY TO ROLE FR_COMMOD_AGRI_PM;

GRANT ROLE DR_ICE_READER TO USER JONATHANKARANIAN;

CREATE WAREHOUSE IF NOT EXISTS COMMOD_AGRI_PM_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 300         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE COMMOD_AGRI_PM_WH TO ROLE FR_COMMOD_AGRI_PM;

GRANT ROLE DR_BBGH_CUSTOM TO ROLE FR_JANAK;
GRANT ROLE DR_BBGH_CUSTOM TO ROLE FR_MAXLEE;

GRANT ROLE DR_ICE_SECURITY_MASTER_DB_READ_ONLY TO ROLE FR_COMMOD_QUANT_US;
GRANT ROLE DR_ICE_READER TO ROLE FR_COMMOD_QUANT_US;
GRANT ROLE DR_SPG_PLATTS_READER TO ROLE FR_COMMOD_QUANT_US;

GRANT ROLE FR_COMMOD_QUANT_US TO USER PULKTIVORA;
GRANT ROLE FR_COMMOD_QUANT_US TO USER RAJIVGUPTA;

CREATE USER JACKWANG PASSWORD='JG_Jack2025' DISPLAY_NAME = 'Jack Wang' FIRST_NAME = 'Jack' LAST_NAME = 'Wang' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE FR_COMMOD_QUANT_US TO USER JACKWANG; GRANT ROLE JG_GENERAL TO USER JACKWANG;

CREATE USER FAHADSHEIKH PASSWORD='JG_Fahad2025' DISPLAY_NAME = 'Fahad Sheikh' FIRST_NAME = 'Fahad' LAST_NAME = 'Sheikh' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE JG_GENERAL TO USER FAHADSHEIKH;
GRANT ROLE FR_BBGH_SUPPORT TO USER FAHADSHEIKH;
GRANT ROLE FR_DATA_PLATFORM TO USER FAHADSHEIKH;
GRANT ROLE FR_DATA_PLATFORM_DEV TO USER FAHADSHEIKH;
GRANT ROLE FR_DATA_PLATFORM_UAT TO USER FAHADSHEIKH;

CREATE WAREHOUSE IF NOT EXISTS COMMOD_QUANT_US_MEDIUM_WH
    WAREHOUSE_SIZE = 'MEDIUM'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 300         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE COMMOD_QUANT_US_MEDIUM_WH TO ROLE FR_COMMOD_QUANT_US;


CREATE USER JIASHAO PASSWORD='JG_Jia2025' DISPLAY_NAME = 'Jia Shao' FIRST_NAME = 'Jia' LAST_NAME = 'Shao' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; 
GRANT ROLE JG_GENERAL TO USER JIASHAO;

CREATE ROLE IF NOT EXISTS FR_EXECUTION_TECH; -- PROD-only Functional Role
CREATE ROLE IF NOT EXISTS FR_EXECUTION_TECH_NONPROD; -- has access to PROD & NON-PROD data, for testing

GRANT ROLE FR_EXECUTION_TECH TO USER VIJAYVASUDEVAN;
GRANT ROLE FR_EXECUTION_TECH TO USER SAMCRAMER;
GRANT ROLE FR_EXECUTION_TECH TO USER JIASHAO;
GRANT ROLE FR_EXECUTION_TECH TO USER JASONMAN;
GRANT ROLE FR_EXECUTION_TECH TO USER DAVIDLAW;
GRANT ROLE FR_EXECUTION_TECH_NONPROD TO USER JIASHAO;
GRANT ROLE FR_EXECUTION_TECH_NONPROD TO USER JASONMAN;
GRANT ROLE FR_EXECUTION_TECH_NONPROD TO USER DAVIDLAW;

CREATE USER SVC_EXEC_STACK;
GRANT ROLE FR_EXECUTION_TECH TO USER SVC_EXEC_STACK;
ALTER USER SVC_EXEC_STACK SET RSA_PUBLIC_KEY='... some value has been set here ...';

CREATE USER SVC_EXEC_STACK_NONPROD;
GRANT ROLE JG_GENERAL TO USER SVC_EXEC_STACK_NONPROD;
GRANT ROLE FR_EXECUTION_TECH TO USER SVC_EXEC_STACK_NONPROD;
GRANT ROLE FR_EXECUTION_TECH_NONPROD TO USER SVC_EXEC_STACK_NONPROD;
ALTER USER SVC_EXEC_STACK_NONPROD SET RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqjIePoH+80BXJ1TRk9mQ
BjDoic4yVG1PkpCUnDqNF//G+IT4xUc7fSDUkWthSNlI5ziL1kZCB2wwNBaOmoJn
W+ANodGAlEibqK7J2kFFRICdEzyHkKI7brxopQr1PlznllacZ1qw+NY+rLcKzHaQ
JfkOXPNWLHAYLvTRFLQlRuAg9xPpMtiNZk/7ywBPGpF/ABAfzVX5y7ffUH40Bwoi
XGKkxccFFl8eV0nk+Z2CZfGIQxdQHPYc/t7GXfyQal790jQ5NmxPbhDzfd5wcqRd
rSn75wmX0xTKzdIZ8aIykLWpJFAfCNvrRCbzgp+Gosbn0YAcEAyDKVSpWWHjKduF
xwIDAQAB';

CREATE WAREHOUSE IF NOT EXISTS EXECUTION_PROD_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60           -- Suspends after 1 minute of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE EXECUTION_PROD_WH TO USER SVC_EXEC_STACK;-- Don't grant usage to FR_EXECUTION_TECH otherwise NONPROD users can use the WH too!

CREATE WAREHOUSE IF NOT EXISTS EXECUTION_NONPROD_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60           -- Suspends after 1 minute of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE EXECUTION_NONPROD_WH TO ROLE FR_EXECUTION_TECH_NONPROD;




CREATE ROLE IF NOT EXISTS FR_TRADING_PROD; -- PROD-only Functional Role
CREATE ROLE IF NOT EXISTS FR_TRADING_NONPROD; -- has access to PROD & NON-PROD data, for testing

GRANT ROLE FR_TRADING_PROD TO USER VIJAYVASUDEVAN;
GRANT ROLE FR_TRADING_PROD TO USER SAMCRAMER;
GRANT ROLE FR_TRADING_NONPROD TO USER VIJAYVASUDEVAN;
GRANT ROLE FR_TRADING_NONPROD TO USER SAMCRAMER;

CREATE USER SVC_TRADING_PROD;
GRANT ROLE FR_TRADING_PROD TO USER SVC_TRADING_PROD;
ALTER USER SVC_TRADING_PROD SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAxzDazciZ+QeRX+MdwKFG
ADjybGyX5t7eE3/64Q3kidbhL9YdruL75yk+WZXMg7t0ZVLoFMVRKLj7G+gQ3OBX
iVWKxBJOmSMCqODkDvn1bXu5VQxQYpj7gplXmAasLVrSPaZRY8/PwoMW17H+Tcvq
X3E11xhWNGimVZpymqNIz048+fWYOVathSICxkBUsLD4SIddSy3LL5rUPmfBoF1Y
APeuBx7qUM91z7EGe3esTawEoR3bNjhcmtthHJ/4bLrn5bcEOSwkJKmrhl/7C2cB
iE1PyWmvmCtKLPha9u/a7pqaajJAN8K+KWN7ugf2dndlJWbC0VPhH2qaX5/5vhO9
ML6MzHWmqhigfuqZjfDi9hdKBU82kPnFb5dDfitk0X2e4/dDDIbg8/gcEVN3k6f7
h2OskUN+FpDpMAj89tMkdqK926bk51hud3BvBW2hE+1ni/sOU+EERgJ9wXzhtxwJ
JGEBV2BuzcKWOc4p2UMmnAKXnScPgzj8WvK5j2W7Gw0LAgMBAAE=
-----END PUBLIC KEY-----';

CREATE USER SVC_TRADING_NONPROD;
GRANT ROLE FR_TRADING_PROD TO USER SVC_TRADING_NONPROD;
GRANT ROLE FR_TRADING_NONPROD TO USER SVC_TRADING_NONPROD;
ALTER USER SVC_TRADING_NONPROD SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAxzDazciZ+QeRX+MdwKFG
ADjybGyX5t7eE3/64Q3kidbhL9YdruL75yk+WZXMg7t0ZVLoFMVRKLj7G+gQ3OBX
iVWKxBJOmSMCqODkDvn1bXu5VQxQYpj7gplXmAasLVrSPaZRY8/PwoMW17H+Tcvq
X3E11xhWNGimVZpymqNIz048+fWYOVathSICxkBUsLD4SIddSy3LL5rUPmfBoF1Y
APeuBx7qUM91z7EGe3esTawEoR3bNjhcmtthHJ/4bLrn5bcEOSwkJKmrhl/7C2cB
iE1PyWmvmCtKLPha9u/a7pqaajJAN8K+KWN7ugf2dndlJWbC0VPhH2qaX5/5vhO9
ML6MzHWmqhigfuqZjfDi9hdKBU82kPnFb5dDfitk0X2e4/dDDIbg8/gcEVN3k6f7
h2OskUN+FpDpMAj89tMkdqK926bk51hud3BvBW2hE+1ni/sOU+EERgJ9wXzhtxwJ
JGEBV2BuzcKWOc4p2UMmnAKXnScPgzj8WvK5j2W7Gw0LAgMBAAE=
-----END PUBLIC KEY-----';

CREATE WAREHOUSE IF NOT EXISTS TRADING_PROD_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60           -- Suspends after 1 minute of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE TRADING_PROD_WH TO USER SVC_TRADING_PROD;-- Don't grant usage to FR_TRADING_PROD otherwise NONPROD users can use the WH too!

CREATE WAREHOUSE IF NOT EXISTS TRADING_NONPROD_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60           -- Suspends after 1 minute of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE TRADING_NONPROD_WH TO ROLE FR_TRADING_NONPROD;



CREATE USER ARJUNKRISHNA PASSWORD='JG_Arjun2025' DISPLAY_NAME = 'Arjun Krishna' FIRST_NAME = 'Arjun' LAST_NAME = 'Krishna' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE JG_GENERAL TO USER ARJUNKRISHNA;


CREATE USER DAVIDGLASS PASSWORD='JG_David2025' DISPLAY_NAME = 'David Glass' FIRST_NAME = 'David' LAST_NAME = 'Glass' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER DAVIDGLASS;
GRANT ROLE FR_JANAK TO USER DAVIDGLASS;

GRANT ROLE DR_EEX_DB_READ_ONLY TO USER JONATHANKARANIAN;
GRANT ROLE DR_EEX_DB_READ_ONLY_UAT TO USER JONATHANKARANIAN;

CREATE ROLE IF NOT EXISTS FR_CREDIT_TECH;CREATE USER FARUKHTAMBOLI PASSWORD='JG_Farukh2025' DISPLAY_NAME = 'Farukh Tamboli' FIRST_NAME = 'Farukh' LAST_NAME = 'Tamboli' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE; GRANT ROLE JG_GENERAL TO USER FARUKHTAMBOLI; GRANT ROLE FR_CREDIT_TECH TO USER FARUKHTAMBOLI;
GRANT ROLE GS_DATA_ANALYST TO ROLE FR_CREDIT_TECH;

CREATE WAREHOUSE IF NOT EXISTS CREDIT_TECH_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 300         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE CREDIT_TECH_WH TO ROLE FR_CREDIT_TECH;

CREATE USER DANIELWESTPHAL PASSWORD='JG_Westphal2025' DISPLAY_NAME = 'Daniel Westphal' FIRST_NAME = 'Daniel' LAST_NAME = 'Westphal' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER DANIELWESTPHAL;
GRANT ROLE FR_COMMOD_QUANT_US TO USER DANIELWESTPHAL;

CREATE USER ANDERSONCHEN PASSWORD='JG_Cheung2025' DISPLAY_NAME = 'Anderson Chen' FIRST_NAME = 'Anderson' LAST_NAME = 'Chen' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ANDERSONCHEN;
GRANT ROLE DR_ICE_READER TO USER ANDERSONCHEN;

CREATE ROLE IF NOT EXISTS REBAL_SHARED;
GRANT ROLE REBAL_SHARED TO ROLE FR_EQDELTAONE;

GRANT ROLE SECURITYADMIN TO USER PULKITVORA;
GRANT ROLE DR_METEOLOGICA_READER TO ROLE FR_COMMOD_QUANT_US;

CREATE ROLE IF NOT EXISTS DR_AMPERON_READER;
GRANT ROLE DR_AMPERON_READER TO ROLE FR_COMMOD_QUANT_US;

GRANT ROLE DR_AMPERON_READER TO ROLE FR_DATA_PLATFORM;

GRANT ROLE DR_SPG_CDS_PRICING_READER TO ROLE FR_GGRIGOROV;

GRANT ROLE DR_INDEX_COMP_READER TO ROLE FR_MAXLEE;
GRANT ROLE FR_MAXLEE TO USER PULKITVORA;

CREATE WAREHOUSE IF NOT EXISTS MAXLEE_XS_WH
    WAREHOUSE_SIZE = 'X-SMALL'
    AUTO_SUSPEND = 60
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE MAXLEE_XS_WH TO ROLE FR_MAXLEE;

CREATE USER SVC_GGRIGOROV;
GRANT ROLE JG_GENERAL TO USER SVC_GGRIGOROV;
GRANT ROLE FR_GGRIGOROV TO USER SVC_GGRIGOROV;

GRANT MODIFY PROGRAMMATIC AUTHENTICATION METHODS ON USER SVC_GGRIGOROV
  TO ROLE FR_GGRIGOROV;

CREATE USER SVC_EQVOL_NZHU;
GRANT ROLE JG_GENERAL TO USER SVC_EQVOL_NZHU;
GRANT ROLE FR_EQVOL TO USER SVC_EQVOL_NZHU;

GRANT MODIFY PROGRAMMATIC AUTHENTICATION METHODS ON USER SVC_EQVOL_NZHU
  TO ROLE FR_EQVOL;

GRANT ROLE DR_SPG_PLATTS_READER TO ROLE FR_COMMOD_AGRI_PM;

/******** DATADEV-364 Snowflake Enhancements for the week of 19-May-2025 *********/

CREATE USER SVC_CORE;
ALTER USER SVC_CORE SET RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyjMk0OFJ0DN81ea9OvFN
QTZDGMBQQGpLe+tB+7/FqL5CZXvfmKSyTWQMFDuoZOnfHEmDH7xGILj9hSJqoLfe
b37XAikijibaAEuB28viutWERxi8p9OElk9b5EkjyA2J3ffP44C+JfP+49kqHDBD
Aw4hMTyIjLuBoINfL+D3pUgPHhcgaGMgd5soDP5hQlNNDveXLrBaJNHt8e3kajhq
8rsIMHcoJaVf83a8XmXoluU2IYKkyuT/tRsAbFSjDAGbBqNfZmoegkOXMIcsc2rL
idPN1cPSvDTyoe0kztrqMAOH2bYWMheuJO1lxVMz92mVFH3Gb1KYhPrzzVJcoA+9
XwIDAQAB';

GRANT ROLE JG_GENERAL TO USER SVC_CORE;
GRANT ROLE GS_DATA_ANALYST_PROD TO USER SVC_CORE;

CREATE USER SVC_CORE_UAT;
ALTER USER SVC_CORE_UAT SET RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvwiXk14251ewPhaZy+Aw
RjxSVAb4a1erbYs1Qf2wC4UFB+w8+8m6Zl6bYyEdFlj94rsdKBIhT3GGsQ533WOl
mh2PhQt9fJa79u1hbzLuJcED4TVBLsH9VI0Mu8GNrdMba8c6dGiUY1Hyxs0dbWA0
mk5fsB5PGSReO+ojLNDeaV9Dh9C9eHY2alOj/vX7StcFIVWZO9Uqn9GaWH/l5RId
PVuoXv2MJesPfiJPq2aNuFmVl8N/N4H6UhNTqxkMe7sgG7i/nJhnQOu83z8tiZYd
UaaJ8To5NJkyjLp5dT2/tkrwUY5MNcftrTYAkDKhekZydRkzLObxFy0ZJcXRxiTv
LwIDAQAB';

GRANT ROLE JG_GENERAL TO USER SVC_CORE_UAT;
GRANT ROLE GS_DATA_ANALYST_UAT TO USER SVC_CORE_UAT;

CREATE USER SVC_EQDELTAONE;
ALTER USER SVC_EQDELTAONE SET RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0VIaqjZ7CNDm19gnkPTh
Ag8NK/cZhW5fYPnqjV5Zt0tq0/gBFlzwyDY58IBgoRB4RgK7BOa1tYnb4SCqPzg6
5nk6sJlUWm1OSAwUjf5xYVlSc0DHSIhlYMM1KFVWTjKqskz1/K1OtRJGKwd4ccUV
lXcPN8fTHOs1m6cUaKFjRnZ00i+JV8pkpg8S7f/yMx4N/uUFNYpfNZzeGWYahEAZ
+xZ1q6TDFKPI7zgKz1h5y/L06tkAncgrzxSfKi/+mVmK+4eZ6jW0yiLJbLwQ3Yb6
sPrSY77t7PXG8A2+8JjGVbGPZtl5kgdl2kewINbKt2lcgzje/aPeI7leZccS0xU8
GQIDAQAB';

GRANT ROLE JG_GENERAL TO USER SVC_EQDELTAONE;
GRANT ROLE FR_EQDELTAONE TO USER SVC_EQDELTAONE;

CREATE USER SVC_APACPOWER;

GRANT ROLE JG_GENERAL TO USER SVC_APACPOWER;


CREATE USER NICHOLASSTANFORD PASSWORD='JG_Stanford2025' DISPLAY_NAME = 'Nicholas Stanford' FIRST_NAME = 'Nicholas' LAST_NAME = 'Stanford' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER NICHOLASSTANFORD;
GRANT ROLE FR_DATA_PLATFORM TO USER NICHOLASSTANFORD;

GRANT ROLE DR_POSITIONS_UAT_OWNER TO ROLE FR_FINANCE_IT;
GRANT ROLE DR_SSNC_UAT_DB_READ_ONLY TO ROLE FR_FINANCE_IT;


CREATE ROLE IF NOT EXISTS FR_CORE_SERVICES;
GRANT ROLE DR_MACQUARIE_DB_READ_ONLY TO ROLE FR_CORE_SERVICES;

GRANT ROLE FR_CORE_SERVICES TO USER HARRYCHEN;

GRANT ROLE DR_MACQUARIE_DB_READ_ONLY TO ROLE FR_RISK;
GRANT ROLE DR_MACQUARIE_UAT_DB_READ_ONLY TO ROLE FR_RISK;


use role securityadmin ;


-- CREATE USER SVC_APACPOWER DISPLAY_NAME = 'SVC_APACPOWER' DEFAULT_ROLE = DR_APACPOWER_PROD_READ_WRITE ;

grant role DR_APACPOWER_PROD_READ_WRITE to user SVC_APACPOWER;



ALTER USER SVC_APACPOWER SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAojqS7OMT9uq8HxcvOx1l
kp4G0Nm2n5CYiwuUnlUK+WCyFHAskaa0tYEYIpdTLEfaZEq02SOx7h3WqtY0d5ax
rfhXsQzQR61j4NtQ4HaORfR1cQBZzS2HeBHld8YHI6JKRcY4FeMyAq65/KMW0tg8
JAgPbt+/S3gTmxMc4kkRc3sHIJHhYxToqoXaxtzOE9VUC2t0k0kIKDMtma9S+tC/
afiWeBhBAHGgfQjutUSmSmBDiaWBMoBdBaSc1Lbj/mM6x2B5watMXpOym/GYngAi
noGJ+J0k6wB18LFRyoQsoDiK+GJiEf3+BnQOyzE4IzufUBr/1B93RshDRiXwaCDQ
XwIDAQAB
-----END PUBLIC KEY-----';

GRANT ROLE DR_COPPCLARK_DB_READ_ONLY TO ROLE FR_RISK;
GRANT ROLE DR_SWAPSMON_READER TO ROLE FR_RISK;

GRANT ROLE DR_COPPCLARK_DB_READ_ONLY TO ROLE FR_COMMOD_TECH;
GRANT ROLE DR_SWAPSMON_READER TO ROLE FR_COMMOD_TECH;

GRANT ROLE DR_ICIS_HEREN_READER TO USER JOHNMORLEY;
GRANT ROLE DR_ICIS_HEREN_READER TO USER AYMENSALAH;
GRANT ROLE DR_ICIS_HEREN_READER TO USER BENWOODS;
GRANT ROLE DR_ICIS_HEREN_READER TO USER ANDRELEITHAEUSER;

CREATE ROLE IF NOT EXISTS FR_APP_PLATFORM;
CREATE USER THIRUPRATURI PASSWORD='JG_Thiru2025' DISPLAY_NAME = 'Thiru Praturi' FIRST_NAME = 'Thiru' LAST_NAME = 'Praturi' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;

GRANT ROLE JG_GENERAL TO USER THIRUPRATURI;
GRANT ROLE FR_APP_PLATFORM TO USER THIRUPRATURI;
GRANT ROLE FR_APP_PLATFORM TO USER ANDREWROSSLEE;

CREATE USER SVC_APP_PLATFORM;
ALTER USER SVC_APP_PLATFORM SET RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp0+nY2b+2wlLXkLJW+Ew
KpAtRaNXC33kh6ctVYo4NTU7U5sX/0kFNxXOHVrhu2niGKgOIlxkKAQoaL2nC6mT
o9yivP18SaKblK8Yt27VevoP4PUMOli/4QbaYSPESYe3uaVcjYZw5x0R8/jehHHN
014b2LNqyZo02pXZxaOaoOHbtIiPzO1MROoyOyM57qQ5FF2JVKVeuOdkPwpGVdtX
u0c9ajIGzK8nL2d+M/qtlMl9JnA+jNEBPBZUItssk9lNZqQs1HFQTIiaaoXNSxog
+Fu9hBgoWSaSvMU9ryCv3vkIsZ9qOmYQ0lCmXpJ0dm5kIa4B1jOAG90dPzCCw9ES
5QIDAQAB';

GRANT ROLE JG_GENERAL TO USER SVC_APP_PLATFORM;
GRANT ROLE FR_APP_PLATFORM TO USER SVC_APP_PLATFORM;

GRANT ROLE DR_BBGH_CUSTOM TO ROLE FR_APP_PLATFORM;

GRANT ROLE DR_COMMOD_OWNER TO ROLE FR_DATA_PLATFORM; # This is a tempory entitlement until DATADEV-376 is in place.

GRANT ROLE JG_GENERAL TO USER DAVIDLAW;

CREATE USER ILYAMINEVICH PASSWORD='JG_Minevich2025' DISPLAY_NAME = 'Ilya Minevich' FIRST_NAME = 'Ilya' LAST_NAME = 'Minevich' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL 		 TO USER ILYAMINEVICH;
GRANT ROLE FR_DATA_PLATFORM  TO USER ILYAMINEVICH;

GRANT ROLE DR_SWAPSMON_READER TO ROLE REBAL_READONLY; 
GRANT ROLE DR_COPPCLARK_DB_READ_ONLY TO ROLE REBAL_READONLY; 

GRANT ROLE DR_ICE_READER TO ROLE FR_APAC_POWER;
GRANT ROLE DR_ICE_SECURITY_MASTER_DB_READ_ONLY TO ROLE FR_APAC_POWER;

GRANT ROLE DR_SPG_XPRESSFEED_READER TO ROLE REBAL_READONLY;

CREATE USER FABIANSALAMO PASSWORD='JG_Salamo2025' DISPLAY_NAME = 'Fabian Salamo' FIRST_NAME = 'Fabian' LAST_NAME = 'Salamo' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_BBGH_SUPPORT 	 		TO USER FABIANSALAMO;
GRANT ROLE FR_CREDIT_TECH 	 		TO USER FABIANSALAMO;
GRANT ROLE FR_DATA_PLATFORM 	 	TO USER FABIANSALAMO;
GRANT ROLE FR_DATA_PLATFORM_DEV 	TO USER FABIANSALAMO;
GRANT ROLE FR_DATA_PLATFORM_LEADS 	TO USER FABIANSALAMO;
GRANT ROLE FR_DATA_PLATFORM_UAT 	TO USER FABIANSALAMO;
GRANT ROLE JG_GENERAL 	 			TO USER FABIANSALAMO;


CREATE USER SASHAACOSTA PASSWORD='JG_Sasha2025' DISPLAY_NAME = 'Sasha Acosta' FIRST_NAME = 'Sasha' LAST_NAME = 'Acosta' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_RISK_IT TO USER SASHAACOSTA;
GRANT ROLE DR_POSITIONS_UAT_OWNER TO USER SASHAACOSTA;
GRANT ROLE DR_POSITIONS_UAT_OWNER TO ROLE FR_RISK_IT;

GRANT ROLE DR_SPG_CDS_PRICING_READER TO ROLE REBAL_READONLY;

    /********  *********/

/******** DATADEV-396 Snowflake Enhancements Week of 02-June-2025 *********/

CREATE USER JACKSTONCHUA PASSWORD='JG_Chua2025' DISPLAY_NAME = 'Jackston Chua' FIRST_NAME = 'Jackston' LAST_NAME = 'Chua' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;


GRANT ROLE JG_GENERAL 			TO USER JACKSTONCHUA;
GRANT ROLE FR_PRODUCT_CONTROL  	TO USER JACKSTONCHUA;

GRANT ROLE DR_ARCESIUM TO ROLE FR_PRODUCT_CONTROL;


CREATE USER PAWELDRYGAS PASSWORD='JG_Drygas2025' DISPLAY_NAME = 'Pawel Drygas' FIRST_NAME = 'Pawel' LAST_NAME = 'Drygas' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL 			TO USER PAWELDRYGAS;
GRANT ROLE FR_DATA_PLATFORM  	TO USER PAWELDRYGAS;

CREATE USER WEIMINGZHANG PASSWORD='JG_Zhang2025' DISPLAY_NAME = 'Weiming Zhang' FIRST_NAME = 'Weiming' LAST_NAME = 'Zhang' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL 			TO USER WEIMINGZHANG;
GRANT ROLE FR_CREDIT  			TO USER WEIMINGZHANG;

CREATE ROLE FR_SIMONWALSH ;

GRANT ROLE JG_GENERAL 					TO ROLE FR_SIMONWALSH;
GRANT ROLE DR_SPG_CDS_PRICING_READER 	TO ROLE FR_SIMONWALSH;

CREATE USER SIMONWALSH PASSWORD='JG_Walsh2025' DISPLAY_NAME = 'Simon Walsh' FIRST_NAME = 'Simon' LAST_NAME = 'Walsh' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;

GRANT ROLE FR_SIMONWALSH	TO USER SIMONWALSH;

GRANT ROLE DR_ICE_UAT_READER  to USER MAHAMMEDSHARIF;

CREATE USER SVC_PRICING DISPLAY_NAME = 'SVC_PRICING' ;

CREATE ROLE FR_PRICING_SVC ;

GRANT ROLE DR_ARCESIUM 		TO ROLE FR_PRICING_SVC;
GRANT ROLE DR_BBGH_CUSTOM 	TO ROLE FR_PRICING_SVC;

GRANT ROLE FR_PRICING_SVC to USER SVC_PRICING ;

ALTER USER SVC_PRICING SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmGLi8LISZ5F1f07QbWCk
1Ic3JtY8606n+CcXDuGIm1vAT6CdPGGIEzrdhw1Xiwzm7ss6FQwK3OMvPEVFC5n+
AQjBj9vWXCmHoNwqZb6yCMgMI/sFQeUOghxhWQufOQurchahtwjN7TkvvKaTRE1V
5XfN8hSUIEwkIjtYOs3ViGqYSfPRwdp+7RER9C6ehkloQeuf89fqg0c7r7Ov52JC
Kb+0yynrSUfWVa7OWrOgNF26jBGTgw9j4GQU75+vcFcHou+04AqyezoBrHfLh3aD
0CQxEEtI3k+r76MhHTAcDNml6k7bjnZx1e+JQ0RdSdm7yjcBZn2GAfbQl0HQn+ej
HwIDAQAB
-----END PUBLIC KEY-----';

GRANT ROLE DR_ICE_UAT_READER  to USER HARRYCHEN;

GRANT ROLE DR_ICIS_HEREN_READER TO USER SVC_RISK;

GRANT ROLE DR_N2EX_PRICE_READER TO ROLE FR_RISK;

GRANT SELECT ON ALL TABLES IN SCHEMA ICE_UAT.MFT TO ROLE DR_ICE_UAT_READER;
GRANT SELECT ON ALL VIEWS  IN SCHEMA ICE_UAT.MFT TO ROLE DR_ICE_UAT_READER;

GRANT SELECT ON FUTURE TABLES IN SCHEMA ICE_UAT.MFT TO ROLE DR_ICE_UAT_READER;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA ICE_UAT.MFT TO ROLE DR_ICE_UAT_READER;

CREATE USER NIKOLAIBATCHVAROV PASSWORD='JG_Batchvarov2025' DISPLAY_NAME = 'Nikolai Batchvarov' FIRST_NAME = 'Nikolai' LAST_NAME = 'Batchvarov' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;

GRANT ROLE JG_GENERAL 			TO USER NIKOLAIBATCHVAROV;
GRANT ROLE FR_APP_PLATFORM  	TO USER NIKOLAIBATCHVAROV;
GRANT ROLE FR_CREDIT		  	TO USER NIKOLAIBATCHVAROV;

GRANT ROLE DR_BBG_DLPLUS_READER TO ROLE FR_APP_PLATFORM ;


CREATE USER ANILBADA PASSWORD='JG_Bada2025' DISPLAY_NAME = 'Anil Bada' FIRST_NAME = 'Anil' LAST_NAME = 'Bada' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL 			TO USER ANILBADA;
GRANT ROLE FR_APP_PLATFORM  	TO USER ANILBADA;

GRANT ROLE FR_APP_PLATFORM 		TO USER SAMEERGUPTA;

CREATE USER SUJAYSAVANDV PASSWORD='JG_SavandV2025' DISPLAY_NAME = 'Sujay SavandV' FIRST_NAME = 'Sujay' LAST_NAME = 'SavandV' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL 			TO USER SUJAYSAVANDV;
GRANT ROLE FR_APP_PLATFORM 		TO USER SUJAYSAVANDV;

CREATE USER SAUMYAMISHRA PASSWORD='JG_Mishra2025' DISPLAY_NAME = 'Saumya Mishra' FIRST_NAME = 'Saumya' LAST_NAME = 'Mishra' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL 			TO USER SAUMYAMISHRA;
GRANT ROLE FR_APP_PLATFORM 		TO USER SAUMYAMISHRA;

CREATE USER COSMINHANGANU PASSWORD='JG_Cosmin2025' DISPLAY_NAME = 'Cosmin Hanganu' FIRST_NAME = 'Cosmin' LAST_NAME = 'Hanganu' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER COSMINHANGANU;
GRANT ROLE FR_RISK TO USER COSMINHANGANU;

CREATE USER IANHARRIS PASSWORD='JG_Ian2025' DISPLAY_NAME = 'Ian Harris' FIRST_NAME = 'Ian' LAST_NAME = 'Harris' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER IANHARRIS;
GRANT ROLE FR_RISK TO USER IANHARRIS;

CREATE USER JASMINELEE PASSWORD='JG_Jasmine2025' DISPLAY_NAME = 'Jasmine Lee' FIRST_NAME = 'Jasmine' LAST_NAME = 'Lee' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER JASMINELEE;
GRANT ROLE FR_MAXLEE TO USER JASMINELEE;


GRANT ROLE DR_IVYDBUS_READER TO ROLE FR_RISK;
GRANT ROLE DR_JG_SNP_DB_READ_ONLY TO ROLE FR_JANAK;

CREATE USER SAUMYAMISHRA PASSWORD='JG_Mishra2025' DISPLAY_NAME = 'Saumya Mishra' FIRST_NAME = 'Saumya' LAST_NAME = 'Mishra' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL 			TO USER SAUMYAMISHRA;
GRANT ROLE FR_APP_PLATFORM 		TO USER SAUMYAMISHRA;

CREATE USER SVC_RISKIT DISPLAY_NAME = 'SVC_RISKIT' 

GRANT ROLE DR_ARCESIUM 		TO ROLE FR_RISK_IT;
GRANT ROLE FR_RISK_IT 	    TO USER SVC_RISKIT;


ALTER USER SVC_RISKIT SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjthzSDn/Ro9EpDEkGLS9
Ej9/jEaFIVmaS4gnPA+gbjK+giF/FKme1jFtEzmUhGyKzLI8vIgpwc6LE0fBLYZ2
TV/XU2WNYGSvvsh+5ILBsHXgzcjcPSOK5J2f0Ea9p/H7BOuj8q+AMfSNhjsV6ELf
d6Fngxyx0MFciYFcvVUZROSxOslK1mOitGYvkHn5GMsnaaTN3rvw1L7Y43W7/xVS
qDrIsPdu5a3ldyeyBwyIzhe+q5PllOtk1iyN61K432VchsFgfG1cizscHtb0EE1z
4k3RSMzrVZ6eZ6v1LkM8XfWjgDYjG5SA3HJ3fXunMwgqP5canUWQAw24LEtKtPXs
TQIDAQAB
-----END PUBLIC KEY-----';

ALTER WAREHOUSE MDO_WH
SET WAREHOUSE_SIZE = 'Medium';

GRANT ROLE DR_BBGH_CUSTOM TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_BBGH_ETF_OPTIONS TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_BBGH_FUTURES_BASIC TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_BBGH_FUTURES_PREMIUM TO ROLE FR_DATA_PLATFORM;


CREATE USER STUARTGRIMSELL PASSWORD='JG_Grimsell2025' DISPLAY_NAME = 'Stuart Grimsell' FIRST_NAME = 'Stuart' LAST_NAME = 'Grimsell' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER STUARTGRIMSELL;
GRANT ROLE FR_TRADING_PROD TO USER STUARTGRIMSELL;
GRANT ROLE FR_TRADING_NONPROD TO USER STUARTGRIMSELL;


CREATE USER DARRENSMITH PASSWORD='JG_Smith2025' DISPLAY_NAME = 'Darren Smith' FIRST_NAME = 'Darren' LAST_NAME = 'Smith' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER DARRENSMITH;
GRANT ROLE FR_TRADING_PROD TO USER DARRENSMITH;
GRANT ROLE FR_TRADING_NONPROD TO USER DARRENSMITH;


CREATE USER PAULTHOMSON PASSWORD='JG_Thomson2025' DISPLAY_NAME = 'Paul Thomson' FIRST_NAME = 'Paul' LAST_NAME = 'Thomson' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER PAULTHOMSON;
GRANT ROLE FR_TRADING_PROD TO USER PAULTHOMSON;
GRANT ROLE FR_TRADING_NONPROD TO USER PAULTHOMSON;

CREATE USER BESIMENEWTON PASSWORD='JG_Newton2025' DISPLAY_NAME = 'Besime Newton' FIRST_NAME = 'Besime' LAST_NAME = 'Newton' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER BESIMENEWTON;
GRANT ROLE FR_TRADING_PROD TO USER BESIMENEWTON;
GRANT ROLE FR_TRADING_NONPROD TO USER BESIMENEWTON;


GRANT ROLE FR_TRADING_PROD TO USER JIASHAO;
GRANT ROLE FR_TRADING_NONPROD TO USER JIASHAO;

GRANT ROLE FR_TRADING_PROD TO USER JAMESOCALLAGHAN;
GRANT ROLE FR_TRADING_NONPROD TO USER JAMESOCALLAGHAN;

CREATE ROLE FR_DATA_OPS;

GRANT ROLE JG_GENERAL to ROLE FR_DATA_OPS ;
GRANT ROLE FR_SECURITY_MASTER_USER to ROLE FR_DATA_OPS;

CREATE USER ABHINAVGUPTA PASSWORD='JG_Snowflake2025' DISPLAY_NAME = 'Abhinav Gupta' FIRST_NAME = 'Abhinav' LAST_NAME = 'Gupta' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_OPS TO USER ABHINAVGUPTA;

CREATE USER BIBHUDENDUBISWAL PASSWORD='JG_Snowflake2025' DISPLAY_NAME = 'Bibhudendu Biswal' FIRST_NAME = 'Bibhudendu' LAST_NAME = 'Biswal' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_OPS TO USER BIBHUDENDUBISWAL;

CREATE USER DEBJITBHOWMIK PASSWORD='JG_Snowflake2025' DISPLAY_NAME = 'Debjit Bhowmik' FIRST_NAME = 'Debjit' LAST_NAME = 'Bhowmik' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_OPS TO USER DEBJITBHOWMIK;

CREATE USER MUKESHKUMAR PASSWORD='JG_Snowflake2025' DISPLAY_NAME = 'Mukesh Kumar' FIRST_NAME = 'Mukesh' LAST_NAME = 'Kumar' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_OPS TO USER MUKESHKUMAR;

CREATE USER PANKAJGOPLANI  PASSWORD='JG_Snowflake2025' DISPLAY_NAME = 'Pankaj Goplani' FIRST_NAME = 'Pankaj' LAST_NAME = 'Goplani' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_OPS TO USER PANKAJGOPLANI ;

CREATE USER RICKYMONNAPPA  PASSWORD='JG_Snowflake2025' DISPLAY_NAME = 'Ricky Monnappa' FIRST_NAME = 'Ricky' LAST_NAME = 'Monnappa' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_OPS TO USER RICKYMONNAPPA ;

CREATE USER ROHITVIKAAS PASSWORD='JG_Snowflake2025' DISPLAY_NAME = 'Rohit Vikaas' FIRST_NAME = 'Rohit' LAST_NAME = 'Vikaas' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_OPS TO USER ROHITVIKAAS;

CREATE USER SAGARYADAV PASSWORD='JG_Snowflake2025' DISPLAY_NAME = 'Sagar Yadav' FIRST_NAME = 'Sagar' LAST_NAME = 'Yadav' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_OPS TO USER SAGARYADAV;

CREATE USER SONYLINGOLO PASSWORD='JG_Snowflake2025' DISPLAY_NAME = 'Sony Lingolo' FIRST_NAME = 'Sony' LAST_NAME = 'Lingolo' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_OPS TO USER SONYLINGOLO;

CREATE USER TANVIGHATNEKAR PASSWORD='JG_Snowflake2025' DISPLAY_NAME = 'Tanvi Ghatnekar' FIRST_NAME = 'Tanvi' LAST_NAME = 'Ghatnekar' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_DATA_OPS TO USER TANVIGHATNEKAR;

CREATE USER JAKEBALLANTYNE PASSWORD='JG_Ballantyne2025' DISPLAY_NAME = 'Jake Ballantyne' FIRST_NAME = 'Jake' LAST_NAME = 'Ballantyne' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL 					TO USER JAKEBALLANTYNE;
GRANT ROLE FR_QUANTMODELING 			TO USER JAKEBALLANTYNE;

GRANT ROLE DR_ICE_READER TO USER  HARRYCHEN;

GRANT ROLE DR_ICE_READER TO ROLE FR_YVESMINDREN;
GRANT ROLE DR_BBGH_FUTURES_BASIC TO ROLE FR_YVESMINDREN;

CREATE USER MARKTHATCHER PASSWORD='JG_Thatcher2025' DISPLAY_NAME = 'Mark Thatcher' FIRST_NAME = 'Mark' LAST_NAME = 'Thatcher' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER MARKTHATCHER;
GRANT ROLE FR_APAC_RISK TO USER MARKTHATCHER;

CREATE USER MANISHPATHAK PASSWORD='JG_Pathak2025' DISPLAY_NAME = 'Manish Pathak' FIRST_NAME = 'Manish' LAST_NAME = 'Pathak' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER MANISHPATHAK;
GRANT ROLE FR_SECURITY_MASTER_USER  TO USER MANISHPATHAK;

CREATE WAREHOUSE IF NOT EXISTS MDO_WH_L
    WAREHOUSE_SIZE = 'LARGE'
    AUTO_SUSPEND = 60
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE MDO_WH_L TO ROLE MDO_USER;


CREATE USER SVC_DATAIT_MDS DISPLAY_NAME = 'SVC_DATAIT_MDS' DEFAULT_ROLE = FR_DATA_PLATFORM ;

ALTER USER SVC_DATAIT_MDS SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArEevDT+KK49cmfgm/3Ja
7832kjhGWuvAvSiy19vTM41OPuqrNbVpQIm6uqz+SZ9//z/6sgq0IVPGzprSeDBC
eMTg0zMHT2JQRKQIAUNntQLDVMdSf64giWAx9Y7dWP1Nell7ND+RJM6wh/C1st40
GNPRZeXE6yI0HN5ddeXFYq3OJbuJkFzV0QCv0W3Nxzr9DS5e/1iKrc15WTFuDd4d
hjQE8h966KSJ5tyxG/yloQCfpF8LklkwIaTc+NVoJe/batSJ/H9K0gVQ7TIy4Src
e+1EHKavYUnoMRtu0doJjf/VnhqQxszHS69TQArx9bfm1nMwINr69rJZCvfuu4+z
kwIDAQAB
-----END PUBLIC KEY-----';

GRANT ROLE FR_DATA_PLATFORM TO USER SVC_DATAIT_MDS;

CREATE USER PETERCHOW PASSWORD='JG_Chow2025' DISPLAY_NAME = 'Peter Chow' FIRST_NAME = 'Peter' LAST_NAME = 'Chow' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER PETERCHOW;
GRANT ROLE FR_SECURITY_MASTER_USER  TO USER PETERCHOW;

CREATE USER MICHAELDUDA PASSWORD='JG_Duda2025' DISPLAY_NAME = 'Michael Duda' FIRST_NAME = 'Michael' LAST_NAME = 'Duda' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER MICHAELDUDA;
GRANT ROLE FR_SECURITY_MASTER_USER  TO USER MICHAELDUDA;

CREATE USER TARIQKHAN PASSWORD='JG_Khan2025' DISPLAY_NAME = 'Tariq Khan' FIRST_NAME = 'Tariq' LAST_NAME = 'Khan' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER TARIQKHAN;
GRANT ROLE FR_SECURITY_MASTER_USER  TO USER TARIQKHAN;

CREATE USER JAMESCOPELAND PASSWORD='JG_James2025' DISPLAY_NAME = 'James Copeland' FIRST_NAME = 'James' LAST_NAME = 'Copeland' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER JAMESCOPELAND;
GRANT ROLE FR_PRODUCT_CONTROL  TO USER JAMESCOPELAND;

CREATE USER DAVIDHUACK PASSWORD='JG_David2025' DISPLAY_NAME = 'David Huack' FIRST_NAME = 'David' LAST_NAME = 'Huack' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER DAVIDHUACK;
GRANT ROLE FR_PRODUCT_CONTROL  TO USER DAVIDHUACK;

GRANT ROLE DR_ICE_READER TO FR_PRODUCT_CONTROL;

CREATE USER SVC_ICE_UAT DISPLAY_NAME = 'SVC_ICE_UAT' 
ALTER USER SVC_ICE_UAT SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz0x6wmJVSBQB/+/MEWJ6
087V9OtaTkhCZSoa/UzI0w5f2pmGj6oqhGhCE10bV5568JXDECbQKxH2I1mgPSS7
CD9XI0jnMQ9i/zGLf/Pe9SiZaTFGGK/NQXmTJsfT3k+IoSybKeZxjKWyc1I9aSGd
l2VOaZBRkVMTBi2xzyb4mKNIPUtGJ/Q9erzjoaujdeao/ggqxpGGG8hzTocDscx2
lPBsjtlSf2xmlTGnmPh6ktN26kxCvmQHPL5pxfsQ03O900cBllb31c7xO+qJI9Ec
9hpZn2G2QYrIiPEJjAgyG+PeYmGoLMxOc8Sd3fJHOo/3BaA81DFygOf1bi/FS7zs
3QIDAQAB
-----END PUBLIC KEY-----';

GRANT ROLE DR_ICE_UAT_WRITER to USER SVC_ICE_UAT ;
GRANT ROLE JG_GENERAL to USER SVC_ICE_UAT ;


--Granting access

GRANT ROLE DR_MSCI_READER TO ROLE FR_APAC_RISK ;
GRANT ROLE DR_MSCI_READER TO ROLE FR_APACAUSFE_PROD ;
GRANT ROLE DR_MSCI_READER TO ROLE DR_APACAUSFE_PROD_WRITER      ;

GRANT ROLE REBAL_OWNER to user STEFANOATTANASIO;


CREATE USER STEWARTMORONEY PASSWORD='JG_Moroney2025' DISPLAY_NAME = 'Stewart Moroney' FIRST_NAME = 'Stewart' LAST_NAME = 'Moroney' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER STEWARTMORONEY;
GRANT ROLE FR_SECURITY_MASTER_USER  TO USER STEWARTMORONEY;

--- create role
use role securityadmin;

CREATE ROLE DR_CTRM_DEV_OWNER ;

--Create role heirarch
GRANT ROLE DR_CTRM_DEV_OWNER to ROLE DR_COMMOD_UAT_OWNER;

GRANT USAGE ON DATABASE COMMOD_UAT  TO ROLE DR_CTRM_DEV_OWNER;

--warehouse usage
GRANT USAGE ON WAREHOUSE COMMOD_WH TO ROLE DR_CTRM_DEV_OWNER;

-- Schema & role creation

use role ACCOUNTADMIN;

-- DROP SCHEMA COMMOD_UAT.CTRM_DEV
CREATE SCHEMA COMMOD_UAT.CTRM_DEV;

GRANT OWNERSHIP ON SCHEMA COMMOD_UAT.CTRM_DEV to ROLE DR_CTRM_DEV_OWNER;

GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_CTRM_DEV_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS  IN SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_CTRM_DEV_OWNER;


GRANT ALL ON SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_COMMOD_UAT_OWNER;
GRANT ALL ON SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_CTRM_DEV_OWNER;

GRANT USAGE ON SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_CTRM_DEV_OWNER;
GRANT USAGE ON SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_COMMOD_UAT_OWNER;

GRANT USAGE ON SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_COMMOD_UAT_DB_READ_WRITE;
GRANT USAGE ON SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_COMMOD_UAT_DB_READ_ONLY;


GRANT ALL ON FUTURE TABLES IN SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_COMMOD_UAT_OWNER;
GRANT ALL ON FUTURE VIEWS  IN SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_COMMOD_UAT_OWNER;


GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_COMMOD_UAT_DB_READ_WRITE;
GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE VIEWS  IN SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_COMMOD_UAT_DB_READ_WRITE;

GRANT SELECT ON FUTURE TABLES IN SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_COMMOD_UAT_DB_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD_UAT.CTRM_DEV TO ROLE DR_COMMOD_UAT_DB_READ_ONLY;



--creating user
CREATE USER SVC_CTRM;
GRANT ROLE DR_CTRM_DEV_OWNER TO USER SVC_CTRM;

ALTER USER SVC_CTRM SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmGM9Vr6jmc8eaqwtIlsQ
E3xs5vKBcOk/Er5hgPL/WnqBaHV4YbA2N9+mecGBvztWMXAi4kRTSBT3yrFPDiGJ
oqV43CHHe7KJaLQRz610b6Kfuh0tJKcjO0ISY6bDz77/okY2VFsUjgYuZWjsv3+p
ANiyA0eOS3YhjUyKsEyVN9VrWh8WPR4g5HKfH9O0CbubZ82qVpEt+PRt5gKtPlyQ
lFRu/Aeadhr4JyW4pZqXZZfkl/o5WlJoZwrmR79iqocURX+UG2q4amh7hBpHlemo
efS0LjjS7wNvjWcP2+wA3d7pNwEzVd+0Ly6lGMHxCrS6SDOrolKpiHk0c3tGeIvt
jQIDAQAB
-----END PUBLIC KEY-----' ;

CREATE USER NARENDRAGAUR PASSWORD='JG_Gaur2025' DISPLAY_NAME = 'Narendra Gaur' FIRST_NAME = 'Narendra' LAST_NAME = 'Gaur' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER NARENDRAGAUR;

CREATE USER JACKCHAN PASSWORD='JG_Chan2025' DISPLAY_NAME = 'Jack Chan' FIRST_NAME = 'Jack' LAST_NAME = 'Chan' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER JACKCHAN;
GRANT ROLE FR_TRADING_PROD  TO USER JACKCHAN;

GRANT ROLE JG_GENERAL                   TO USER SASHAACOSTA;
GRANT ROLE FR_COMPLIANCE_IT   			TO USER SASHAACOSTA;
GRANT ROLE FR_COMPLIANCE_IT_NONPROD	  	TO USER SASHAACOSTA;

CREATE USER JAIMEGONZALEZ PASSWORD='JG_Gonzalez2025' DISPLAY_NAME = 'Jaime Gonzalez' FIRST_NAME = 'Jaime' LAST_NAME = 'Gonzalez' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER JAIMEGONZALEZ;
GRANT ROLE FR_COMMOD_TECH    TO USER JAIMEGONZALEZ;

GRANT ROLE FR_COMMOD_TECH    TO USER ALEXCHEUNG  ;

GRANT ROLE DR_COMMOD_UAT_DB_READ_ONLY    TO ROLE FR_COMMOD_TECH  ;

GRANT ROLE DR_INDEX_COMP_READER TO ROLE FR_CORE_SERVICES ;


GRANT ROLE DR_CTRM_DEV_OWNER TO ROLE FR_COMMOD_TECH;
GRANT ROLE DR_CTRM_DEV_OWNER TO ROLE FR_COMMOD_QUANT_US;
GRANT ROLE DR_CTRM_DEV_OWNER TO ROLE FR_BI_DEVELOPER;


CREATE USER SVC_PNL;

ALTER USER SVC_PNL SET RSA_PUBLIC_KEY='-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmXR89eLbOwiiWVqxnTDx
q6o7ZbaSsDKgoN/udVwbBsGbkKIm0GrWk/tarATLiXAbWwDI6QpsQVu6MJ4hbVz/
drrkz0P7h7o7dI20+y2LZDPIXgQWRX04h/hWK8HLH6smdYHxoYUfdre5GdxJ/m3p
khQVmLEGcteK80I26SXWqy3CObo4D3oI8UZ6w8UH/47I8H5xBLL5w3k1zQpU+EkI
0xeXLrWEIqnoKowODKJSC1E8J0RkGirumCQ6EaGqN6lBPsP/KKSmO36HwbQDGGfS
9heYhRBq2fhVQf+UJBW2oRjgeJCpav0UZKtSdJ+T6XvB+OkgEdY5jkZ6Yqjl49rk
VwIDAQAB
-----END PUBLIC KEY-----' ;


GRANT ROLE FR_COMMOD_TECH  to USER ANDERSONCHEN;

GRANT USAGE ON DATABASE COMMOD TO ROLE FR_PRICING_SVC;
GRANT USAGE ON DATABASE ICE TO ROLE FR_PRICING_SVC;

CREATE USER PUNITKUMAR PASSWORD='JG_Kumar2025' DISPLAY_NAME = 'Punit Kumar' FIRST_NAME = 'Punit' LAST_NAME = 'Kumar' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER PUNITKUMAR;
GRANT ROLE FR_FINANCE_IT TO USER PUNITKUMAR;

CREATE ROLE FR_FINANCE_IT_LEADS;
GRANT ROLE FR_FINANCE_IT_LEADS TO ROLE FR_DATA_PLATFORM;

GRANT ROLE DR_PNL_UAT_DB_OWNER TO ROLE  FR_FINANCE_IT_LEADS;
GRANT ROLE DR_POSITIONS_UAT_OWNER TO ROLE  FR_FINANCE_IT_LEADS;

REVOKE ROLE DR_PNL_UAT_DB_OWNER FROM ROLE  FR_FINANCE_IT;
REVOKE ROLE DR_POSITIONS_UAT_OWNER FROM ROLE  FR_FINANCE_IT;

GRANT ROLE FR_FINANCE_IT_LEADS TO USER SANDIPPATIL;

GRANT ROLE FR_FINANCE_IT TO ROLE FR_FINANCE_IT_LEADS;

GRANT ROLE DR_PNL_UAT_READ_ONLY TO ROLE FR_FINANCE_IT;
GRANT ROLE DR_POSITIONS_UAT_DB_READ_ONLY TO ROLE FR_FINANCE_IT;

GRANT ROLE DR_INDEX_COMP_WRITER to ROLE FR_CORE_SERVICES;


CREATE USER JASPREETSINGH PASSWORD='JG_Singh2025' DISPLAY_NAME = 'Jaspreet Singh' FIRST_NAME = 'Jaspreet' LAST_NAME = 'Singh' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER JASPREETSINGH;
GRANT ROLE FR_FINANCE_IT TO USER JASPREETSINGH;

CREATE USER RAYADURGAKHIL PASSWORD='JG_Akhil2025' DISPLAY_NAME = 'Rayadurg Akhil' FIRST_NAME = 'Rayadurg' LAST_NAME = 'Akhil' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER RAYADURGAKHIL;
GRANT ROLE FR_FINANCE_IT TO USER RAYADURGAKHIL;

CREATE USER SUMANDEEPPODDER PASSWORD='JG_Podder2025' DISPLAY_NAME = 'Sumandeep Podder' FIRST_NAME = 'Sumandeep' LAST_NAME = 'Podder' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER SUMANDEEPPODDER;
GRANT ROLE FR_FINANCE_IT TO USER SUMANDEEPPODDER;

GRANT ROLE FR_FINANCE_IT TO USER SUJAYSAVANDV;
GRANT ROLE FR_FINANCE_IT TO USER RAHULDESHPANDE;
GRANT ROLE FR_FINANCE_IT TO USER SAUMYAMISHRA;

GRANT ROLE DR_COMMOD_UAT_DB_READ_WRITE TO USER SVC_BEACON ;


USE ROLE SECURITYADMIN ;

CREATE ROLE FR_ACCOUNT_USAGE;
 
GRANT ROLE FR_ACCOUNT_USAGE to role SECURITYADMIN;
 
GRANT DATABASE ROLE SNOWFLAKE.OBJECT_VIEWER TO ROLE FR_ACCOUNT_USAGE;
GRANT DATABASE ROLE SNOWFLAKE.USAGE_VIEWER TO ROLE FR_ACCOUNT_USAGE;
GRANT DATABASE ROLE SNOWFLAKE.GOVERNANCE_VIEWER TO ROLE FR_ACCOUNT_USAGE;
GRANT DATABASE ROLE SNOWFLAKE.SECURITY_VIEWER TO ROLE FR_ACCOUNT_USAGE;
 
GRANT ROLE FR_ACCOUNT_USAGE TO USER PULKITVORA;

DROP USER RAYADURGAKHIL ;

CREATE USER KARANVIRSHOKER  PASSWORD='JG_Shoker2025' DISPLAY_NAME = 'Karanvir Shoker' FIRST_NAME = 'Karanvir' LAST_NAME = 'Shoker' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER KARANVIRSHOKER ;


GRANT OWNERSHIP ON  SCHEMA COMMOD_UAT.PUBLIC TO ROLE DR_COMMOD_UAT_OWNER ;
GRANT USAGE     ON  SCHEMA COMMOD_UAT.PUBLIC TO ROLE DR_COMMOD_UAT_DB_READ_WRITE ;

GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA COMMOD_UAT.PUBLIC TO ROLE DR_COMMOD_UAT_DB_READ_WRITE;
GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE VIEWS  IN SCHEMA COMMOD_UAT.PUBLIC TO ROLE DR_COMMOD_UAT_DB_READ_WRITE;

GRANT ROLE DR_COMMOD_UAT_DB_READ_WRITE TO ROLE FR_PRICING_SVC;

GRANT ROLE FR_COMMOD_TECH TO USER SVC_CTRM;
GRANT ROLE DR_ICE_READER TO ROLE FR_COMMOD_TECH

   /********  *********/
CREATE WAREHOUSE IF NOT EXISTS EU_COMMOD_RESEARCH_XS_WH
    WAREHOUSE_SIZE = 'X-SMALL'
    AUTO_SUSPEND = 60
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

CREATE WAREHOUSE IF NOT EXISTS EU_COMMOD_RESEARCH_S_WH
    WAREHOUSE_SIZE = 'SMALL'
    AUTO_SUSPEND = 60
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

CREATE WAREHOUSE IF NOT EXISTS EU_COMMOD_RESEARCH_M_WH
    WAREHOUSE_SIZE = 'MEDIUM'
    AUTO_SUSPEND = 60
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE EU_COMMOD_RESEARCH_XS_WH TO ROLE FR_COMMOD_PM_EU;
GRANT USAGE ON WAREHOUSE EU_COMMOD_RESEARCH_S_WH TO ROLE FR_COMMOD_PM_EU;
GRANT USAGE ON WAREHOUSE EU_COMMOD_RESEARCH_M_WH TO ROLE FR_COMMOD_PM_EU;

CREATE ROLE IF NOT EXISTS DR_ICIS_HEREN_READER;

GRANT ROLE DR_ICIS_HEREN_READER TO USER STEPHENALLEN;
GRANT ROLE DR_ICIS_HEREN_READER TO USER RAJIVGUPTA;
GRANT ROLE DR_ICIS_HEREN_READER TO USER PULKITVORA;

GRANT USAGE ON WAREHOUSE EU_COMMOD_RESEARCH_XS_WH TO ROLE DR_ICIS_HEREN_READER;

GRANT USAGE ON DATABASE BLOOMBERG TO ROLE DR_ICIS_HEREN_READER;
GRANT USAGE ON SCHEMA BLOOMBERG.BBGH_ONDEMAND TO ROLE DR_ICIS_HEREN_READER;

GRANT ROLE DR_EXT_FACTSET_UAT_READER TO ROLE REBAL_READONLY; 

GRANT ROLE DR_SECURITY_MASTER_UAT_DB_READ_ONLY TO ROLE REBAL_READONLY; 

GRANT ROLE DR_MSCI_READER TO ROLE FR_FETECH;
GRANT ROLE FR_FETECH TO USER PULKITVORA;

GRANT ROLE REBAL_SHARED TO ROLE FR_DATA_PLATFORM;

GRANT ROLE OLIVIALPERIN TO ROLE FR_EQDELTAONE;


GRANT ROLE DR_BBGH_ETF_OPTIONS TO ROLE FR_GGRIGOROV;
GRANT ROLE DR_BBGH_CUSTOM TO ROLE FR_GGRIGOROV;

GRANT ROLE DR_ICE_SECURITY_MASTER_DB_READ_ONLY TO ROLE FR_RISK;

CREATE ROLE IF NOT EXISTS FR_YVESMINDREN;
GRANT ROLE DR_SPG_PLATTS_READER TO ROLE FR_YVESMINDREN;
GRANT ROLE JG_GENERAL TO ROLE FR_YVESMINDREN;

CREATE USER YVESMINDREN PASSWORD='JG_Mindren2025' DISPLAY_NAME = 'Yves Mindren' FIRST_NAME = 'Yves' LAST_NAME = 'Mindren' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_YVESMINDREN TO USER YVESMINDREN;

CREATE USER ABDERRAHMANNEMAHOUDI PASSWORD='JG_Mahoudi2025' DISPLAY_NAME = 'Abderrahmane Mahoudi' FIRST_NAME = 'Abderrahmane' LAST_NAME = 'Mahoudi' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE FR_YVESMINDREN TO USER ABDERRAHMANNEMAHOUDI;

CREATE WAREHOUSE IF NOT EXISTS YVESMINDREN_XS_WH
    WAREHOUSE_SIZE = 'X-SMALL'
    AUTO_SUSPEND = 60
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE YVESMINDREN_XS_WH TO ROLE FR_YVESMINDREN;

CREATE USER HARRYCHEN PASSWORD='JG_Harry2025' DISPLAY_NAME = 'Harry Chen' FIRST_NAME = 'Harry' LAST_NAME = 'Chen' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER HARRYCHEN;

CREATE USER RYANGENTILCORE PASSWORD='JG_Ryan2025' DISPLAY_NAME = 'Ryan Gentilcore' FIRST_NAME = 'Ryan' LAST_NAME = 'Gentilcore' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER RYANGENTILCORE;

CREATE USER ADAMHEALY PASSWORD='JG_Adam2025' DISPLAY_NAME = 'Adam Healy' FIRST_NAME = 'Adam' LAST_NAME = 'Healy' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER ADAMHEALY;

CREATE USER NICHOLASSZCZEPURA PASSWORD='JG_Nick2025' DISPLAY_NAME = 'Nicholas Szczepura' FIRST_NAME = 'Nicholas' LAST_NAME = 'Szczepura' EMAIL = '<EMAIL>' MUST_CHANGE_PASSWORD = TRUE;
GRANT ROLE JG_GENERAL TO USER NICHOLASSZCZEPURA;
GRANT ROLE FR_EQDELTAONE TO USER NICHOLASSZCZEPURA;
GRANT ROLE FR_EQVOL TO USER NICHOLASSZCZEPURA;

GRANT ROLE DR_BBGH_CUSTOM TO ROLE FR_APAC_RISK;
GRANT ROLE DR_BBGH_ETF_OPTIONS TO ROLE FR_APAC_RISK;
GRANT ROLE DR_BBGH_FUTURES_BASIC TO ROLE FR_APAC_RISK;
GRANT ROLE DR_BBG_DLPLUS_READER TO ROLE FR_APAC_RISK;

CREATE ROLE IF NOT EXISTS FR_COMMOD_OPS;
GRANT ROLE FR_COMMOD_OPS TO USER RYANGENTILCORE;
GRANT ROLE FR_COMMOD_OPS TO USER ADAMHEALY;
GRANT ROLE FR_COMMOD_OPS TO USER RAJIVGUPTA;
GRANT ROLE FR_COMMOD_OPS TO USER SAMUELLASKER;
GRANT ROLE FR_COMMOD_OPS TO USER PULKITVORA;

GRANT ROLE DR_BBGH_FUTURES_BASIC TO ROLE FR_COMMOD_OPS;
GRANT ROLE DR_ICE_READER  TO ROLE FR_COMMOD_OPS;
GRANT ROLE DR_ICE_SECURITY_MASTER_DB_READ_ONLY TO ROLE FR_COMMOD_OPS;
GRANT ROLE DR_CME_SECURITY_MASTER_DB_READ_ONLY TO ROLE FR_COMMOD_OPS;

GRANT ROLE DR_ICIS_HEREN_READER TO USER SVC_EU_COMMOD_RESEARCH;
REVOKE ROLE DR_ICIS_HEREN_READER FROM USER PULKITVORA;
REVOKE ROLE DR_ICIS_HEREN_READER FROM USER RAJIVGUPTA;

CREATE ROLE IF NOT EXISTS FR_DATA_PLATFORM_LEADS;
GRANT ROLE FR_DATA_PLATFORM_LEADS TO USER TRISTANFABER;
GRANT ROLE FR_DATA_PLATFORM_LEADS TO USER PULKITVORA;
GRANT ROLE FR_DATA_PLATFORM_LEADS TO USER NEALACHORD;
GRANT ROLE FR_DATA_PLATFORM_LEADS TO USER FAHADSHEIKH;
GRANT ROLE FR_DATA_PLATFORM_LEADS TO USER MAHENDRACHANDRASEKHAR;
GRANT ROLE FR_DATA_PLATFORM_LEADS TO USER NITINTHAKRAL;

GRANT ROLE FR_DATA_PLATFORM TO ROLE FR_DATA_PLATFORM_LEADS;

CREATE ROLE FR_CORE_SERVICES_DB_ADMINS;
GRANT ROLE FR_CORE_SERVICES_DB_ADMINS TO USER RONBENCHETRIT;
