-- DB & role creation
CREATE DATABASE POSITIONS_UAT;
CREATE ROLE DR_POSITIONS_UAT_OWNER;
GRANT OWNERSHIP ON DATABASE POSITIONS_UAT TO ROLE DR_POSITIONS_UAT_OWNER;

-- <PERSON><PERSON><PERSON> WHEN GRANTING THE READ_ONLY ROLE: This DB contains firm-wide protected data,
-- so users from individual business units MUST NOT be granted access to the whole DB, 
-- only to views that filters the rows down to their business unit.
CREATE ROLE DR_POSITIONS_UAT_DB_READ_ONLY;
CREATE ROLE DR_POSITIONS_UAT_DB_READ_WRITE;
GRANT ROLE DR_POSITIONS_UAT_DB_READ_ONLY TO ROLE DR_POSITIONS_UAT_DB_READ_WRITE;
GRANT ROLE DR_POSITIONS_UAT_DB_READ_WRITE TO ROLE DR_POSITIONS_UAT_OWNER;

GRANT ALL ON DATABASE POSITIONS_UAT TO ROLE DR_POSITIONS_UAT_OWNER;
GRANT USAGE ON DATABASE POSITIONS_UAT TO DR_POSITIONS_UAT_DB_READ_ONLY;


-- Schema & role creation
CREATE SCHEMA POSITIONS_UAT.POC;

GRANT OWNERSHIP ON SCHEMA POSITIONS_UAT.POC TO ROLE DR_POSITIONS_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA POSITIONS_UAT.POC TO DR_POSITIONS_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA POSITIONS_UAT.POC TO DR_POSITIONS_UAT_OWNER;
GRANT ALL ON SCHEMA POSITIONS_UAT.POC TO DR_POSITIONS_UAT_OWNER;
GRANT USAGE ON SCHEMA POSITIONS_UAT.POC TO DR_POSITIONS_UAT_DB_READ_ONLY;

CREATE SCHEMA POSITIONS_UAT.TRADING;

GRANT OWNERSHIP ON SCHEMA POSITIONS_UAT.TRADING TO ROLE DR_POSITIONS_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA POSITIONS_UAT.TRADING TO DR_POSITIONS_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA POSITIONS_UAT.TRADING TO DR_POSITIONS_UAT_OWNER;
GRANT ALL ON SCHEMA POSITIONS_UAT.TRADING TO DR_POSITIONS_UAT_OWNER;
GRANT USAGE ON SCHEMA POSITIONS_UAT.TRADING TO DR_POSITIONS_UAT_DB_READ_ONLY;

-- Manage Functional Role privileges here
GRANT ROLE DR_POSITIONS_UAT_OWNER TO ROLE FR_DATA_PLATFORM;
-- TODO: WE can't have DR roles given to users directly.
GRANT ROLE DR_POSITIONS_UAT_OWNER TO USER CHARLESPEHLIVANIAN;
