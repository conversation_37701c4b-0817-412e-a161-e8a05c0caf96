from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta, date
import os, sys, pendulum, subprocess, logging
from airflow.providers.http.operators.http import HttpOperator
from util.etl import check_for_anomalies

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()
import stcommon.tools.dates as dates
from airflow.timetables.trigger import CronTriggerTimetable

JGDATA_PATH = os.environ.get("JGDATA_PATH")

def run_ice_secmaster():
    command = (
        f"python3 {JGDATA_PATH}/bin/ice.secmaster.gl.py"
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise


default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 12, 5, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id="jg-etl-ice-secmaster",
    description="This DAG runs ETL for ICE Security Master",
    default_args=default_args,
    # no need to change time post day light saving
    schedule = CronTriggerTimetable('45 2 * * 2-6', timezone="UTC"),
    tags=["jgdata","ICE"],
    catchup=False
)

etl_job = PythonOperator(
    task_id="jg-etl-ice-secmaster",
    python_callable=run_ice_secmaster,
    dag=dag
)

validation_job = HttpOperator(
    task_id="call-daily-data-validation-api",
    http_conn_id="vendor_http_raw", 
    endpoint="get_daily_data_validation_rule_based/?dataset_id=ICESecmaster",
    method="GET",
    headers={"Content-Type": "application/json"},
    response_check=check_for_anomalies,
    extra_options={"check_response": True},
    log_response=True,
    dag=dag
)

etl_job >> validation_job
