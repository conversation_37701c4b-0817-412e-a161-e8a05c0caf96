import requests
import time
import json
import pandas as pd
import polars as pl
from datetime import datetime
import pytz
import os
import psycopg2
from psycopg2.extras import execute_values
import logging
from enum import Enum
from io import StringIO
from utils.date_utils import get_now
from utils.postgres.adaptor import PostgresAdaptor

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SpiderRockMessageType(Enum):
    OPTION_NBBO_QUOTE = "OptionNbboQuote"
    TICKER_DEFINITION = "TickerDefinition"


class SpiderRockClient:
    def __init__(self, config: dict, message_type: SpiderRockMessageType):
        self.config = config
        self.url = self.config["api_url"]
        self.message_type = message_type.value         

    def get_nested_value(self, data, nested_key):
        keys = nested_key.split(".")
        for key in keys:
            if isinstance(data, dict):
                data = data.get(key)
            else:
                return None
        return data

    def invoke_api(self, tickers: list, api_req_time: datetime):
        if self.message_type not in self.config["message_types"]:
            raise ValueError("Invalid message type")

        message_config = self.config["message_types"][self.message_type]

        params = message_config["params"].copy()
        field_mapping = message_config["field_mapping"]
        unique_key_path = message_config["unique_key_path"]
        dict_columns = message_config["columns"]
        
        where_conditions = [message_config["where_template"].format(ticker=t) for t in tickers]
        params["where"] = " | ".join(where_conditions)

        response = requests.get(self.url, params=params)
        api_recvd_time = get_now("UTC")

        if response.status_code != 200:
            logger.error("Response content:", response.text)
            raise ValueError(f"Request failed with status code: {response.status_code}")

        all_rows = []
        for msg in response.json():
            dict_message = msg.get("message", {})
            if "pkey" not in dict_message:
                continue

            okey_data = self.get_nested_value(dict_message, unique_key_path)
            if not okey_data:
                continue

            row = {sf_col: self.get_nested_value(msg, kafka_key.replace("_", ".")) for sf_col, kafka_key in field_mapping.items()}
            row["SR_SRCTIMESTAMP"] = datetime.fromtimestamp(row["SR_SRCTIMESTAMP"] / 1_000_000_000, pytz.utc) if row["SR_SRCTIMESTAMP"] is not None else None
            row["SR_NETTIMESTAMP"] = datetime.fromtimestamp(row["SR_NETTIMESTAMP"] / 1_000_000_000, pytz.utc) if row["SR_NETTIMESTAMP"] is not None else None
            all_rows.append(row)

        df_all_rows = pd.DataFrame(all_rows)
        df_all_rows["JG_API_RECVD_TIMESTAMP"] = api_recvd_time
        df_all_rows["JG_API_REQ_TIMESTAMP"] = api_req_time
        df_all_rows["CALL_PUT"] = df_all_rows["CALL_PUT"].map({"Call": "C", "Put": "P"})
        df_all_rows["STRIKE_PRICE"] = df_all_rows["STRIKE_PRICE"] * 1000
        df_all_rows["STRIKE_PRICE"] = df_all_rows["STRIKE_PRICE"].astype("int32")
        
        df_all_rows["BID_SIZE"] = df_all_rows["BID_SIZE"].astype("Int64")
        df_all_rows["CUM_BID_SIZE"] = df_all_rows["CUM_BID_SIZE"].astype("Int64")
        df_all_rows["ASK_SIZE"] = df_all_rows["ASK_SIZE"].astype("Int64")
        df_all_rows["CUM_ASK_SIZE"] = df_all_rows["CUM_ASK_SIZE"].astype("Int64")

        df_all_rows["BID_TIME"] = df_all_rows["BID_TIME"].astype("Int64")
        df_all_rows["ASK_TIME"] = df_all_rows["ASK_TIME"].astype("Int64")
        
        df_all_rows = df_all_rows[dict_columns.keys()]
        df_all_rows.rename(columns=dict_columns, inplace=True)

        return df_all_rows
        
    def load_csv_to_db(self, df, table_full_path):
        pg_host = self.config["pg_host"]
        pg_username = os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"]
        pg_password = os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"]
        dbname, schema, table = table_full_path.split(".")

        start_time = time.time()

        buffer = StringIO()
        df.to_csv(buffer, index=False, header=True)
        end_time = time.time()
        logger.info(f"Time taken for buffer stream: {end_time - start_time:.4f} secs")
        
        start_time = time.time()
        buffer.seek(0)
        column_list = ", ".join(list(df.columns))
        conn_string = f"host={pg_host} dbname={dbname} user={pg_username} password={pg_password} port=5432"
        with psycopg2.connect(conn_string) as conn:
            with conn.cursor() as cursor:
                cursor.execute("SET work_mem TO '1GB'")
                copy_sql = f"""COPY eqvol.sr_option_quotes_exp ({column_list}) FROM STDIN WITH (FORMAT CSV, HEADER)"""
                cursor.copy_expert(copy_sql, buffer)

            conn.commit()
        end_time = time.time()
        logger.info(f"Time taken for saving sr_option_quotes_exp: {end_time - start_time:.4f} secs")
        
        start_time = time.time()
        buffer.seek(0)
        column_list = ", ".join(list(df.columns))
        conn_string = f"host={pg_host} dbname={dbname} user={pg_username} password={pg_password} port=5432"
        with psycopg2.connect(conn_string) as conn:
            with conn.cursor() as cursor:
                cursor.execute("SET work_mem TO '1GB'")
                cursor.execute("TRUNCATE TABLE eqvol.sr_option_quotes_exp_latest")
                copy_sql = f"""COPY eqvol.sr_option_quotes_exp_latest ({column_list}) FROM STDIN WITH (FORMAT CSV, HEADER)"""
                cursor.copy_expert(copy_sql, buffer)

            conn.commit()
        end_time = time.time()
        logger.info(f"Time taken for saving sr_option_quotes_latest: {end_time - start_time:.4f} secs")

        opts_api_req_time = df["jg_api_req_timestamp"].iloc[0]

        df_opt_time_stamps = pd.DataFrame({"snap_timestamp": [opts_api_req_time], "snap_date": [opts_api_req_time.date()], "is_cold_snap": [False]})

        loader = PostgresAdaptor(
            host=self.config["pg_host"],
            database="fe_risk",
            schema="eqvol",
            user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
            password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
        )

        success = loader.load_dataframe(
        df=df_opt_time_stamps,
        table_name="sr_option_quote_exp_timestamps",
        if_exists="append",
        )

        if success:
            logger.info(f"Successfully saved option quote timestamp.")
        else:
            raise ValueError("Saving Option Quote timestamp failed!")
        
        opts_api_req_time_eastern = (opts_api_req_time.tz_localize('UTC').astimezone(pytz.timezone('US/Eastern')).replace(tzinfo=None))

        if is_time_in_range(opts_api_req_time_eastern, (9, 15), (9, 29)):
            loader.execute_query_no_ret("TRUNCATE TABLE eqvol.sr_option_quotes_exp_complete;")
            logger.info(f"Successfully truncated table eqvol.sr_option_quotes_exp_complete")
        
        
        if is_time_in_range(opts_api_req_time_eastern, (9, 30), (15, 59)):
            start_time = time.time()
            loader.execute_query_no_ret("CALL eqvol.update_option_quotes_exp_complete();")
            end_time = time.time()
            logger.info(f"Time taken for updating sr_option_quotes_exp_complete: {end_time - start_time:.4f} secs")
        else:
            logger.info(f"Skipped updating sr_option_quotes_exp_complete..")
    

def is_time_in_range(timestamp: datetime, start: tuple[int, int], end: tuple[int, int]) -> bool:
    # Extract hour and minute from tuples
    start_hour, start_minute = start
    end_hour, end_minute = end
    
    timestamp = timestamp.replace(second=0, microsecond=0)

    # Create start and end time objects for the same date
    start_time = timestamp.replace(hour=start_hour, minute=start_minute, second=0, microsecond=0)
    end_time = timestamp.replace(hour=end_hour, minute=end_minute, second=0, microsecond=0)
    
    # Handle case where end time is on the next day (e.g., 23:30 to 1:00)
    if end_time < start_time:
        raise ValueError("Invalid time range: start time is after end time")
    
    # Check if current time is within the range (inclusive)
    return start_time <= timestamp <= end_time

if __name__ == "__main__":
    if is_time_in_range(datetime.now(pytz.timezone('US/Eastern')).replace(tzinfo=None), (22, 16), (22, 40)):
        logger.info("Current time is within market opening window")
    else:
        logger.info("Current time is outside market opening window")