-- DB & role creation
CREATE DATABASE SECURITY_MASTER_UAT;

CREATE ROLE DR_SECURITY_MASTER_UAT_OWNER;
CREATE ROLE DR_SECURITY_MASTER_UAT_DB_READ_WRITE;
CREATE ROLE DR_SECURITY_MASTER_UAT_DB_READ_ONLY;
GRANT ROLE DR_SECURITY_MASTER_UAT_DB_READ_ONLY TO ROLE DR_SECURITY_MASTER_UAT_DB_READ_WRITE;
GRANT ROLE DR_SECURITY_MASTER_UAT_DB_READ_WRITE TO ROLE DR_SECURITY_MASTER_UAT_OWNER;

GRANT OWNERSHIP ON DATABASE SECURITY_MASTER_UAT TO ROLE DR_SECURITY_MASTER_UAT_OWNER;
GRANT ALL ON DATABASE SECURITY_MASTER_UAT TO ROLE DR_SECURITY_MASTER_UAT_OWNER;
GRANT USAGE ON DATABASE SECURITY_MASTER_UAT TO DR_SECURITY_MASTER_UAT_DB_READ_ONLY;
GRANT SELECT ON ALL TABLES IN DATABASE SECURITY_MASTER_UAT TO DR_SECURITY_MASTER_UAT_DB_READ_ONLY;
GRANT SELECT ON ALL VIEWS  IN DATABASE SECURITY_MASTER_UAT TO DR_SECURITY_MASTER_UAT_DB_READ_ONLY;


-- Schema & role creation
CREATE SCHEMA SECURITY_MASTER_UAT.EQUITIES;
GRANT OWNERSHIP ON SCHEMA SECURITY_MASTER_UAT.EQUITIES TO ROLE DR_SECURITY_MASTER_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA SECURITY_MASTER_UAT.EQUITIES TO DR_SECURITY_MASTER_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA SECURITY_MASTER_UAT.EQUITIES TO DR_SECURITY_MASTER_UAT_OWNER;
GRANT ALL ON SCHEMA SECURITY_MASTER_UAT.EQUITIES TO DR_SECURITY_MASTER_UAT_OWNER;
GRANT USAGE ON SCHEMA SECURITY_MASTER_UAT.EQUITIES TO DR_SECURITY_MASTER_UAT_DB_READ_ONLY;

CREATE SCHEMA SECURITY_MASTER_UAT.FLEX;
GRANT OWNERSHIP ON SCHEMA SECURITY_MASTER_UAT.FLEX TO ROLE DR_SECURITY_MASTER_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA SECURITY_MASTER_UAT.FLEX TO DR_SECURITY_MASTER_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA SECURITY_MASTER_UAT.FLEX TO DR_SECURITY_MASTER_UAT_OWNER;
GRANT ALL ON SCHEMA SECURITY_MASTER_UAT.FLEX TO DR_SECURITY_MASTER_UAT_OWNER;
GRANT USAGE ON SCHEMA SECURITY_MASTER_UAT.FLEX TO DR_SECURITY_MASTER_UAT_DB_READ_ONLY;

CREATE SCHEMA SECURITY_MASTER_UAT.GSC_ENRICHMENT;
GRANT OWNERSHIP ON SCHEMA SECURITY_MASTER_UAT.GSC_ENRICHMENT TO ROLE DR_SECURITY_MASTER_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA SECURITY_MASTER_UAT.GSC_ENRICHMENT TO DR_SECURITY_MASTER_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA SECURITY_MASTER_UAT.GSC_ENRICHMENT TO DR_SECURITY_MASTER_UAT_OWNER;
GRANT ALL ON SCHEMA SECURITY_MASTER_UAT.GSC_ENRICHMENT TO DR_SECURITY_MASTER_UAT_OWNER;
GRANT USAGE ON SCHEMA SECURITY_MASTER_UAT.GSC_ENRICHMENT TO DR_SECURITY_MASTER_UAT_DB_READ_ONLY;


-- Manage Functional Role privileges here
CREATE ROLE FR_SECURITY_MASTER_NONPROD_USER;

GRANT ROLE DR_SECURITY_MASTER_UAT_OWNER TO ROLE FR_DATA_PLATFORM;

-- Ensure DR_SECURITY_MASTER_OWNER can read from ST_CHECK schema in UAT DB until it also exists in PROD
GRANT ROLE DR_SECURITY_MASTER_UAT_ST_CHECK_DB_READ_ONLY TO ROLE DR_SECURITY_MASTER_OWNER;
GRANT SELECT ON VIEW SECURITY_MASTER_UAT.ST_CHECK.ST_LISTINGS_CLEAN TO ROLE DR_SECURITY_MASTER_UAT_ST_CHECK_DB_READ_ONLY;

GRANT ROLE DR_SECURITY_MASTER_UAT_DB_READ_ONLY TO ROLE FR_SECURITY_MASTER_NONPROD_USER;
GRANT ROLE GS_DATA_ANALYST_UAT TO ROLE FR_SECURITY_MASTER_NONPROD_USER;

GRANT ROLE FR_SECURITY_MASTER_NONPROD_USER TO ROLE FR_DATA_PLATFORM;
GRANT ROLE FR_SECURITY_MASTER_NONPROD_USER TO ROLE FR_EXECUTION_TECH_NONPROD;
GRANT ROLE FR_SECURITY_MASTER_NONPROD_USER TO ROLE FR_TRADING_NONPROD;
GRANT ROLE FR_SECURITY_MASTER_NONPROD_USER TO ROLE FR_FETECH;
GRANT ROLE FR_SECURITY_MASTER_NONPROD_USER TO ROLE FR_COMPLIANCE_IT_NONPROD;
GRANT ROLE FR_SECURITY_MASTER_NONPROD_USER TO ROLE FR_JG_INTERCONNECT_NONPROD;