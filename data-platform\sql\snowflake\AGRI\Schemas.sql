CREATE DATABASE JG_AGRI;
CREATE SCHEMA COMMOD.AGRI_USDA;
CREATE SCHEMA JG_AGRI.BRIDGETON;
CREATE SCHEMA JG_AGRI.QUICKSTATS;
CREATE SCHEMA JG_AGRI.SNP;
CREATE SCHEMA JG_AGRI.SUGAR;

CREATE ROLE DR_JG_AGRI_DB_READ_WRITE;
CREATE ROLE DR_JG_AGRI_DB_READ_ONLY;
CREATE ROLE DR_JG_AGRI_OWNER;

GRANT ROLE DR_JG_AGRI_OWNER TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_JG_AGRI_DB_READ_WRITE TO ROLE FR_DATA_PLATFORM;
GRANT ALL ON DATABASE USDA TO ROLE DR_JG_AGRI_OWNER;

--Grant all gives create schema--
GRANT USAGE ON SCHEMA COMMOD.<PERSON><PERSON>_<PERSON>A TO DR_JG_AGRI_OWNER;
GRANT USAGE ON SCHEMA COMMOD.AGRI_USDA TO DR_JG_AGRI_DB_READ_ONLY;
GRANT USAGE ON SCHEMA COMMOD.AG<PERSON>_USDA TO DR_JG_AGRI_DB_READ_WRITE;

GRANT USAGE ON SCHEMA JG_AGRI.BRIDGETON TO DR_JG_AGRI_OWNER;
GRANT USAGE ON SCHEMA JG_AGRI.BRIDGETON TO DR_JG_AGRI_DB_READ_ONLY;
GRANT USAGE ON SCHEMA JG_AGRI.BRIDGETON TO DR_JG_AGRI_DB_READ_WRITE;

GRANT USAGE ON SCHEMA JG_AGRI.QUICKSTATS TO DR_JG_AGRI_OWNER;
GRANT USAGE ON SCHEMA JG_AGRI.QUICKSTATS TO DR_JG_AGRI_DB_READ_ONLY;
GRANT USAGE ON SCHEMA JG_AGRI.QUICKSTATS TO DR_JG_AGRI_DB_READ_WRITE;

GRANT USAGE ON SCHEMA JG_AGRI.SNP TO DR_JG_AGRI_OWNER;
GRANT USAGE ON SCHEMA JG_AGRI.SNP TO DR_JG_AGRI_DB_READ_ONLY;
GRANT USAGE ON SCHEMA JG_AGRI.SNP TO DR_JG_AGRI_DB_READ_WRITE;

GRANT USAGE ON SCHEMA JG_AGRI.SUGAR TO DR_JG_AGRI_OWNER;
GRANT USAGE ON SCHEMA JG_AGRI.SUGAR TO DR_JG_AGRI_DB_READ_ONLY;
GRANT USAGE ON SCHEMA JG_AGRI.SUGAR TO DR_JG_AGRI_DB_READ_WRITE;

--change all to ownership
GRANT OWNERSHIP ON ALL TABLES IN DATABASE JG_AGRI TO DR_JG_AGRI_OWNER;
GRANT ALL PRIVILEGES on schema COMMOD.AGRI_USDA to role DR_JG_AGRI_OWNER;
GRANT ALL PRIVILEGES on schema JG_AGRI.BRIDGETON to role DR_JG_AGRI_OWNER;
GRANT ALL PRIVILEGES on schema JG_AGRI.QUICKSTATS to role DR_JG_AGRI_OWNER;
GRANT ALL PRIVILEGES on schema JG_AGRI.SNP to role DR_JG_AGRI_OWNER;
GRANT ALL PRIVILEGES on schema JG_AGRI.SUGAR  to role DR_JG_AGRI_OWNER;


GRANT SELECT ON ALL VIEWS IN DATABASE JG_AGRI TO ROLE FR_COMMOD_JG_AGRI_PM;
GRANT SELECT ON FUTURE VIEWS  IN DATABASE JG_AGRI TO DR_JG_AGRI_DB_READ_ONLY;
GRANT SELECT ON ALL VIEWS  IN DATABASE JG_AGRI TO DR_JG_AGRI_DB_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN DATABASE JG_AGRI TO DR_JG_AGRI_DB_READ_ONLY;

