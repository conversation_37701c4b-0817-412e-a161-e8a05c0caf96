# SubscriptionWithEventHandlerExample.py

import blpapi
import blpapi.logging
from optparse import OptionParser
import time
import logging
import toml
import snowflake.connector
import os, sys
from confluent_kafka import Producer, KafkaException
from typing import Optional
import json
import pandas as pd

from strunner import *
setupEnvironment()

import jglib.paths as pth
from stcommon.infra.realtimetick.tick_writer import TickFileWriter
import datetime
import os
from stcommon.infra.rds.kafka_operation import KafkaPublisher
import socket
import sys

HOSTNAME = str(socket.gethostname())
project_root_temp= os.environ.get('JGDATA_PATH')
TOML_CONFIG_FILE = project_root_temp + "/conf/sources/bar_creator/bar_config_prod.toml"
instance_id = ""

with open(TOML_CONFIG_FILE, 'r') as file:
    toml_config_handler = toml.load(file)

config_head_obj = toml_config_handler.get("config_heads", {}).get('bpipe', {})
bpipe_tick_dir = config_head_obj.get("tick_dir")
BPIPE_RAW_PATH = os.path.join(bpipe_tick_dir, f"bpipe_streaming_{HOSTNAME}_PROD")

EXCEPTIONS = blpapi.Name("exceptions")
FIELD_ID = blpapi.Name("fieldId")
REASON = blpapi.Name("reason")
CATEGORY = blpapi.Name("category")
DESCRIPTION = blpapi.Name("description")

SessionConnectionDown = blpapi.Name("SessionConnectionDown")
SessionConnectionUp = blpapi.Name("SessionConnectionUp")
SessionTerminated = blpapi.Name("SessionTerminated")
ServiceDown = blpapi.Name("ServiceDown")
SlowConsumerWarning = blpapi.Name("SlowConsumerWarning")
SlowConsumerWarningCleared = blpapi.Name("SlowConsumerWarningCleared")
DataLoss = blpapi.Name("DataLoss")

ServiceName = blpapi.Name("serviceName")
# authorization 
AUTHORIZATION_SUCCESS = blpapi.Name("AuthorizationSuccess")
AUTHORIZATION_FAILURE = blpapi.Name("AuthorizationFailure")
AUTHORIZATION_REVOKED = blpapi.Name("AuthorizationRevoked")
TOKEN_SUCCESS = blpapi.Name("TokenGenerationSuccess")
TOKEN_FAILURE = blpapi.Name("TokenGenerationFailure")
TOKEN = blpapi.Name("token")


g_session = None
g_sessionStarted = False
g_subscriptions = None
g_identity = None
g_authCorrelationId = None
sf_conn = None
sf = None

kafka_conf = {}
objconfig = {}
logger = logging.getLogger()
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

class SF_Helper:
    def __init__(self):
        global sf_conn
        sf_conn = snowflake.connector.connect(
            user = objconfig['sf_user'],
            password = objconfig['sf_password'],
            account = objconfig['sf_account'],
            warehouse = objconfig['sf_bloomberg_warehouse'],
            database = objconfig['sf_bloomberg_database'],
            schema = objconfig['sf_bloomberg_schema'],
            role = objconfig['sf_bloomberg_owner']
        )
    
    def run_sf_query(self,conn,query):
        try:
            logging.getLogger('snowflake.connector').setLevel(logging.WARNING)
            df = pd.read_sql(query, conn)
            return df
        except Exception as e:
            logger.error(f"Error querying Snowflake: {e}")
            return None

    def run_dml(self, conn, query):
        try:
            logging.getLogger('snowflake.connector').setLevel(logging.WARNING)
            cur = conn.cursor()
            cur.execute(query)
            cur.close()
            return True
        except Exception as e:
            logger.error(f"Error querying Snowflake: {e}")
            return False

class JGConfig:
    def jg_config_path(self):
        configPath = os.environ.get('CONFIG_PATH',os.getcwd())
        with open(f'{configPath}/config.json', 'r') as f:
            config = json.load(f)
        return config["JG_CONFIG_PATH"]

    def read_config_secrets(self):
        config_secret_path = os.path.join(self.jg_config_path(), 'config_secret.json')
        logger.debug("config_secret_path: "+config_secret_path)
        with open(config_secret_path, 'r') as f:
            config_secret = json.load(f)
        return config_secret

class CorrelationInfo:
    def __init__(self, topic):
        self.topic = topic

    def getTopic(self):
        return self.topic


class SubscriptionEventHandler(object):
    """ TICK-BAR Coded added: Adding a constructor to init the bpipe output path"""
    def __init__(self, output_dir=BPIPE_RAW_PATH):
        
        with open(TOML_CONFIG_FILE, 'r') as file:
            self.toml_config = toml.load(file)
        
        self.config_head = self.toml_config.get("config_heads", {}).get('bpipe', {})
        self.bar_size = self.config_head.get("bar_size")
        # self.tick_file_prefix = self.config_head.get("tick_file_prefix")
        
        self.tick_file_prefix = f"{self.config_head.get('tick_file_prefix')}_{HOSTNAME}{'_' + instance_id if instance_id else ''}"
        # self.tick_file_prefix = f"{self.config_head.get('tick_file_prefix')}_{HOSTNAME}"
        
        self.file_writer = TickFileWriter(output_dir, self.tick_file_prefix, self.bar_size)

    def write_to_file(self, data):
        """
        Write data to the current file, rotating files as needed.
        """
        self.file_writer.write_to_file(data)
        
        
    def getTimeStamp(self):
        return time.strftime("%Y-%m-%d %X")

    def processSessionStatus(self, event):
        timeStamp = self.getTimeStamp()
        logger.info("Processing SESSION_STATUS")
        for msg in event:
            if msg.messageType() == SessionConnectionDown:
                # API connection disconnect (Session still active)
                logger.info("%s: Session connection down detected: %s" % (timeStamp, msg))
            elif msg.messageType() == SessionConnectionUp:
                # API connection up (Session still active)
                logger.info("%s: Session connection up detected: %s" % (timeStamp, msg))
            elif msg.messageType() == SessionTerminated:
                # Session no longer active
                logger.info("%s: Session terminated!: %s" % (timeStamp, msg))
                g_sessionStarted = False
            else:
                # misc messages
                logger.info("%s: Misc session message: %s" % (timeStamp, msg))
               
    def processServiceStatus(self, event):
        timeStamp = self.getTimeStamp()
        logger.info("Processing SESSION_STATUS")
        for msg in event:
            if msg.messageType() == ServiceDown:
                if msg.hasElement(ServiceName, True):
                    logger.info("%s: Service down detected: %s" % (timeStamp, msg))
            else:
                # misc messages
                logger.info("%s: Misc session message: %s" % (timeStamp, msg))
        
    def processSubscriptionStatus(self, event):
        timeStamp = self.getTimeStamp()
        logger.info(f"{timeStamp} Processing SUBSCRIPTION_STATUS")
        for msg in event:
            cInfo = msg.correlationIds()[0].value()
            logger.info("%s: %s" % (timeStamp, cInfo))
            logger.info(msg)

            if msg.hasElement(REASON):
                # This can occur on SubscriptionFailure.
                reason = msg.getElement(REASON)
                if msg.hasElement(CATEGORY) and msg.hasElement(DESCRIPTION):
                    logger.info("        %s: %s" % (
                        reason.getElement(CATEGORY).getValueAsString(),
                        reason.getElement(DESCRIPTION).getValueAsString()))

            if msg.hasElement(EXCEPTIONS):
                # This can occur on SubscriptionStarted if at least
                # one field is good while the rest are bad.
                exceptions = msg.getElement(EXCEPTIONS)
                for exInfo in exceptions.values():
                    fieldId = exInfo.getElement(FIELD_ID)
                    reason = exInfo.getElement(REASON)
                    logger.info("        %s: %s" % (
                        fieldId.getValueAsString(),
                        reason.getElement(CATEGORY).getValueAsString()))

    def processElementData(self, element, padding):
        global printbuf
        if element.isArray():
            # field is an array
            itemCount = element.numValues()
            for index in range(itemCount):
                self.processElementData(element.getValueAsElement(index), '')
        elif element.numElements() > 0:
            #print ("%s" % (element.name()),end='')
            printbuf=printbuf+","+element.name()
            eleCount = element.numElements()
            for eleIndex in range(eleCount):
                self.processElementData(element.getElement(eleIndex), padding + '    ')
        else:
            # Assume all values are scalar.
            #print( ",%s = %s" % (element.name(), element.getValueAsString()),end='')
            printbuf=printbuf+",'"+str(element.name())+"': '"+element.getValueAsString()+"'"
            

    def processSubscriptionDataEvent(self, event):
        timeStamp = self.getTimeStamp()
        #print ("Processing SUBSCRIPTION_DATA")
        for msg in event:
            cInfo = msg.correlationIds()[0].value()
            global printbuf
            printbuf="{'TIME': '"+timeStamp + "', 'SECURITY': '" + str(cInfo.getTopic()) + "', 'TYPE': '" + str(msg.messageType()) + "'"
            #print ("%s: %s - %s" % (timeStamp, cInfo.getTopic(), msg.messageType()),end='')
            for field in msg.asElement().elements():
                if field.numValues() < 1:
                    #print ("%s is NULL" % field.name())
                    continue
                else:
                    self.processElementData(field, '')
            printbuf=printbuf+"}"
            printbuf=printbuf.replace("'", '"')
            logger.debug(f"{self.getTimeStamp()} {printbuf}")
            self.write_to_file(printbuf)

    def processAdmin(self, event):
        timeStamp = self.getTimeStamp()
        for msg in event:
            if msg.messageType() == SlowConsumerWarning:
                logger.info("!!!! Slow consumer warning !!!! ")
            elif msg.messageType() == SlowConsumerWarningCleared:
                logger.info("!!!! Slow consumer warning cleared !!!! ")
            else:
                logger.info("%s: %s\n%s" % (timeStamp, msg.messageType(), msg))

    def processAuthEvent(self, event, session):
        timeStamp = self.getTimeStamp()

        logger.info("\nProcessing Authorization Status")

        for msg in event:
            # An authorize status event can have more than one messages.
            if msg.messageType() == AUTHORIZATION_SUCCESS:
                logger.info(timeStamp + ": " + "Authentication Success")
                # get session authorized identity
                d_identity = session.getAuthorizedIdentity()

            elif msg.messageType() == AUTHORIZATION_FAILURE:
                # authentication failed
                logger.info(timeStamp + ": " + "Authentication Failed")
                logger.info(msg)
            elif msg.messageType() == AUTHORIZATION_REVOKED:
                # Identity is revoked
                logger.info(timeStamp + ": " + "Authentication Revoked")
                logger.info(msg)
            else:
                logger.info(timeStamp + ": " +
                            msg.messageType().toString())
                logger.info(msg)
        return True

    def processMiscEvents(self, event):
        timeStamp = self.getTimeStamp()
        for msg in event:
            logger.info("%s: %s\n%s" % (timeStamp, msg.messageType(), msg))

    def processEvent(self, event, session):
        try:
            if event.eventType() == blpapi.Event.SUBSCRIPTION_DATA:
                return self.processSubscriptionDataEvent(event)
            elif event.eventType() == blpapi.Event.SUBSCRIPTION_STATUS:
                return self.processSubscriptionStatus(event)
            elif event.eventType() == blpapi.Event.SESSION_STATUS:
                return self.processSessionStatus(event)
            elif event.eventType() == blpapi.Event.SERVICE_STATUS:
                return self.processServiceStatus(event)
            elif event.eventType() == blpapi.Event.ADMIN:
                return self.processAdmin(event)
            elif event.eventType() == blpapi.Event.AUTHORIZATION_STATUS:
                return self.processAuthEvent(event, session)
            else:
                return self.processMiscEvents(event)
        except blpapi.Exception as e:
            logger.info("Library Exception !!! %s" % e.description())
        return False



def subscribe(ticker):
    # subscriptionOptions = []
    fieldList = []
    with open(TOML_CONFIG_FILE, 'r') as file:
        toml_config = toml.load(file)
        
    config_head = toml_config.get("config_heads", {}).get('bpipe', {})
    identifier_query = config_head.get("subscribe_field")
    
    for identifier in identifier_query:
        fieldList.append(identifier)

    # subscriptionOptions1 = []
    cInfo = CorrelationInfo(ticker)
    cId = blpapi.CorrelationId(cInfo)
    #TOTRY: 
    # g_subscriptions.add(cInfo.getTopic(), fieldList, subscriptionOptions1, cId)
    g_subscriptions.add(cInfo.getTopic(), fieldList, "&conflate", cId)


# callback for BLPAPI logging
def onMessage(threadId, traceLevel, dateTime, loggerName, message):
    logger.info("%s %s [%s] Thread ID = %s %s" %
          (dateTime, loggerName, traceLevel, threadId, message))


def main():
    global g_session, g_sessionStarted, g_subscriptions , g_identity, g_authCorrelationId, objconfig,kafka_conf, sf_conn, sf, BPIPE_RAW_PATH, instance_id


    parser = argparse.ArgumentParser(description='BPIPE Streaming Bar')
    parser.add_argument('--instance_id', type=str, default='', help='Instance ID to use in file names')
    args = parser.parse_args()
    instance_id = args.instance_id
    
    with open(TOML_CONFIG_FILE, 'r') as file:
        toml_config = toml.load(file)
        
    config_head = toml_config.get("config_heads", {}).get('bpipe', {})
    identifier_query = config_head.get("identifier_query")
    print(f"Identifier Query: {identifier_query}")
    
    jgconfig = JGConfig()  
    objconfig = jgconfig.read_config_secrets()
    # kafka_conf = {"bootstrap.servers": objconfig["kafka_bootstrap_url_uat"]}
    sf = SF_Helper()
    df_cusip_ref = sf.run_sf_query(sf_conn,identifier_query)                                
    # set BLPAPI log level
    blpapi.logging.Logger.registerCallback(onMessage, blpapi.logging.Logger.SEVERITY_OFF)


    # Update BPIPE_RAW_PATH with instance_id
    instance_id = args.instance_id
    BPIPE_RAW_PATH = os.path.join(bpipe_tick_dir, f"bpipe_streaming_{HOSTNAME}{'_' + instance_id if instance_id else ''}_PROD")
    logger.info(f"Using BPIPE_RAW_PATH: {BPIPE_RAW_PATH}")

    # Create directory if it doesn't exist
    os.makedirs(BPIPE_RAW_PATH, exist_ok=True)

    # Fill SessionOptions
    options = blpapi.SessionOptions()
    g_subscriptions = blpapi.SubscriptionList()


    # Note: SessionOptions.SessionName require SDK version 3.23.x or later
    options.sessionName = 'Example Session'

    # Server address setup
    options.setServerAddress(objconfig["bpipe_server"], objconfig["bpipe_server_port"], 0)
    authOptions = blpapi.AuthOptions.createWithApp(objconfig["bpipe_app"])

    g_authCorrelationId = blpapi.CorrelationId("authCorrelation");
    options.setSessionIdentityOptions(authOptions, g_authCorrelationId)


    logger.info("Session options: %s" % options)
    eventHandler = SubscriptionEventHandler(BPIPE_RAW_PATH)
    # Create a Session
    g_session = blpapi.Session(options, eventHandler.processEvent)

    # Start a Session
    if not g_session.start():
        logger.info("Failed to start session.")
        return

    logger.info("Connected successfully")
    g_sessionStarted = True

    service = "//blp/mktdata"
    if not g_session.openService(service):
        logger.error("Failed to open %s service" % service)
        return

    # Open authorization service
    if not g_session.openService("//blp/apiauth"):
        logger.info("Failed to open //blp/apiauth")
        return



    logger.info("Subscribing...")
    for identifier in df_cusip_ref['IDENTIFIER'].to_list():
        logger.info(f"SUBSCRIBING {identifier} ...")
        subscribe(identifier)
    g_session.subscribe(g_subscriptions)

    try:
        while True:
            time.sleep(1)
    finally:
        if g_sessionStarted:
            g_session.unsubscribe(g_subscriptions)
            # Stop the session
            g_session.stop()
            time.sleep(1)

if __name__ == "__main__":
    logger.info("SubscriptionWithEventHandlerExample")
    try:
        main()
    except KeyboardInterrupt:
        logger.error("Ctrl+C pressed. Stopping...")

__copyright__ = """
Copyright 2023 Bloomberg Finance L.P
"""