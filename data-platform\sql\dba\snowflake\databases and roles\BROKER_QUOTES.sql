USE ROLE SYSADMIN ;

CREATE DATABASE BROKER_QUOTES;

USE DATABASE BROKER_QUOTES;

USE ROLE SECURITYADMIN;

DROP ROLE IF EXISTS DR_BROKER_QUOTES_READ_ONLY;
DROP ROLE IF EXISTS DR_BROKER_QUOTES_READ_WRITE;
DROP ROLE IF EXISTS DR_BROKER_QUOTES_DB_OWNER;


CREATE ROLE IF NOT EXISTS DR_BROKER_QUOTES_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_BROKER_QUOTES_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_BROKER_QUOTES_DB_OWNER;

GRANT ROLE DR_BROKER_QUOTES_READ_ONLY TO ROLE DR_BROKER_QUOTES_READ_WRITE;
GRANT ROLE DR_BROKER_QUOTES_READ_WRITE TO ROLE DR_BROKER_QUOTES_DB_OWNER;
GRANT ROLE DR_BROKER_QUOTES_DB_OWNER TO ROLE FR_DATA_PLATFORM_LEADS;

USE ROLE SYSADMIN ;

GRANT OWNERSHIP ON DATABASE BROKER_QUOTES TO ROLE DR_BROKER_QUOTES_DB_OWNER;


GRANT USAGE ON DATABASE BROKER_QUOTES TO ROLE DR_BROKER_QUOTES_READ_ONLY;
GRANT USAGE ON DATABASE BROKER_QUOTES TO ROLE DR_BROKER_QUOTES_READ_WRITE;
GRANT USAGE ON DATABASE BROKER_QUOTES TO ROLE DR_BROKER_QUOTES_DB_OWNER;

USE ROLE SECURITYADMIN;

GRANT ROLE DR_BROKER_QUOTES_DB_OWNER TO USER OONTONGTAN;
GRANT ROLE DR_BROKER_QUOTES_DB_OWNER TO USER EMILLAU;

GRANT ROLE DR_BROKER_QUOTES_READ_ONLY  TO ROLE FR_TRADING_PROD;
