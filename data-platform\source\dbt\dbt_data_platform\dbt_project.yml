
# Name your project! Project names should contain only lowercase characters
# and underscores. A good package name should reflect your organization's
# name or the intended use of these models
name: 'dbt_data_platform'
version: '1.0.0'

# This setting configures which "profile" dbt uses for this project.
profile: 'dbt_data_platform'

# These configurations specify where dbt should look for different types of files.
# The `model-paths` config, for example, states that models in this project can be
# found in the "models/" directory. You probably won't need to change these!
model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
  - "target"
  - "dbt_packages"
  - "dbt_modules"

# TODO - Make sure the a run log table is created
#      
#on-run-start:
#- create table if not exists {{target_database}}.datait_admin.etl_load_log"


# Configuring models
# Full documentation: https://docs.getdbt.com/docs/configuring-models

# In this example config, we tell dbt to build all models in the example/
# directory as views. These settings can be overridden in the individual model
# files using the `{{ config(...) }}` macro.
models:
  transient: false
  dbt_data_platform:
    admin:
      schema: datait_admin
    published:
      schema: secmaster_pub
      +materialized: view
      +secure: true
    etl:
      schema: secmaster_etl

    stage:
      raw_spglobal_express:
        +schema: raw_spglobal_express

      raw_coppclark:
        +database: coppclark
        +schema: stage

      raw_ice_mft:
        +database: ice
        +schema: stage
      
      raw_ice_mft_flash:
        +database: ice
        +schema: stage

      raw_ice_prelim_price:
        +database: ice
        +schema: stage

      raw_eex_market_data:
        +database: EEX
        +schema: stage

      raw_eex_spot:
        +database: EEX
        +schema: stage

      raw_swapsmon_hours:
        +database: swapsmon
        +schema: stage

      raw_usda_agri:
        +database: usda
        +schema: stage

      raw_gtt_trade_details:
        +database: COMMOD
        +schema: stage

      raw_emerging_textiles:
        +database: COMMOD
        +schema: stage

      raw_cftc_cotton_on_call:
        +database: COMMOD
        +schema: stage

      raw_spacinsider:
        +database: SPACINSIDER
        +schema: STAGE

    integration:
      int_coppclark:
        +database: coppclark
        +schema: integration

      int_ice_mft:
        +database: ice
        +schema: INTEGRATION
      
      int_ice_mft_flash:
        +database: ice
        +schema: INTEGRATION

      int_ice_prelim_price:
        +database: ice
        +schema: INTEGRATION

      int_eex_market_data:
        +database: EEX
        +schema: INTEGRATION

      int_eex_spot:
        +database: EEX
        +schema: INTEGRATION

      int_swapsmon_hours:
        +database: swapsmon
        +schema: INTEGRATION      
        
      int_usda_agri:
        +database: usda
        +schema: INTEGRATION

      int_gtt_trade_details:
        +database: COMMOD
        +schema: INTEGRATION

      int_emerging_textiles:
        +database: COMMOD
        +schema: INTEGRATION

      int_cftc_cotton_on_call:
        +database: COMMOD
        +schema: INTEGRATION

      int_spacinsider:
        +database: SPACINSIDER
        +schema: INTEGRATION

    publication:    
      pub_coppclark_refdata:
        +database: coppclark
        +schema: hds

      pub_ice_mft:
        +database: ice
        +schema: mft  

      pub_ice_flash:
        +database: ice
        +schema: mft      
      
      pub_ice_prelim_price:
        +database: ice
        +schema: prelim_price

      pub_eex_market_data:
        +database: EEX
        +schema: MARKET_DATA

      pub_eex_spot:
        +database: EEX
        +schema: SPOT

      pub_swpsmon_hours:
        +database: swapsmon
        +schema: hours

      pub_usda_agri:
        +database: usda
        +schema: agri

      pub_gtt_trade_details:
        +database: COMMOD
        +schema: AGRI_GTT

      pub_emerging_textiles:
        +database: COMMOD
        +schema: AGRI_EMERGING_TEXTILES

      pub_cftc_cotton_on_call:
        +database: COMMOD
        +schema: AGRI_CFTC

      pub_spacinsider:
        +database: SPACINSIDER
        +schema: SPACANALYTICS

    
# Set the start_run_dt variable for all models
#pre-hook: "set start_run_dt = sysdate()"

#use this post-hook to insert etl load log info into the etl_load_log table
#+post_hook:
#  sql: "insert into {{target_database}}.datait_admin.etl_load_log ..."
#  transaction: false

#test:
#  schema: datait_tests

#use seeds for file based reference data
seeds:
  dbt_data_platform:
    gtt_seeds:
      +database: COMMOD
      +schema: AGRI_GTT
      hs_code:
        +column_types:
          hs_code: VARCHAR(16777216)

# Variables to control date ranges and transform behavior
# Can use macros to populate or pass in on the dbt command line
#vars:
#  var_start_dt: 'default'
#  var_end_dt: 'default'