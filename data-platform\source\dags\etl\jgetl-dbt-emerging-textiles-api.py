from airflow import DAG
from airflow.operators.python import Python<PERSON>perator
from datetime import datetime, timedelta
import os, sys, pendulum, subprocess, logging, pytz
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from airflow.timetables.trigger import CronTriggerTimetable
from airflow.providers.ssh.operators.ssh import SSHOperator
from airflow.providers.ssh.hooks.ssh import SSHHook
from util.jg_ssh_dbt_operator import JainGlobalSSHDBTOperator
from airflow.hooks.base import BaseHook


from strunner import *

setupEnvironment()

dag_id = os.path.basename(__file__).replace(".py", "")

today = datetime.now(tz=pytz.timezone('America/New_York'))
date_pattern = today.strftime('%Y%m%d')

def get_jg_env():
    env_value = os.getenv('jg_env', 'prod').lower()
    if env_value not in ['prod', 'uat']:
        raise ValueError(f"Invalid jg_env value: {env_value}. Allowed values are 'prod' or 'uat'.")
    return env_value

env = get_jg_env()

conn = BaseHook.get_connection("dbt_conn_id")
key_file = conn.extra_dejson.get("path")

source='emerging_textiles'
dataset = 'fibers_yarns'
jsvc_path = '/opt/jsvc-datait'
source_path = f'{jsvc_path}/prod/JG-DATA-PLATFORM/source'
python_file_path = f'{source_path}/bin'

#fibers & yarns
command_pipeline_rawdata = f'--dataset {source}.{dataset} --date {date_pattern}'
command_copy = f'--dataset {source}.{dataset} --date {date_pattern} --keypair'


default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2025, 5, 9, tz="America/New_York"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 4,
    "retry_delay": timedelta(minutes=15),
}

def download_trade_details(**context):
        jgdata = os.environ.get('JGDATA_PATH')
        script = os.path.join(jgdata, 'bin', 'emerging_textiles_data_download.py')
        output_path = '/jfs/tech1/apps/rawdata/emerging_textiles'

        cmd = [
            'python3', script,
            '--date', date_pattern,
            '--output_path', output_path
        ]
        cmd_str = ' '.join(cmd)
        logging.info(f'Executing command: {cmd_str}')

        try:
            result = subprocess.run(
                cmd_str,
                shell=True,
                check=True,
                capture_output=True,
                text=True
            )
            logging.info("---LOGS---")
            logging.info("Command output: %s", result.stdout)
            logging.info("Command error output: %s", result.stderr)
        except subprocess.CalledProcessError as e:
            logging.error("Command failed with exit status %d", e.returncode)
            logging.error("STDOUT: %s", e.stdout)
            logging.error("STDERR: %s", e.stderr)
            raise


doc_md_DAG=f'This DAG downloads the data from Emerging Textiles API and writes it to dated CSV files.'
dag = DAG(
    dag_id=dag_id,
    default_args=default_args,
    description='This DAG downloads the data from Emerging Textiles API and writes it to dated CSV files.',
    schedule=CronTriggerTimetable('0 17 10 * *',timezone="America/New_York"), # 10th of every month at 5:00 PM ET
    tags=["phdata","emerging", "textiles", "api", "fibers", "yarns"],
    catchup=False,
    doc_md=doc_md_DAG,
)

ssh_hook = SSHHook(
        remote_host='*************',
        username='jsvc-datait',
        key_file=key_file,
        port=22
    )

download_emergin_textiles_task = PythonOperator(
    task_id="download_trade_details",
    python_callable=download_trade_details,
    dag=dag
)

run_rawdata_command = SSHOperator(
    task_id='run_raw_data_command',
    ssh_hook=ssh_hook,
    command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_rawdata.py {command_pipeline_rawdata}',
    do_xcom_push=True,
    dag=dag
)


run_sf_copy_command = SSHOperator(
    task_id='run_pipeline_sf_copy_command',
    ssh_hook=ssh_hook,
    command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_sf_copy.py {command_copy}',
    do_xcom_push=True,
    dag=dag
)

run_dbt_command = JainGlobalSSHDBTOperator(
    task_id='run_dbt_command',
    ssh_hook=ssh_hook,
    base_command = 'run',
    profile = 'dbt_data_platform',
    select_args = [f"tag:{source}_{dataset}"],
    target = env,
    env = env,
)

download_emergin_textiles_task >> run_rawdata_command >> run_sf_copy_command >> run_dbt_command