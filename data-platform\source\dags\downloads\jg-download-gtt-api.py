from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
import os, sys, pendulum, subprocess, logging, pytz
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from airflow.timetables.trigger import CronTriggerTimetable
from strunner import *

setupEnvironment()

dag_id = os.path.basename(__file__).replace(".py", "")

today = datetime.now(tz=pytz.timezone('America/New_York'))
date_pattern = today.strftime('%Y%m%d')

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2025, 5, 9, tz="America/New_York"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 4,
    "retry_delay": timedelta(minutes=15),
}

def download_trade_details(**context):
        jgdata = os.environ.get('JGDATA_PATH')
        script = os.path.join(jgdata, 'bin', 'gtt_data_download.py')
        output_path = '/jfs/tech1/apps/rawdata/gtt/trade_details'

        cmd = [
            'python3', script,
            '--date', date_pattern,
            '--output_path', output_path
        ]
        cmd_str = ' '.join(cmd)
        logging.info(f'Executing command: {cmd_str}')

        try:
            result = subprocess.run(
                cmd,
                shell=True,
                check=True,
                capture_output=True,
                text=True
            )
            logging.info("---LOGS---")
            logging.info("Command output: %s", result.stdout)
            logging.info("Command error output: %s", result.stderr)
        except subprocess.CalledProcessError as e:
            logging.error("Command failed with exit status %d", e.returncode)
            logging.error("STDOUT: %s", e.stdout)
            logging.error("STDERR: %s", e.stderr)
            raise


doc_md_DAG=f'This DAG downloads the data from GTT API and writes it to dated CSV files.'
dag = DAG(
    dag_id=dag_id,
    default_args=default_args,
    description='This DAG downloads the data from GTT API and writes it to dated CSV files.',
    schedule=CronTriggerTimetable('30 10 * * *',timezone="America/New_York"),
    tags=["phdata","gtt", "trade_details","api"],
    catchup=False,
    doc_md=doc_md_DAG,
)


download_gtt_task = PythonOperator(
    task_id="download_trade_details",
    python_callable=download_trade_details,
    dag=dag
)

download_gtt_task