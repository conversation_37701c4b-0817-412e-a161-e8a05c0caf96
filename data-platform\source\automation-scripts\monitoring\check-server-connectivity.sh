#!/bin/bash

# Define the output file
OUTPUT_FILE="/etc/node_exporter/server_connectivity_status.prom"

# Function to read the YAML config file
read_config() {
    local file="$1"
    mapfile -t servers < <(yq eval -r '.server_ip[]' "$file")
}

# Read the config file
read_config "/config/config.yaml"

# Verify if servers are loaded
if [[ ${#servers[@]} -eq 0 ]]; then
    echo "ERROR: No servers found in the configuration. Exiting."
    exit 1
fi

while true; do
    echo "=== Starting new iteration to check server statuses ==="

    # Temporary file for safe writes
    TEMP_FILE="/tmp/server_connectivity_status.prom.$$"

    # Write Prometheus headers
    {
        echo "# HELP ssh_status SSH server status (1=up, 0=down)"
        echo "# TYPE ssh_status gauge"
        echo "# HELP node_textfile_mtime_seconds Timestamp of last update"
        echo "# TYPE node_textfile_mtime_seconds gauge"
        echo "# HELP ssh_connector_script_running Indicator that the script is running"
        echo "# TYPE ssh_connector_script_running gauge"
        echo "ssh_connector_script_running 1"
    } > "$TEMP_FILE"

    # Check SSH connectivity for each server
    for server in "${servers[@]}"; do
        echo "Checking connection to server: $server..."

        if ssh -i ~/.ssh/id_rsa \
               -o StrictHostKeyChecking=no \
               -o UserKnownHostsFile=/dev/null \
               -o ConnectTimeout=5 \
               "$server" "echo 'Connection Successful'" > /dev/null 2>&1; then
            echo "✔️  Server $server is UP"
            echo "ssh_status{server=\"$server\"} 1" >> "$TEMP_FILE"
        else
            echo "❌ Server $server is DOWN"
            echo "ssh_status{server=\"$server\"} 0" >> "$TEMP_FILE"
        fi
    done

    # Timestamp metric
    current_time=$(date +%s)
    echo "node_textfile_mtime_seconds{file=\"$OUTPUT_FILE\"} $current_time" >> "$TEMP_FILE"

    # Atomically move new file in place
    mv "$TEMP_FILE" "$OUTPUT_FILE"
    chmod 644 "$OUTPUT_FILE"

    echo "✅ Metrics updated at $(date) and written to $OUTPUT_FILE"
    echo

    sleep 60
done
