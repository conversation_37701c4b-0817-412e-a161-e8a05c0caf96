from airflow.providers.ssh.operators.ssh import SSHOperator

class JainGlobalSSHDBTOperator(SSHOperator):
    """
    Executes a dbt command on a remote server via SSH.
    
    This operator builds a dbt command from the provided parameters and then executes it
    using an SSH hook. The command is prefixed with the sourcing of environment variables 
    and setting the pipefail flag.
    
    Example usage:
        dbt_command = JainGlobalSSHDBTOperator(
            task_id='dbt_running_coppclark',
            base_command='run',            # could also be 'build', 'debug', 'compile'
            profile='dbt_data_platform',
            select_args=["tag:coppclark"],
            vars='',                       # if no vars, the --vars part is omitted
            target='prod',
            env='prod',                    # used for sourcing export_variables_{env}
            project_dir='/opt/jsvc-datait/prod/JG-DATA-PLATFORM/source/dbt/dbt_data_platform',
            ssh_hook=your_ssh_hook          # pass your SSHHook instance here
        )
    
    The resulting command executed remotely will be similar to:
        source export_variables_prod; set -o pipefail; dbt run -s tag:coppclark --project-dir 
        /opt/jsvc-datait/prod/JG-DATA-PLATFORM/source/dbt/dbt_data_platform --profile dbt_data_platform --target prod
    """
    
    def __init__(self,
                 base_command: str,
                 profile: str,
                 select_args: list = None,
                 vars: str = None,
                 target: str = None,
                 env: str = 'prod',
                 project_dir: str = '/opt/jsvc-datait/prod/data-platform/source/dbt/dbt_data_platform',
                 timeout = 300, #5 min of timeout
                 *args, **kwargs):
        # Build the core dbt command
        dbt_cmd = f"dbt {base_command}"
        
        # Append select arguments if provided
        if select_args:
            # Here, we assume each select argument will be preceded by "-s"
            select_str = ' '.join(f"-s {arg}" for arg in select_args)
            dbt_cmd += f" {select_str}"
            
        # Append project directory, profile, and target (and vars if provided)
        dbt_cmd += f" --project-dir {project_dir}"
        
        if profile:
            dbt_cmd += f" --profile {profile}"
        
        if vars:
            # If vars is provided and non-empty, add it to the command.
            dbt_cmd += f" --vars '{vars}'"
            
        if target:
            dbt_cmd += f" --target {target}"
        
        # Prepend the command with sourcing of the environment variables and setting pipefail.
        full_command = f"source export_variables_{env}; set -o pipefail; {dbt_cmd}"

        # Pass the constructed command to the SSHOperator's constructor.
        super().__init__(command=full_command, conn_timeout=timeout, cmd_timeout=timeout, *args, **kwargs)


