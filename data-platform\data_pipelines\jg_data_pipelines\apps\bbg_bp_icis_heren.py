import os
import gzip
import shutil
import tempfile
import pandas as pd

from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from utils.date_utils import get_n_bday_yyyymmdd, get_n_bday, get_now
from bloomberg.bpipe import BpipeHandler

if __name__ == "__main__":
    securities = ["IINDNEDA ICIS Index" ,"IINDNGDA ICIS Index" ,"IINDTEDA ICIS Index" ,"IINDNGMI ICIS Index" ,"IINDNGDI ICIS Index" ,"IINDSESW ICIS Index" ,"IINDPEDA ICIS Index" ,"IINDSEDA ICIS Index" ,"IINDZGDA ICIS Index" ,"IINDZGDC ICIS Index" ,"IINDVEDA ICIS Index" ,"IINDTEM1 ICIS Index" ,"IINDVEDC ICIS Index" ,"IINDCEDA ICIS Index" ,"IINDCEDC ICIS Index" ,"IINDGEMC ICIS Index" ,"IINDVEMC ICIS Index" ,"IINDVESW ICIS Index" ,"IINDSEDC ICIS Index" ,"IINDSEM1 ICIS Index" ,"IINDPEDC ICIS Index" ,"IINDPEMC ICIS Index" ,"IINDTEDI ICIS Index" ,"IINDPESW ICIS Index" ,"IINDNGDC ICIS Index" ,"IINDNGM1 ICIS Index" ,"IINDZGMA ICIS Index" ,"IINDZGWA ICIS Index" ,"IINDCEMA ICIS Index" ,"IINDCEMC ICIS Index" ,"IINDGEDA ICIS Index" ,"IINDNGMC ICIS Index" ,"IINDNGWA ICIS Index" ,"IINDNGWC ICIS Index" ,"IINDNGWI ICIS Index" ,"IINDTUMI ICIS Index" ,"IINDGESW ICIS Index" ,"IINDTEDC ICIS Index" ,"IINDPEM1 ICIS Index" ,"IINDNEWD ICIS Index" ,"IINDNGMA ICIS Index" ,"IINDZGMC ICIS Index" ,"IINDZGSW ICIS Index" ,"IINDNEMA ICIS Index" ,"IINDTEMA ICIS Index" ,"IINDTEMC ICIS Index" ,"IINDTESW ICIS Index" ,"IINDTEWA ICIS Index" ,"IINDTEWC ICIS Index" ,"IINDCESW ICIS Index" ,"IINDNGSW ICIS Index" ,"IINDSEMC ICIS Index" ,"IINDNGWD ICIS Index"]
    
    bpipe_instance = ("Tech1ProdBPipe47537.jainglobal.net", 8194)
    bpipe_app = "JAIN:pmdashboard-bps"
    bpipe_handler = BpipeHandler(bpipe_instance, bpipe_app)

    to_date = get_n_bday(0)
    from_date = get_n_bday(15)

    df_icis_here = bpipe_handler.fetch_hist(p_ticker=securities, p_field="PX_LAST", p_start=from_date, p_end=to_date)

    print(df_icis_here.head())

    df_icis_here.rename(columns= {"ticker": "BBG_TICKER", "field": "FIELD", "date": "DATE", "value": "VALUE"}, inplace=True)
    df_icis_here["DATE"] = pd.to_datetime(df_icis_here["DATE"], format="%m/%d/%Y", errors="coerce")
    df_icis_here["VALUE"] = pd.to_numeric(df_icis_here["VALUE"], errors="coerce")
    df_icis_here["WHEN_UPDATED"] = get_now()

    print(df_icis_here.head())

    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_ONDEMAND",
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER"
    )

    ret_val = adaptor.upsert(df_icis_here, "BBG_ICIS_HEREN", ["BBG_TICKER", "FIELD", "DATE"])
    print(ret_val)