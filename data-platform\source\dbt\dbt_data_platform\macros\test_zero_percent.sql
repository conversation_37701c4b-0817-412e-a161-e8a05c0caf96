-- Custom dbt test: Asserts that the percentage of zeros in a column for the last date is less than or equal to a threshold
{% test test_zero_percent(model, column_name,threshold) %}

    {% set model_name = model if model is string else model.name %}


    with check_zero as (
    Select
        count(*) as total_count,
        sum(case {{ column_name }} when 0 then 1 else 0 end ) as zero_count,
        zero_count/total_count as zero_percent
    from {{ model_name }}

    )
    Select
    *
    from check_zero
    where (zero_percent >= {{ threshold }} or total_count = 0)

{% endtest %}