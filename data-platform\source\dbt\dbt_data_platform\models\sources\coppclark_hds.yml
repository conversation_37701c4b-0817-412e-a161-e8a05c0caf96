version: 2

sources:
  - name: coppclark
    database: vendor_raw
    schema: coppclark_hds
    tags: [coppclark]
    description: '{{ doc("coppclark") }}'
    tables:
      - name: centre_change_report_raw
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits"
          - name: centre_code
            description: "3 for ISO Currency Code, 3 for UN/LOCODE, 4 for Exchange MIC Code, 8 for Exchange MIC Code with subexchange"
          - name: new_centre_code
            description: "3 for ISO Currency Code, 3 for UN/LOCODE, 4 for Exchange MIC Code, 8 for Exchange MIC Code with subexchange"
          - name: iso_country_code
            description: ""
          - name: new_iso_country_code
            description: ""
          - name: centre
            description: "Name of the center"
          - name: new_centre
            description: "Name of the center"
          - name: file_type
            description: "C for currency, B for Bank/Financial Center, S for Exchange (settlement) and T for Exchange (Trading)"
          - name: add_delete
            description: "A for add,  U for Update"
          - name: filename
            description: "metadata column used to determine file origin"
          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"
            

      - name: currencies_raw
        description: "Raw data load that are only updated once a every two weeks"
        freshness:
          warn_after: {count: 15, period: day}
          error_after: { count: 21, period: day }
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits; this is a fixed numeric code, which when taken together with FileType, constitutes a unique identifer"

          - name: iso_currency_code
            description: "Based on ISO 4217; currency codes are subject to change"

          - name: iso_country_code
            description: "Based on ISO 3166"
    
          - name: related_financial_center
            description: "Name of the primary city associated with the currency"

          - name: event_year
            description: "Year of event"

          - name: event_date
            description: "Depends on the current date format (yyyyMMdd)"
          
          - name: event_day_of_week
            description: "Day of week"

          - name: event_name
            description: "Description of holiday or event"

          - name: file_type
            description: 'For this file, this will always be "C" for Currency'

          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"
          
          - name: filename
            description: "metadata column used to determine file origin"

          - name: gbd
            description: "tells whether it is a Half day or Full day holiday"

      - name: currencies_history_raw
        description: "Raw historical data load that are only updated once a year"
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits; this is a fixed numeric code, which when taken together with FileType, constitutes a unique identifer"

          - name: iso_currency_code
            description: "Based on ISO 4217; currency codes are subject to change"

          - name: iso_country_code
            description: "Based on ISO 3166"
    
          - name: related_financial_center
            description: "Name of the primary city associated with the currency"

          - name: event_year
            description: "Year of event"

          - name: event_date
            description: "Depends on the current date format (yyyyMMdd)"
          
          - name: event_day_of_week
            description: "Day of week"

          - name: event_name
            description: "Description of holiday or event"

          - name: file_type
            description: 'For this file, this will always be "C" for Currency'

          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"
          
          - name: filename
            description: "metadata column used to determine file origin"

          - name: gbd
            description: "tells whether it is a Half day or Full day holiday"

      - name: exchange_settlement_raw
        description: "Raw data load that are only updated once a every two weeks"
        freshness:
          warn_after: {count: 15, period: day}
          error_after: { count: 21, period: day }
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits; this is a fixed numeric code, which when taken together with FileType, constitutes a unique identifer"

          - name: isomic_code
            description: "Based on ISO 10383; sub-markets on a main exchange may be indicated by additional 3-character extension; MICs are subject to change"

          - name: iso_country_code
            description: "Based on ISO 3166"
    
          - name: exchange_name
            description: "Name of exchange or market"

          - name: event_year
            description: "Year of event"

          - name: event_date
            description: "Depends on the current date format (yyyyMMdd)"
          
          - name: event_day_of_week
            description: "Day of week"

          - name: event_name
            description: "Description of holiday or event"

          - name: file_type
            description: 'For this file, this will always be "C" for Currency'

          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"
          
          - name: filename
            description: "metadata column used to determine file origin"

          - name: gbd
            description: "tells whether it is a Half day or Full day holiday"

      - name: exchange_settlement_history_raw
        description: "Raw historical data load that are only updated once a year"
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits; this is a fixed numeric code, which when taken together with FileType, constitutes a unique identifer"

          - name: isomic_code
            description: "Based on ISO 10383; sub-markets on a main exchange may be indicated by additional 3-character extension; MICs are subject to change"

          - name: iso_country_code
            description: "Based on ISO 3166"
    
          - name: exchange_name
            description: "Name of exchange or market"

          - name: event_year
            description: "Year of event"

          - name: event_date
            description: "Depends on the current date format (yyyyMMdd)"
          
          - name: event_day_of_week
            description: "Day of week"

          - name: event_name
            description: "Description of holiday or event"

          - name: file_type
            description: 'For this file, this will always be "C" for Currency'

          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"
          
          - name: filename
            description: "metadata column used to determine file origin"

          - name: gbd
            description: "tells whether it is a Half day or Full day holiday"

      - name: exchange_trading_raw
        description: "Raw data load that are only updated once a every two weeks"
        freshness:
          warn_after: {count: 15, period: day}
          error_after: { count: 21, period: day }
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits; this is a fixed numeric code, which when taken together with FileType, constitutes a unique identifer"

          - name: isomic_code
            description: "Based on ISO 10383; sub-markets on a main exchange may be indicated by additional 3-character extension; MICs are subject to change"

          - name: iso_country_code
            description: "Based on ISO 3166"
    
          - name: exchange_name
            description: "Name of exchange or market"

          - name: event_year
            description: "Year of event"

          - name: event_date
            description: "Depends on the current date format (yyyyMMdd)"
          
          - name: event_day_of_week
            description: "Day of week"

          - name: event_name
            description: "Description of holiday or event"

          - name: file_type
            description: 'For this file, this will always be "C" for Currency'

          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"
          
          - name: filename
            description: "metadata column used to determine file origin"

          - name: gbd
            description: "tells whether it is a Half day or Full day holiday"

      - name: exchange_trading_history_raw
        description: "Raw historical data load that are only updated once a year"
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits; this is a fixed numeric code, which when taken together with FileType, constitutes a unique identifer"

          - name: isomic_code
            description: "Based on ISO 10383; sub-markets on a main exchange may be indicated by additional 3-character extension; MICs are subject to change"

          - name: iso_country_code
            description: "Based on ISO 3166"
    
          - name: exchange_name
            description: "Name of exchange or market"

          - name: event_year
            description: "Year of event"

          - name: event_date
            description: "Depends on the current date format (yyyyMMdd)"
          
          - name: event_day_of_week
            description: "Day of week"

          - name: event_name
            description: "Description of holiday or event"

          - name: file_type
            description: 'For this file, this will always be "C" for Currency'

          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"
          
          - name: filename
            description: "metadata column used to determine file origin"

          - name: gbd
            description: "tells whether it is a Half day or Full day holiday"

      - name: financial_centres_raw
        description: "Raw data load that are only updated once a every two weeks"
        freshness:
          warn_after: {count: 15, period: day}
          error_after: { count: 21, period: day }
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits; this is a fixed numeric code, which when taken together with FileType, constitutes a unique identifer"

          - name: iso_currency_code
            description: "Based on ISO 4217; currency codes are subject to change"

          - name: iso_country_code
            description: "Based on ISO 3166"
    
          - name: financial_center
            description: "Name of city, location of financial center"
    
          - name: un_locode
            description: "In combination with the country code, forms a unique identifier for the financial center"

          - name: event_year
            description: "Year of event"

          - name: event_date
            description: "Depends on the current date format (yyyyMMdd)"
          
          - name: event_day_of_week
            description: "Day of week"

          - name: event_name
            description: "Description of holiday or event"

          - name: file_type
            description: 'For this file, this will always be "C" for Currency'

          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"
          
          - name: filename
            description: "metadata column used to determine file origin"

          - name: gbd
            description: "tells whether it is a Half day or Full day holiday"

      - name: financial_centres_history_raw
        description: "Raw historical data load that are only updated once a year"
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits; this is a fixed numeric code, which when taken together with FileType, constitutes a unique identifer"

          - name: iso_currency_code
            description: "Based on ISO 4217; currency codes are subject to change"

          - name: iso_country_code
            description: "Based on ISO 3166"
    
          - name: financial_center
            description: "Name of city, location of financial center"
    
          - name: un_locode
            description: "In combination with the country code, forms a unique identifier for the financial center"

          - name: event_year
            description: "Year of event"

          - name: event_date
            description: "Depends on the current date format (yyyyMMdd)"
          
          - name: event_day_of_week
            description: "Day of week"

          - name: event_name
            description: "Description of holiday or event"

          - name: file_type
            description: 'For this file, this will always be "C" for Currency'

          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"
          
          - name: filename
            description: "metadata column used to determine file origin"

          - name: gbd
            description: "tells whether it is a Half day or Full day holiday"

      - name: fxspot_calendar_forward_values_raw

      - name: fxspot_calendar_latam_ndfs_raw

      - name: fxspot_calendar_ndfs_foward_and_fixing_dates_raw

      - name: special_raw
        description: "Raw data load that are only updated once a every two weeks"
        freshness:
          warn_after: {count: 15, period: day}
          error_after: { count: 21, period: day }
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits; this is a fixed numeric code, which when taken together with FileType, constitutes a unique identifer"

          - name: isomic_code
            description: "Based on ISO 10383; sub-markets on a main exchange may be indicated by additional 3-character extension; MICs are subject to change"

          - name: iso_country_code
            description: "Based on ISO 3166"
    
          - name: exchange_name
            description: "Name of exchange or market"

          - name: event_year
            description: "Year of event"

          - name: event_date
            description: "Depends on the current date format (yyyyMMdd)"
          
          - name: event_day_of_week
            description: "Day of week"

          - name: event_name
            description: "Description of holiday or event"

          - name: file_type
            description: 'For this file, this will always be "C" for Currency'

          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"
          
          - name: filename
            description: "metadata column used to determine file origin"

          - name: gbd
            description: "tells whether it is a Half day or Full day holiday"


      - name: special_history_raw
        description: "Raw historical data load that are only updated once a year"
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits; this is a fixed numeric code, which when taken together with FileType, constitutes a unique identifer"

          - name: isomic_code
            description: "Based on ISO 10383; sub-markets on a main exchange may be indicated by additional 3-character extension; MICs are subject to change"

          - name: iso_country_code
            description: "Based on ISO 3166"
    
          - name: exchange_name
            description: "Name of exchange or market"

          - name: event_year
            description: "Year of event"

          - name: event_date
            description: "Depends on the current date format (yyyyMMdd)"
          
          - name: event_day_of_week
            description: "Day of week"

          - name: event_name
            description: "Description of holiday or event"

          - name: file_type
            description: 'For this file, this will always be "C" for Currency'

          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"
          
          - name: filename
            description: "metadata column used to determine file origin"

          - name: gbd
            description: "tells whether it is a Half day or Full day holiday"


      - name: weekend_definitions_raw
        columns:
          - name: center_id
            description: "Currently ranges from 1 to 4 digits; this is a fixed numeric code, which when taken together with FileType, constitutes a unique identifer"

          - name: file_type
            description: "C for currency, B for Bank/Financial Center, S for Exchange (settlement) and T for Exchange (Trading)"

          - name: iso_country_code
            description: "Based on ISO 3166"

          - name: centre_code
            description: "3 for ISO Currency Code, 3 for UN/LOCODE, 4 for Exchange MIC Code, 8 for Exchange MIC Code with subexchange"

          - name: centre
            description: "Name of the center"

          - name: weekend
            description: "Day of week"

          - name: filename
            description: "metadata column used to determine file origin"
          
          - name: start_scan_time
            description: "metadata column used by snowflake to check last time data was scanned"

      - name: working_weekends_history_raw

      - name: working_weekends_raw
  
