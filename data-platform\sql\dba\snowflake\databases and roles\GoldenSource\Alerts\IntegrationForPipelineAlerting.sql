
CREATE NOTIFICATION INTEGRATION OMNI_PROD_NOTIFICATION_INTEGRATION
  ENABLED = TRUE
  DIRECTION = OUTBOUND
  TYPE = QUEUE
  NOTIFICATION_PROVIDER = AWS_SNS
  AWS_SNS_TOPIC_ARN = 'arn:aws:sns:us-east-1:390844739337:gs-jain-us-east-1-prod-snowflake-SF-notification'
  AWS_SNS_ROLE_ARN = 'arn:aws:iam::390844739337:role/gs-jain-us-east-1-prod-snowflake-sf-role';

DESC NOTIFICATION INTEGRATION OMNI_PROD_NOTIFICATION_INTEGRATION;
