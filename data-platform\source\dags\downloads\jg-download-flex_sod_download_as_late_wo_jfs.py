from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import timedelta
import os, sys, pendulum, subprocess, logging
from airflow.providers.http.operators.http import HttpOperator
from util.validate import check_for_anomalies
from airflow.timetables.trigger import CronTriggerTimetable

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()

def run_flex_sod_preprocess():
    
    JGDATA_PATH = os.environ.get("JGDATA_PATH")
    command = (
        f"python3 {JGDATA_PATH}/dags/flex_sod_main_smtp_wo_jfs.py SOD_PnL_jainglobal_Flex_Flash_*.csv AS_Late"
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.yesterday(tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id='jg-position-sod-wo-jfs-as-late',
    default_args=default_args,
    description='This DAG downloads the data from FLEX SOD to S3 Data account',
    schedule = CronTriggerTimetable('50 21 * * *', timezone="America/New_York"),
    tags=["jgdata","POSITION-SOD"],
    catchup=False,
)

raw = PythonOperator(
    task_id="jg-position-sod-wo-jfs-as-late",
    python_callable=run_flex_sod_preprocess,
    dag=dag
)

validation_job = HttpOperator(
    task_id="call-daily-data-validation-api",
    http_conn_id="http_default_raw", 
    endpoint="get_sod_positions/?vendor=Sod_Postions&model=as-late",
    method="GET",
    headers={"Content-Type": "application/json"},
    response_check=check_for_anomalies,
    extra_options={"check_response": True},
    log_response=True,
    dag=dag
)

raw >> validation_job