from airflow import <PERSON><PERSON>
from datetime import datetime, timedelta
from airflow.timetables.trigger import CronTriggerTimetable
from util.jg_ssh_dbt_operator import JainGlobalSSHDBTOperator
import os
import pendulum
from airflow.hooks.base import BaseHook
from airflow.providers.ssh.hooks.ssh import SSHHook

def get_jg_env():
    env_value = os.getenv('jg_env', 'prod').lower()
    if env_value not in ['prod', 'uat']:
        raise ValueError(f"Invalid jg_env value: {env_value}. Allowed values are 'prod' or 'uat'.")
    return env_value

env = get_jg_env()

dag_id = os.path.basename(__file__).replace(".py", "")


conn = BaseHook.get_connection("dbt_conn_id")
key_file = conn.extra_dejson.get("path")

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.yesterday(tz="America/New_York"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

doc_md_DAG=f'This DAG runs Data Quality check on Snowflake Option Metrics Ivydb database'
with DAG(
    dag_id=dag_id,
    default_args=default_args,
    description='This DAG runs Data Quality check on Snowflake Option Metrics Ivydb database',
    schedule=CronTriggerTimetable('0 5 * * *',timezone="America/New_York"), 
    tags=['phdata','optionmetrics','ivydb','dbt'],
    catchup=False,
    doc_md=doc_md_DAG
) as dag:

    ssh_hook = SSHHook(
        remote_host='*************',
        username='jsvc-datait',
        key_file=key_file,
        port=22
    )

    test_dbt_command = JainGlobalSSHDBTOperator(
        task_id='run_dbt_command_test',
        ssh_hook=ssh_hook,
        base_command = 'test',
        profile = 'dbt_data_platform',
        select_args = [f"source:optionmetrics_ivydb"],
        target = env,
        env = env,
    )


test_dbt_command

