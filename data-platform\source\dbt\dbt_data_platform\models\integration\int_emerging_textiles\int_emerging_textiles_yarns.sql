{{
   config(
       tags=['emerging_textiles_fibers_yarns'],
       materialized='table',
   )
}}




WITH parsed AS (
 SELECT
   *,
   CASE
     WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
     THEN TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD')
     ELSE NULL
   END AS file_dt,
   CASE WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
        THEN EXTRACT(year  FROM TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD'))
   END AS file_year,
   CASE WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
        THEN EXTRACT(month FROM TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD'))
   END AS file_month,
   CASE WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
        THEN EXTRACT(day   FROM TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD'))
   END AS file_day
 FROM {{ ref("stg_emerging_textiles_yarns") }}
),
dedup AS (
 SELECT
   parsed.*,
   CASE
     WHEN file_dt IS NULL
     THEN 1
     ELSE ROW_NUMBER() OVER (
            PARTITION BY file_year, file_month
            ORDER BY file_day DESC
          )
   END AS rn
 FROM parsed
)
Select
"Date"                                                   as "date",
"China_Domestic_100%_Cotton_Yarn_Open_End_10s_Yuan_MT"   as "china_domestic_100%_cotton_yarn_open_end_10s_yuan_mt",
"China_Domestic_100%_Cotton_Yarn_Carded_32s_Yuan_MT"     as "china_domestic_100%_cotton_yarn_carded_32s_yuan_mt",
"China_Domestic_100%_Cotton_Yarn_Combed_40s_Yuan_MT"     as "china_domestic_100%_cotton_yarn_combed_40s_yuan_mt",
"China_Domestic_100%_Polyester_Yarn_32s_Yuan_MT"         as "china_domestic_100%_polyester_yarn_32s_yuan_mt",
"China_Domestic_100%_Viscose_Yarn_30s_Yuan_MT"           as "china_domestic_100%_viscose_yarn_30s_yuan_mt",
"China_Domestic_100%_Cotton_Yarn_Open_End_10s_USS_Kg"    as "china_domestic_100%_cotton_yarn_open_end_10s_uss_kg",
"China_Domestic_100%_Cotton_Yarn_Carded_32s_USS_Kg"      as "china_domestic_100%_cotton_yarn_carded_32s_uss_kg",
"China_Domestic_100%_Cotton_Yarn_Combed_40s_USS_Kg"      as "china_domestic_100%_cotton_yarn_combed_40s_uss_kg",
"China_Domestic_100%_Polyester_Yarn_32s_USS_Kg"          as "china_domestic_100%_polyester_yarn_32s_uss_kg",
"China_Domestic_100%_Viscose_Yarn_30s_USS_Kg"            as "china_domestic_100%_viscose_yarn_30s_uss_kg",
"China_Imports_FCY_Index_Cotton_21S_USS_Kg"              as "china_imports_fcy_index_cotton_21s_uss_kg",
"China_Imports_FCY_Index_Cotton_32S_USS_Kg"              as "china_imports_fcy_index_cotton_32s_uss_kg",
"China_Imports_FCY_Index_Cotton_Combed_32S_USS_Kg"       as "china_imports_fcy_index_cotton_combed_32s_uss_kg",
"China_Imports_FCY_Index__Cotton_21S_Yuan_MT"            as "china_imports_fcy_index__cotton_21s_yuan_mt",
"China_Imports_FCY_Index__Cotton_32S_Yuan_MT"            as "china_imports_fcy_index__cotton_32s_yuan_mt",
"China_Imports_FCY_Index__Cotton_Combed_32S_Yuan_MT"     as "china_imports_fcy_index__cotton_combed_32s_yuan_mt",
"China_Imports_India__Cotton_OE_10S_USS_Kg"              as "china_imports_india__cotton_oe_10s_uss_kg",
"China_Imports_India__Cotton_21S_USS_Kg"                 as "china_imports_india__cotton_21s_uss_kg",
"China_Imports_India__Cotton_32S_USS_Kg"                 as "china_imports_india__cotton_32s_uss_kg",
"China_Imports_India__Cotton_Combed_32S_USS_Kg"          as "china_imports_india__cotton_combed_32s_uss_kg",
"China_Imports_India__Cotton_OE_10S_Yuan_MT"             as "china_imports_india__cotton_oe_10s_yuan_mt",
"China_Imports_India__Cotton_21S_Yuan_MT"                as "china_imports_india__cotton_21s_yuan_mt",
"China_Imports_India__Cotton_32S_Yuan_MT"                as "china_imports_india__cotton_32s_yuan_mt",
"China_Imports_India__Cotton_Combed_32S_Yuan_MT"         as "china_imports_india__cotton_combed_32s_yuan_mt",
"China_Imports_Pakistan_Cotton_21S_USS_Kg"               as "china_imports_pakistan_cotton_21s_uss_kg",
"China_Imports_Vietnam_Cotton_32s_USS_Kg"                as "china_imports_vietnam_cotton_32s_uss_kg",
"China_Imports_Indonesia_Cotton_32s_USS_Kg"              as "china_imports_indonesia_cotton_32s_uss_kg",
"China_Imports_Pakistan_Cotton_21S_Yuan_MT"              as "china_imports_pakistan_cotton_21s_yuan_mt",
"China_Imports_Vietnam_Cotton_32s_Yuan_MT"               as "china_imports_vietnam_cotton_32s_yuan_mt",
"China_Imports_Indonesia_Cotton_32s_Yuan_MT"             as "china_imports_indonesia_cotton_32s_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Cotton_16S_Yuan_MT"     as "china_qiangqing_(zhejiang)_100%_cotton_16s_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Cotton_21S_Yuan_MT"     as "china_qiangqing_(zhejiang)_100%_cotton_21s_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Cotton_32S__Yuan_MT"    as "china_qiangqing_(zhejiang)_100%_cotton_32s__yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Cotton_40S__Yuan_MT"    as "china_qiangqing_(zhejiang)_100%_cotton_40s__yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Cotton_40S_2_Yuan_MT"   as "china_qiangqing_(zhejiang)_100%_cotton_40s_2_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Cotton_40S_Combed_Yuan_MT" as "china_qiangqing_(zhejiang)_100%_cotton_40s_combed_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Cotton_60S_Combed_Yuan_MT" as "china_qiangqing_(zhejiang)_100%_cotton_60s_combed_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Polyester_16S_Yuan_MT"  as "china_qiangqing_(zhejiang)_100%_polyester_16s_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Polyester_21S_Yuan_MT"  as "china_qiangqing_(zhejiang)_100%_polyester_21s_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Polyester_32S_Weaving_Yuan_MT" as "china_qiangqing_(zhejiang)_100%_polyester_32s_weaving_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Polyester_45S_Yuan_MT"  as "china_qiangqing_(zhejiang)_100%_polyester_45s_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Polyester_50S_Yuan_MT"  as "china_qiangqing_(zhejiang)_100%_polyester_50s_yuan_mt",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_16S_Yuan_MT" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_16s_yuan_mt",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_21S_Yuan_MT" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_21s_yuan_mt",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_32S_Yuan_MT" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_32s_yuan_mt",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_45S_Yuan_MT" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_45s_yuan_mt",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_32S_Combed_Yuan_MT" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_32s_combed_yuan_mt",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_45S_Combed_Yuan_MT" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_45s_combed_yuan_mt",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_60S_Combed_Yuan_MT" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_60s_combed_yuan_mt",
"China_Qiangqing_(Zhejiang)_80_20_Polyester-Cotton_21S_Yuan_MT" as "china_qiangqing_(zhejiang)_80_20_polyester-cotton_21s_yuan_mt",
"China_Qiangqing_(Zhejiang)_80_20_Polyester-Cotton_32S_Yuan_MT" as "china_qiangqing_(zhejiang)_80_20_polyester-cotton_32s_yuan_mt",
"China_Qiangqing_(Zhejiang)_80_20_Polyester-Cotton_45S_Yuan_MT"     as "china_qiangqing_(zhejiang)_80_20_polyester-cotton_45s_yuan_mt",
"China_Qiangqing_(Zhejiang)_80_20_Polyester-Cotton_60S_Yuan_MT"     as "china_qiangqing_(zhejiang)_80_20_polyester-cotton_60s_yuan_mt",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Viscose_21S_Yuan_MT"    as "china_qiangqing_(zhejiang)_65_35_polyester-viscose_21s_yuan_mt",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Viscose_32S_Yuan_MT"    as "china_qiangqing_(zhejiang)_65_35_polyester-viscose_32s_yuan_mt",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Viscose_40S_Yuan_MT"    as "china_qiangqing_(zhejiang)_65_35_polyester-viscose_40s_yuan_mt",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Viscose_40S_2_Yuan_MT"  as "china_qiangqing_(zhejiang)_65_35_polyester-viscose_40s_2_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Viscose_20S_Yuan_MT"               as "china_qiangqing_(zhejiang)_100%_viscose_20s_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Viscose_40S_Yuan_MT"    as "china_qiangqing_(zhejiang)_100%_viscose_40s_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Viscose_50S_Yuan_MT"    as "china_qiangqing_(zhejiang)_100%_viscose_50s_yuan_mt",
"China_Qiangqing_(Zhejiang)_100%_Cotton_16S_USS_Kg"      as "china_qiangqing_(zhejiang)_100%_cotton_16s_uss_kg",
"China_Qiangqing_(Zhejiang)_100%_Cotton__21S_USS_Kg"     as "china_qiangqing_(zhejiang)_100%_cotton__21s_uss_kg",
"China_Qiangqing_(Zhejiang)_100%_Cotton_32S__USS_Kg"     as "china_qiangqing_(zhejiang)_100%_cotton_32s__uss_kg",
"China_Qiangqing_(Zhejiang)_100%_Cotton_40S__USS_Kg"     as "china_qiangqing_(zhejiang)_100%_cotton_40s__uss_kg",
"China_Qiangqing_(Zhejiang)_100%_Cotton_40S_2_USS_Kg"    as "china_qiangqing_(zhejiang)_100%_cotton_40s_2_uss_kg",
"China_Qiangqing_(Zhejiang)_100%_Cotton_40S_Combed_USS_Kg" as "china_qiangqing_(zhejiang)_100%_cotton_40s_combed_uss_kg",
"China_Qiangqing_(Zhejiang)_100%_Cotton_60S_Combed_USS_Kg" as "china_qiangqing_(zhejiang)_100%_cotton_60s_combed_uss_kg",
"China_Qiangqing_(Zhejiang)_100%_Polyester_16S_USS_Kg"   as "china_qiangqing_(zhejiang)_100%_polyester_16s_uss_kg",
"China_Qiangqing_(Zhejiang)_100%_Polyester_21S_USS_Kg"   as "china_qiangqing_(zhejiang)_100%_polyester_21s_uss_kg",
"China_Qiangqing_(Zhejiang)_100%_Polyester_32S_Weaving_USS_Kg" as "china_qiangqing_(zhejiang)_100%_polyester_32s_weaving_uss_kg",
"China_Qiangqing_(Zhejiang)_100%_Polyester_45S_USS_Kg"   as "china_qiangqing_(zhejiang)_100%_polyester_45s_uss_kg",
"China_Qiangqing_(Zhejiang)_100%_Polyester_50S_USS_Kg"   as "china_qiangqing_(zhejiang)_100%_polyester_50s_uss_kg",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_16S_USS_Kg" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_16s_uss_kg",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_21S_USS_Kg" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_21s_uss_kg",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_32S_USS_Kg" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_32s_uss_kg",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_45S_USS_Kg" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_45s_uss_kg",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_32S_Combed_USS_Kg" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_32s_combed_uss_kg",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_45S_Combed_USS_Kg" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_45s_combed_uss_kg",
"China_Qiangqing_(Zhejiang)_65_35_Polyester-Cotton_60S_Combed_USS_Kg" as "china_qiangqing_(zhejiang)_65_35_polyester-cotton_60s_combed_uss_kg",
"China_Qiangqing_(Zhejiang)_80_20_Polyester-Cotton_21S_USS_Kg" as "china_qiangqing_(zhejiang)_80_20_polyester-cotton_21s_uss_kg",
"China_Qiangqing_(Zhejiang)_80_20_Polyester-Cotton_32S_USS_Kg" as "china_qiangqing_(zhejiang)_80_20_polyester-cotton_32s_uss_kg",
"China_Qiangqing_(Zhejiang)_80_20_Polyester-Cotton_45S_USS_Kg" as "china_qiangqing_(zhejiang)_80_20_polyester-cotton_45s_uss_kg",
"China_Qiangqing_(Zhejiang)_80_20_Polyester-Cotton_60S_USS_Kg" as "china_qiangqing_(zhejiang)_80_20_polyester-cotton_60s_uss_kg",
"India_Cotton_Carded_Ring_Knitting_24s_Rupees___Kg" as "india_cotton_carded_ring_knitting_24s_rupees___kg",
"India_Cotton_Carded_Ring_Knitting_26s_Rupees___Kg" as "india_cotton_carded_ring_knitting_26s_rupees___kg",
"India_Cotton_Carded_Ring_Knitting_30s_Rupees___Kg" as "india_cotton_carded_ring_knitting_30s_rupees___kg",
"India_Cotton_Combed_Ring_Knitting_30s_Rupees___Kg" as "india_cotton_combed_ring_knitting_30s_rupees___kg",
"India_Cotton_Combed_Ring_Knitting_40s_Rupees___Kg" as "india_cotton_combed_ring_knitting_40s_rupees___kg",
"India_Poly-Cotton_Carded_52_48_Knitting_30s_Rupees___Kg" as "india_poly-cotton_carded_52_48_knitting_30s_rupees___kg",
"India_Poly-Viscose_80_20_Hosiery_30s_Rupees___Kg" as "india_poly-viscose_80_20_hosiery_30s_rupees___kg",
"India_100%_Polyester_Knitting_30s_Rupees___Kg" as "india_100%_polyester_knitting_30s_rupees___kg",
"India_100%_Viscose_30s_Hosiery_30s_Rupees___Kg" as "india_100%_viscose_30s_hosiery_30s_rupees___kg",
"India_Cotton_Carded_Ring_Knitting_24s_USS_Kg" as "india_cotton_carded_ring_knitting_24s_uss_kg",
"India_Cotton_Carded_Ring_Knitting_26s_USS_Kg" as "india_cotton_carded_ring_knitting_26s_uss_kg",
"India_Cotton_Carded_Ring_Knitting_30s_USS___Kg" as "india_cotton_carded_ring_knitting_30s_uss___kg",
"India_Cotton_Combed_Ring_Knitting_30s_USS___Kg" as "india_cotton_combed_ring_knitting_30s_uss___kg",
"India_Poly-Cotton_Carded_52_48_Knitting_30s_USS___Kg" as "india_poly-cotton_carded_52_48_knitting_30s_uss___kg",
"India_Poly-Viscose_80_20_Hosiery_30s_USS___Kg" as "india_poly-viscose_80_20_hosiery_30s_uss___kg",
"India_100%_Polyester_Knitting_30s_USS___Kg" as "india_100%_polyester_knitting_30s_uss___kg",
"India_100%_Viscose_30s_Hosiery_30s_USS___Kg" as "india_100%_viscose_30s_hosiery_30s_uss___kg",
"Pakistan_100%_Cotton_10s_Carded_Faisalabad_Rps_10lb" as "pakistan_100%_cotton_10s_carded_faisalabad_rps_10lb",
"Pakistan_100%_Cotton_0E_10s_Carded_Faisalabad_Rupees_10lb" as "pakistan_100%_cotton_0e_10s_carded_faisalabad_rupees_10lb",
"Pakistan_100%_Cotton_10s_Carded_Faisalabad_Rps__10lb" as "pakistan_100%_cotton_10s_carded_faisalabad_rps__10lb",
"Pakistan_100%_Cotton_20s_Carded_Faisalabad_Rps_10lb" as "pakistan_100%_cotton_20s_carded_faisalabad_rps_10lb",
"Pakistan_100%_Cotton_30s_Carded_Faisalabad_Rps_10lb" as "pakistan_100%_cotton_30s_carded_faisalabad_rps_10lb",
"Pakistan_100%_Cotton_40s_Combed_Karachi_Rps_10lb" as "pakistan_100%_cotton_40s_combed_karachi_rps_10lb",
"Pakistan_100%_Cotton_60s_Combed_Karachi_Rps_10lb" as "pakistan_100%_cotton_60s_combed_karachi_rps_10lb",
"Pakistan_100%_Cotton_0E_10s_Carded_Faisalabad_USS_Kg" as "pakistan_100%_cotton_0e_10s_carded_faisalabad_uss_kg",
"Pakistan_100%_Cotton_10s_Carded_Faisalabad_USS_Kg" as "pakistan_100%_cotton_10s_carded_faisalabad_uss_kg",
"Pakistan_100%_Cotton_20s_Carded_Faisalabad_USS_Kg" as "pakistan_100%_cotton_20s_carded_faisalabad_uss_kg",
"Pakistan_100%_Cotton_30s_Carded_Faisalabad_USS_Kg" as "pakistan_100%_cotton_30s_carded_faisalabad_uss_kg",
"Pakistan_100%_Cotton_40s_Combed_Karachi_USS_Kg" as "pakistan_100%_cotton_40s_combed_karachi_uss_kg",
"Pakistan_100%_Cotton_80s_Combed_Karachi_USS_Kg" as "pakistan_100%_cotton_80s_combed_karachi_uss_kg",
"Pakistan_52_48_PolyCotton_16s_Carded_Faisalabad_Rps_lb" as "pakistan_52_48_polycotton_16s_carded_faisalabad_rps_lb",
"Pakistan_52_48_PolyCotton_20s_Carded_Faisalabad_Rps_lb" as "pakistan_52_48_polycotton_20s_carded_faisalabad_rps_lb",
"Pakistan_52_48_PolyCotton_30s_Carded_Faisalabad_Rps_lb" as "pakistan_52_48_polycotton_30s_carded_faisalabad_rps_lb",
"Pakistan_52_48_PolyCotton_40s_Combed_Faisalabad_Rps_lb" as "pakistan_52_48_polycotton_40s_combed_faisalabad_rps_lb",
"Pakistan_52_48_PolyCotton_16s_Carded_Faisalabad_USS_Kg" as "pakistan_52_48_polycotton_16s_carded_faisalabad_uss_kg",
"Pakistan_52_48_PolyCotton_20s_Carded_Faisalabad_USS_Kg" as "pakistan_52_48_polycotton_20s_carded_faisalabad_uss_kg",
"Pakistan_52_48_PolyCotton_24s_Carded_Faisalabad_USS_Kg" as "pakistan_52_48_polycotton_24s_carded_faisalabad_uss_kg",
"Pakistan_52_48_PolyCotton_30s_Carded_Faisalabad_USS_Kg" as "pakistan_52_48_polycotton_30s_carded_faisalabad_uss_kg",
"Pakistan_52_48_PolyCotton_40s_Combed_Faisalabad_USS_Kg" as "pakistan_52_48_polycotton_40s_combed_faisalabad_uss_kg",
"Pakistan_100%_Polyester_20s_Carded_Karachi_Rupees_lb" as "pakistan_100%_polyester_20s_carded_karachi_rupees_lb",
"Pakistan_100%_Polyester_30s_Carded_Karachi_Rupees_lb" as "pakistan_100%_polyester_30s_carded_karachi_rupees_lb",
"Pakistan_100%_Polyester_20s_Carded_Karachi_USS_Kg" as "pakistan_100%_polyester_20s_carded_karachi_uss_kg",
"Pakistan_100%_Polyester_30s_Carded_Karachi_USS_Kg" as "pakistan_100%_polyester_30s_carded_karachi_uss_kg",
"Pakistan_80_20_Poly-Viscose_20s_Carded_Karachi_Rupees_lb" as "pakistan_80_20_poly-viscose_20s_carded_karachi_rupees_lb",
"Pakistan_80_20_Poly-Viscose_30s_Carded_Karachi_Rupees_lb" as "pakistan_80_20_poly-viscose_30s_carded_karachi_rupees_lb",
"Pakistan_80_20_Poly-Viscose_40s_Combed_Karachi_Rupees_lb" as "pakistan_80_20_poly-viscose_40s_combed_karachi_rupees_lb",
"Pakistan_80_20_Poly-Viscose_20s_Carded_Karachi_USS_Kg" as "pakistan_80_20_poly-viscose_20s_carded_karachi_uss_kg",
"Pakistan_80_20_Poly-Viscose_30s_Carded_Karachi_USS_Kg" as "pakistan_80_20_poly-viscose_30s_carded_karachi_uss_kg",
"Pakistan_80_20_Poly-Viscose_40s_Combed_Karachi_USS_Kg" as "pakistan_80_20_poly-viscose_40s_combed_karachi_uss_kg",
"Pakistan_Viscose_1.5D_VSF_Import_Rupees_Kg" as "pakistan_viscose_1.5d_vsf_import_rupees_kg",
"Pakistan_Viscose_1.5D_VSF_Import_USS_Kg" as "pakistan_viscose_1.5d_vsf_import_uss_kg",
"Pakistan_Viscose_1.5D_VSF_Import_US_Cents_lb" as "pakistan_viscose_1.5d_vsf_import_us_cents_lb",
"Pakistan_100%_Viscose_30s_Carded_Karachi_Rupees_lb" as "pakistan_100%_viscose_30s_carded_karachi_rupees_lb",
"Pakistan_100%_Viscose_35s_Carded_Karachi_Rupees_lb" as "pakistan_100%_viscose_35s_carded_karachi_rupees_lb",
"Pakistan_100%_Viscose_40s_Combed_Karachi_Rupees_lb" as "pakistan_100%_viscose_40s_combed_karachi_rupees_lb",
"Pakistan_100%_Viscose_30s_Carded_Karachi_USS_Kg" as "pakistan_100%_viscose_30s_carded_karachi_uss_kg",
"Pakistan_100%_Viscose_35s_Carded_Karachi_USS_Kg" as "pakistan_100%_viscose_35s_carded_karachi_uss_kg",
"Pakistan_100%_Viscose_40s_Combed_Karachi_USS_Kg" as "pakistan_100%_viscose_40s_combed_karachi_uss_kg",
"Pakistan_100%_Cotton_8s_Carded_USS_Kg" as "pakistan_100%_cotton_8s_carded_uss_kg",
"Pakistan_100%_Cotton_10s_Carded_USS_Kg" as "pakistan_100%_cotton_10s_carded_uss_kg",
"Pakistan_100%_Cotton_16s_Carded_USS_Kg" as "pakistan_100%_cotton_16s_carded_uss_kg",
"Pakistan_100%_Cotton_30s_Carded_USS_Kg" as "pakistan_100%_cotton_30s_carded_uss_kg",
"Pakistan_100%_Cotton_32s_Carded_USS_Kg" as "pakistan_100%_cotton_32s_carded_uss_kg",
"Pakistan_100%_Cotton_20s_Combed_USS_Kg" as "pakistan_100%_cotton_20s_combed_uss_kg",
"Pakistan_100%_Cotton_30s_Combed_USS_Kg" as "pakistan_100%_cotton_30s_combed_uss_kg",
"Pakistan_100%_Cotton_32s_Combed_USS_Kg" as "pakistan_100%_cotton_32s_combed_uss_kg",
"Pakistan_100%_Cotton_40s_Combed_USS_Kg" as "pakistan_100%_cotton_40s_combed_uss_kg",
"Bangladesh_Cotton_Combed_US_Cotton_30s_USS_Kg" as "bangladesh_cotton_combed_us_cotton_30s_uss_kg",
"Bangladesh_Cotton_Combed_US_Cotton_40s_USS_Kg" as "bangladesh_cotton_combed_us_cotton_40s_uss_kg",
"Bangladesh_Polyester-Cotton_Carded_52_48_30s_USS_Kg" as "bangladesh_polyester-cotton_carded_52_48_30s_uss_kg",
"Bangladesh_Polyester-Viscose_80_20_30s_USS_Kg" as "bangladesh_polyester-viscose_80_20_30s_uss_kg",
"Bangladesh_100%_Polyester_PSF_30s_USS_Kg" as "bangladesh_100%_polyester_psf_30s_uss_kg",
"Bangladesh_100%_Viscose_VSF_30s_USS_Kg" as "bangladesh_100%_viscose_vsf_30s_uss_kg",
to_date(file_year || '-' || file_month || '-' || file_day) AS yarns_extraction_date
FROM dedup
WHERE rn = 1












