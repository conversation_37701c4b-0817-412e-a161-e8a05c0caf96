name: SQL Drift Check & Deploy

# on:
#   pull_request:
#     branches:
#       - uat
#     paths:
#       - '**/*.sql'  # Only trigger if .sql files are modified

on:
  workflow_dispatch:

jobs:
  drift_check:
    name: Drift Detection on Snowflake DB
    runs-on:
      group: core

    env:
      SNOWFLAKE_USER: ${{ secrets.SNOWFLAKE_USER }}
      SNOWFLAKE_PASSWORD: ${{ secrets.SNOWFLAKE_PASSWORD }}
      SNOWFLAKE_ACCOUNT: ${{ secrets.SNOWFLAKE_ACCOUNT }}
      ROOT_SQL_PATH: ${{ github.workspace }}

    steps:
      - name: Checkout PR branch
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Conda Setup in Runner
        run: |
          export MAMBA_EXE="/jfs/tools/conda/exec/micromamba"
          export MAMBA_ROOT_PREFIX="/jfs/tech1/conda/"
          eval "$($MAMBA_EXE shell hook --shell bash --root-prefix $MAMBA_ROOT_PREFIX)"
          micromamba activate tech1-datait
          echo "Micromamba environment activated."

      - name: Run drift check
        run: |
          python source/automation-scripts/snowflake_ddl_diff.py > diff_output.txt
        continue-on-error: true

      - name: Fallback if no drift found
        run: |
          if [ ! -s diff_output.txt ]; then
            echo " No drift detected." > diff_output.txt
          fi

      - name: Upload Diff Report
        uses: actions/upload-artifact@v3
        with:
          name: drift-diff
          path: diff_output.txt

      - name: Comment drift report on PR
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: Drift Check Report
          path: diff_output.txt
