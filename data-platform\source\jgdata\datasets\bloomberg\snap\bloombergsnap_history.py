import pandas as pd

# from jgdata.datasets.bloomberg.snap_api.python.bloomberg.persecurity.py.bbg_302_nested_data_request import *
from datetime import datetime
from jgdata.datasets.bloomberg.snap_api.python.bloomberg.persecurity.py.bbg_309_history_request1 import *
# from jgdata.datasets.bloomberg.snap_api.python.bloomberg.persecurity.py.bbg_302_nested_data_request import *
from stcommon.infra.rds.snowflake_operation import *
from stcommon.infra.rds.snowflake_operation import *
import jglib.infra.python.fileio as fio
from stcommon.infra.python.snap_config_reader import *
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
config_root_temp = os.path.abspath(os.path.join(current_dir, '../../../../conf/datasets/vendors'))
config_path = config_root_temp + '/snap.config.toml'

def build_bsnap_hist_payload():
    ##TEST CASES
    '''
    USE ROLE FR_APACST
    INSERT INTO APACST_PROD_DB.APAC_MACRO.BLOOMBERG_HIST_CONFIG VALUES ('TEST RECORD','PX_LAST')
    INSERT INTO APACST_PROD_DB.APAC_MACRO.BLOOMBERG_HIST_CONFIG VALUES ('XLP US Equity','PX_LAST')
    DELETE FROM APACST_PROD_DB.APAC_MACRO.BLOOMBERG_HIST_CONFIG WHERE IDENTIFIER IN ('TEST RECORD', 'XLP US Equity')
    '''
    from datetime import datetime
    import time
    obj_sf = SnowflakeDML("APACST_PROD_DB")
    df = obj_sf.fetch_query(f"""
        SELECT    c.IDENTIFIER, c.ATTRIBUTE as field, COALESCE(DATE(max(df.REF_DATE))+1,'2004-01-01') sd
        FROM      BLOOMBERG.SNAP.BLOOMBERG_HIST_CONFIG c
        LEFT JOIN BLOOMBERG.SNAP.DATA_FEED df
        ON        c.IDENTIFIER = df.IDENTIFIER 
        AND       c.ATTRIBUTE  = df.ATTRIBUTE
        AND       df.REF_DATE < CURRENT_DATE() - 1
        WHERE     1=1
        --Esure history loaded is not already set
        AND  (c.identifier, c.attribute) not in (select IDENTIFIER, ATTRIBUTE  FROM BLOOMBERG.SNAP.BLOOMBERG_HIST_CONFIG_LOADED)
        GROUP BY  c.IDENTIFIER, c.ATTRIBUTE
    """)
    build_all(df, obj_sf)
  
def build_all(df, obj_sf): 
    from datetime import datetime, timedelta   
    print(f"Returned DF: {df}")
    if df is None or df.empty:
        print("Nothing to run")
        return
    #sd = "20040101"
    #sd = (datetime.strptime(sd, '%Y%m%d')).strftime('%Y-%m-%d')
    #ed = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    ed = (datetime.now() - timedelta(days=1)).date()
    # sd = "20040101"
    # ed = "20091231" 
    # sd = (datetime.strptime(sd, '%Y%m%d')).strftime('%Y-%m-%d')
    # ed = (datetime.strptime(ed, '%Y%m%d')).strftime('%Y-%m-%d')
    print(df.columns)
    for sd, group in df.groupby("SD"):
        sub_df = group[["IDENTIFIER", "FIELD"]].reset_index(drop=True)
        print(f"Start Date: {sd}")
        print(f"End Date: {ed}")
        if ed > sd:
            print("✅✅✅✅✅✅✅Will Run for this DF✅✅✅✅✅✅✅")
            print(sub_df)
            print("✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅✅")
            build_309(df, sd, ed, obj_sf)
        else:
            print("❌❌❌❌❌❌❌Will Not Run for this DF❌❌❌❌❌❌❌")
            print(sub_df)
            print("❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌")

    

def build_309(df_cusip_reference, sd, ed, obj_sf):
  if not df_cusip_reference.empty:
    tickers_list = []
    for index, row in df_cusip_reference.iterrows():
            tickers_list.append({'IDENTIFIER': row['IDENTIFIER'], 'FIELD': row['FIELD']})
                
    if not tickers_list: 
        df_empty = pd.DataFrame()
        return df_empty
    else:      
        df_qualified_tickers = pd.DataFrame(tickers_list)
        
    
    unique_config_types = df_qualified_tickers['FIELD'].unique()
    unique_config_types_lst = df_qualified_tickers['FIELD'].unique().tolist()
    df_309_api_response_final = pd.DataFrame()
    
    for config_type in unique_config_types:
        unique_config_values = df_qualified_tickers[df_qualified_tickers['FIELD'] == config_type]['IDENTIFIER'].unique()
        unique_config_values_lst = list(unique_config_values)
        
        print("309 API Call")
        config = fio.read_config_secrets()
        user_terminal_number = config['bbrg_user_terminal_number']
        response_df2 = call_main_309_for_ref_date_api(config_type,sd, ed, unique_config_values_lst, user_terminal_number)
        df_non_null_from_api_309 = response_df2.dropna(subset=[config_type])
        df_309_api_response_final = pd.concat([df_309_api_response_final, df_non_null_from_api_309], ignore_index=True)
        print(f"Response from 309 API, {df_non_null_from_api_309}")
    

    print(f"After Concat 309: {df_309_api_response_final}")
    print(df_309_api_response_final)
    
    # columns = ["DL_SNAPSHOT_START_TIME", "IDENTIFIER", "DATE","PX_LAST", "FUT_AGGTE_VOL", "ID_CUSIP"]
    columns = ["DL_SNAPSHOT_START_TIME", "IDENTIFIER", "DATE"] + unique_config_types_lst
    existing_columns = [col for col in columns if col in df_309_api_response_final.columns]
    df_309_api_response_final_filtered = pd.DataFrame(df_309_api_response_final[existing_columns])
    
    print("Melting the df_309_api_response_final_filtered")
    df_melted_309 = df_309_api_response_final_filtered.melt(id_vars=["DL_SNAPSHOT_START_TIME", "IDENTIFIER", "DATE"], 
                        var_name="Attribute", 
                        value_name="Value")

    print(df_melted_309)
    df_melted_309['date'] = ed
    df_melted_309['availDate'] = pd.Timestamp.utcnow()
    df_melted_309['ref_date'] = df_melted_309['DATE'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) else None)
    df_melted_309.rename(columns={'date': 'SNAP_DATE', 'IDENTIFIER': 'IDENTIFIER', 'Attribute': 'ATTRIBUTE', 'Value': 'VALUE', 'availDate': 'AVAILDATE', 'ref_date': 'REF_DATE'}, inplace=True)
    df_melted_309 = df_melted_309[['SNAP_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'AVAILDATE', 'REF_DATE']]                 
    df_melted_309.to_csv("testing_309_output.csv", index =False)
    df_melted_309 = df_melted_309.dropna(subset=['VALUE']).reset_index(drop=True)
    df_melted_309['VALUE_TRANSFORM'] = df_melted_309['VALUE'].apply(convert_to_float)
    df_melted_309['VALUE_TRANSFORM'] = df_melted_309['VALUE_TRANSFORM'].map('{:.6f}'.format)
    df_melted_309['VALUE_TRANSFORM'] = df_melted_309['VALUE_TRANSFORM'].astype('float64')
    
    print("Post dt conversion 309 dataframe")
    print(df_melted_309)
    
    latest_records = obj_sf.get_latest_record('BLOOMBERG.SNAP.DATA_FEED', 'AVAILDATE', ['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE'])
    
    existing_df = latest_records[['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE']]
    existing_df['REF_DATE'] = existing_df['REF_DATE'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) else None)
    existing_df['IDENTIFIER'] = existing_df['IDENTIFIER'].astype('object')
    existing_df['ATTRIBUTE'] = existing_df['ATTRIBUTE'].astype('object')
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE'].apply(convert_to_float)
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE_TRANSFORM'].map('{:.6f}'.format)
    existing_df['VALUE_TRANSFORM'] = existing_df['VALUE_TRANSFORM'].astype('float64')
    print("Post dt conversion existing dataframe")
    print(existing_df)
    
    if existing_df.empty:
        df_toBeLoaded = df_melted_309.copy()
    else:
        existing_df = existing_df.drop_duplicates(subset=['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'VALUE_TRANSFORM']).reset_index(drop=True)
        df_merged = df_melted_309.merge(existing_df, on=['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE'], how='left', indicator=True)
        tolerance = 0.0001
        print("DF Mergered 309")
        print(df_merged)
        df_merged['value_diff'] = df_merged['VALUE_TRANSFORM_x'] - df_merged['VALUE_TRANSFORM_y']
        df_toBeLoaded = df_merged[(df_merged['_merge'] == 'left_only') | ((df_merged['_merge'] == 'both') & (df_merged['value_diff'].abs() > tolerance))].drop(columns=['_merge', 'VALUE_y', 'value_diff'])
        df_toBeLoaded.rename(columns = {'VALUE_x':'VALUE'}, inplace=True)
    
    df_toBeLoaded = df_melted_309.copy()
    df_toBeLoaded = df_toBeLoaded.sort_values(by='REF_DATE').reset_index(drop=True)
    # df_toBeLoaded['AVAILDATE'] = df_toBeLoaded['AVAILDATE'].dt.strftime('%Y-%m-%d %H:%M:%S')
    df_toBeLoaded['REF_DATE'] = pd.to_datetime(df_toBeLoaded['REF_DATE'], errors='coerce')
    df_toBeLoaded['REF_DATE'].fillna(pd.Timestamp.now(), inplace=True)
    df_toBeLoaded['AVAILDATE'] = (df_toBeLoaded['REF_DATE'] + timedelta(days=1)).dt.strftime('%Y-%m-%d %H:%M:%S')

    df_toBeLoaded = df_toBeLoaded[['SNAP_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'AVAILDATE', 'REF_DATE']]
    
    for col in ['SNAP_DATE', 'AVAILDATE', 'REF_DATE']:
        if col in df_toBeLoaded.columns:
            df_toBeLoaded[col] = pd.to_datetime(df_toBeLoaded[col], errors='coerce').dt.strftime('%Y-%m-%d %H:%M:%S')

    print("To Be loaded for 309")
    print(df_toBeLoaded)
    # df_toBeLoaded.to_csv("309_sf_insertion.csv", index = False)
    if not df_toBeLoaded.empty:
        print("Data to be loaded from 309 API are")
        print(df_toBeLoaded)
        df_toBeLoaded.to_csv("ticker_hist", index=False)
        print("\nInserting data into snowflake table")
        # obj_sf.insert_dataframe(df_toBeLoaded, 'BLOOMBERG.SNAP.DATA_FEED')
        obj_sf.copy_df_to_table(df_toBeLoaded, 'BLOOMBERG.SNAP', 'BLOOMBERG_HIST', 'BLOOMBERG.SNAP.DATA_FEED')
        df_identifiers=df_toBeLoaded[['IDENTIFIER','ATTRIBUTE']].drop_duplicates()
        df_identifiers['LOAD_DATETIME'] = pd.Timestamp.utcnow().strftime('%Y-%m-%d %H:%M:%S')
        obj_sf.insert_dataframe(df_identifiers,'BLOOMBERG.SNAP.BLOOMBERG_HIST_CONFIG_LOADED')
        print("Data is inserted into snowflake")
    else:
        print("Nothing to insert into snowflake")
 
def convert_to_float(value):
    try:
        # Try converting directly to float
        return float(value)
    except ValueError:
        try:
            # Try converting to datetime and then to epoch
            return pd.to_datetime(value).timestamp()
        except Exception:
            return None 
        

