use role ACCOUNTADMIN;

-- Create schemas
USE DATABASE BLOOMBERG;

CREATE SCHEMA IF NOT EXISTS BBGH_FUTURES;
CREATE SCHEMA IF NOT EXISTS BBGH_OPTIONS;
CREATE SCHEMA IF NOT EXISTS BBGH_CUSTOM;
CREATE SCHEMA IF NOT EXISTS BBGH_SAMPLE;

-- Create warehouse
CREATE WAREHOUSE IF NOT EXISTS BLOOMBERG_HUB_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 300         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

-- Create roles
CREATE ROLE IF NOT EXISTS DR_BBGH_FUTURES_BASIC;
CREATE ROLE IF NOT EXISTS DR_BBGH_FUTURES_PREMIUM;
CREATE ROLE IF NOT EXISTS DR_BBGH_ETF_OPTIONS;
CREATE ROLE IF NOT EXISTS DR_BBGH_CUSTOM;
CREATE ROLE IF NOT EXISTS DR_BBGH_SAMPLE;
CREATE ROLE IF NOT EXISTS DR_BBGH_WRITER;
CREATE ROLE IF NOT EXISTS DR_BBGH_OWNER;
CREATE ROLE IF NOT EXISTS FR_BBGH_SUPPORT;


-- Grant usage on warehouse to all roles
GRANT USAGE ON WAREHOUSE BLOOMBERG_HUB_WH TO ROLE DR_BBGH_OWNER;
GRANT USAGE ON WAREHOUSE BLOOMBERG_HUB_WH TO ROLE FR_BBGH_SUPPORT;

-- Grant usage on database to all roles
GRANT USAGE ON DATABASE BLOOMBERG TO ROLE DR_BBGH_FUTURES_BASIC;
GRANT USAGE ON DATABASE BLOOMBERG TO ROLE DR_BBGH_FUTURES_PREMIUM;
GRANT USAGE ON DATABASE BLOOMBERG TO ROLE DR_BBGH_ETF_OPTIONS;
GRANT USAGE ON DATABASE BLOOMBERG TO ROLE DR_BBGH_CUSTOM;
GRANT USAGE ON DATABASE BLOOMBERG TO ROLE DR_BBGH_SAMPLE;
GRANT USAGE ON DATABASE BLOOMBERG TO ROLE DR_BBGH_OWNER;
GRANT USAGE ON DATABASE BLOOMBERG TO ROLE FR_BBGH_SUPPORT;


-- Grant schema-specific permissions
GRANT USAGE ON SCHEMA BLOOMBERG.BBGH_FUTURES TO ROLE DR_BBGH_FUTURES_BASIC;
GRANT USAGE ON SCHEMA BLOOMBERG.BBGH_FUTURES TO ROLE DR_BBGH_FUTURES_PREMIUM;
GRANT USAGE ON SCHEMA BLOOMBERG.BBGH_OPTIONS TO ROLE DR_BBGH_ETF_OPTIONS;
GRANT USAGE ON SCHEMA BLOOMBERG.BBGH_CUSTOM TO ROLE DR_BBGH_CUSTOM;
GRANT USAGE ON SCHEMA BLOOMBERG.BBGH_SAMPLE TO ROLE DR_BBGH_SAMPLE;

-- OWNER role (full access)
GRANT ALL PRIVILEGES ON SCHEMA BLOOMBERG.BBGH_FUTURES TO ROLE DR_BBGH_OWNER;
GRANT ALL PRIVILEGES ON SCHEMA BLOOMBERG.BBGH_OPTIONS TO ROLE DR_BBGH_OWNER;
GRANT ALL PRIVILEGES ON SCHEMA BLOOMBERG.BBGH_CUSTOM TO ROLE DR_BBGH_OWNER;
GRANT ALL PRIVILEGES ON SCHEMA BLOOMBERG.BBGH_SAMPLE TO ROLE DR_BBGH_OWNER;

-- Grant future grants to OWNER role
GRANT ALL PRIVILEGES ON FUTURE TABLES IN SCHEMA BLOOMBERG.BBGH_FUTURES TO ROLE DR_BBGH_OWNER;
GRANT ALL PRIVILEGES ON FUTURE TABLES IN SCHEMA BLOOMBERG.BBGH_OPTIONS TO ROLE DR_BBGH_OWNER;
GRANT ALL PRIVILEGES ON FUTURE TABLES IN SCHEMA BLOOMBERG.BBGH_CUSTOM TO ROLE DR_BBGH_OWNER;
GRANT ALL PRIVILEGES ON FUTURE TABLES IN SCHEMA BLOOMBERG.BBGH_SAMPLE TO ROLE DR_BBGH_OWNER;

GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_FUTURES TO ROLE DR_BBGH_WRITER;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_OPTIONS TO ROLE DR_BBGH_WRITER;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_CUSTOM TO ROLE DR_BBGH_WRITER;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_SAMPLE TO ROLE DR_BBGH_WRITER;

GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_FUTURES TO ROLE DR_BBGH_OWNER;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_OPTIONS TO ROLE DR_BBGH_OWNER;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_CUSTOM TO ROLE DR_BBGH_OWNER;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_SAMPLE TO ROLE DR_BBGH_OWNER;

GRANT ROLE DR_BBGH_FUTURES_BASIC TO ROLE FR_BBGH_SUPPORT;
GRANT ROLE DR_BBGH_FUTURES_PREMIUM TO ROLE FR_BBGH_SUPPORT;
GRANT ROLE DR_BBGH_ETF_OPTIONS TO ROLE FR_BBGH_SUPPORT;
GRANT ROLE DR_BBGH_CUSTOM TO ROLE FR_BBGH_SUPPORT;
GRANT ROLE DR_BBGH_SAMPLE TO ROLE FR_BBGH_SUPPORT;
GRANT ROLE DR_BBGH_WRITER TO ROLE FR_BBGH_SUPPORT;

CREATE USER SVC_BBGH DISPLAY_NAME = 'SVC_BBGH'
GRANT ROLE FR_BBGH_SUPPORT TO USER SVC_BBGH;
ALTER USER SVC_BBGH SET DEFAULT_ROLE = FR_BBGH_SUPPORT
ALTER USER SVC_BBGH SET RSA_PUBLIC_KEY='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnABp4wEEGfqqOFQ2Uaxm
N90yqKphL9T0v0KaWJDjbzFKdaRpn7f7tb7N42dkzFrSINWHvgHCUBf0aJNamsWJ
tmToCUBIYxdVF9oziw7ZYb45pVtmGoiyMm817u+6Xog46gSvBhOYQeIglN8YSFfP
DRtkGLm4SenSWoWARmZyFQe2/aroekostw8uzqt6ayYfnsuoYQwvstyyZpkxBvWO
y0fpTIwcA2sagKvpTQEw2dYYTARb6j0OOQ4HYAH/zQMU7lH4Xw4znas0cDkCT1GG
rXF/x+Y9fP+dNpua4fnevAST0sPMPjziIDQAJgEXOWIkKxhiRRqNchPhicKKU4M2
oQIDAQAB';

GRANT ROLE DR_BBGH_OWNER TO USER PULKITVORA;
GRANT ROLE DR_BBGH_OWNER TO USER RONBENCHETRIT;
GRANT ROLE DR_BBGH_OWNER TO USER MANISHKUMAR;

GRANT SELECT ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_FUTURES TO ROLE DR_BBGH_FUTURES_PREMIUM;
GRANT SELECT ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_FUTURES TO ROLE DR_BBGH_FUTURES_BASIC;
GRANT SELECT ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_OPTIONS TO ROLE DR_BBGH_ETF_OPTIONS;
GRANT SELECT ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_CUSTOM TO ROLE DR_BBGH_CUSTOM;

GRANT SELECT ON ALL TABLES IN SCHEMA BLOOMBERG.BBGH_SAMPLE TO ROLE DR_BBGH_SAMPLE;
GRANT SELECT ON FUTURE TABLES IN SCHEMA BLOOMBERG.BBGH_SAMPLE TO ROLE DR_BBGH_SAMPLE;

GRANT ROLE DR_BBGH_FUTURES_BASIC TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_BBGH_FUTURES_PREMIUM TO ROLE FR_DATA_PLATFORM;

GRANT SELECT ON TABLE BBGH_ONDEMAND.BBG_MACRO_EVENT_DATES TO ROLE DR_BBGH_CUSTOM;
