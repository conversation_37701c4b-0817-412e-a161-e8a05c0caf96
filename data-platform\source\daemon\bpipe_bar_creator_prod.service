[Unit]
Description=Bpipe Bar Creator Realtime Service
After=network.target
Requires=cwiqfs.service

[Service]
Type=simple
ExecStart=/bin/bash -c ' \
export MAMBA_EXE="/jfs/tools/conda/exec/micromamba"; \
export MAMBA_ROOT_PREFIX="/jfs/tech1/conda/"; \
__mamba_setup="$("$MAMBA_EXE" shell hook --shell bash --root-prefix "$MAMBA_ROOT_PREFIX" 2> /dev/null)"; \
eval "$__mamba_setup"; \
unset __mamba_setup; \
micromamba activate tech1-datait; \
python /jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/bin/bpipe-bar-creater_msk_prod.py bpipe --instance_id prod>> /opt/data/process_logs/bpipe_bar_creator_$(hostname)_$(date +%%Y%%m%%d)_prod.log 2>&1'
ExecStop=/bin/bash -c ' \
    pkill -f /jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/bin/bpipe-bar-creater_msk_prod.py'
Restart=on-failure
 
[Install]
WantedBy=multi-user.target