import requests
import zipfile
import yaml
from io import BytesIO
from jgdata import jgdata
import csv

def get_config_for_source(source):
    ymml_file = f'{jgdata()}/conf/sources/agri_config.yaml'
    with open(ymml_file, 'r') as file:
        config_file = yaml.safe_load(file)
    config_file=config_file["Sources"][source]
    return config_file

def get_response(url):    

    res = requests.get(url)
    if res.status_code==404:
        return []

    config=get_config_for_source("usda_psd")

    files=url.split("/")[-1][:-8]
    with zipfile.ZipFile(BytesIO(res.content)) as z:
        z.extractall(config['rawdata_location'])

    
    with open(f"{config['rawdata_location']}/{files}.csv",mode='r',newline='',encoding='utf-8') as file:
        reader=csv.DictReader(file)
        rows=list(reader)
    
    return rows



    
