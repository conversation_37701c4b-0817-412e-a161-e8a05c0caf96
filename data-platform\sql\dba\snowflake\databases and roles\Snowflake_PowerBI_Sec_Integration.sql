USE ROLE ACCOUNTADMIN;

CREATE SECURITY INTEGRATION POWERBI_AZURE_SSO
    TYPE = EXTERNAL_OAUTH
    ENABLED = TRUE
    EXTERNAL_OAUTH_TYPE = AZURE
    EXTERNAL_OAUTH_ISSUER = 'https://sts.windows.net/ec302311-a76e-4d47-bdb2-f537e3ae027e/'
    EXTERNAL_OAUTH_JWS_KEYS_URL = 'https://login.windows.net/common/discovery/keys'
    EXTERNAL_OAUTH_AUDIENCE_LIST = ('https://analysis.windows.net/powerbi/connector/Snowflake', 'https://analysis.windows.net/powerbi/connector/snowflake')
    EXTERNAL_OAUTH_TOKEN_USER_MAPPING_CLAIM = 'upn'
    EXTERNAL_OAUTH_SNOWFLAKE_USER_MAPPING_ATTRIBUTE = 'login_name'    
    EXTERNAL_OAUTH_ANY_ROLE_MODE = ENABLE;

--Describe the Security Integration, as needed.
DESCRIBE INTEGRATION POWERBI_AZURE_SSO;