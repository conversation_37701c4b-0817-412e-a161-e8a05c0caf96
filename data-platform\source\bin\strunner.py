import sys, os, pathlib, argparse

# ST_PROJECT = 'JGDA<PERSON>_PATH'
JGDATA_PATH = 'JGDATA_PATH'
STDATA_PATH = 'STDATA_PATH'
STCOMMON_PATH = 'STCOMMON_PATH'
CONFIG_PATH = 'CONFIG_PATH'
def setupEnvironment():
  print('Setup Systematic Trading Environment')

  try: # export the path to the project
    path = os.path.dirname(os.path.realpath(__file__))
    path = path.split('/bin')[0]
    # os.environ[ST_PROJECT] = path
    os.environ[JGDATA_PATH] = path
    os.environ[STDATA_PATH] = path  # load stdata_path from same path
    os.environ[STCOMMON_PATH] = path  # load stcommon library from same path
    os.environ[CONFIG_PATH] = path  
    os.environ['DATA_PIPELINE_PATH'] = path+'/../data_pipelines/jg_data_pipelines'
    sys.path.append(path)
    sys.path.append(os.path.join(pathlib.Path(path).parent, 'stcommon'))

  except: # running locally
    sys.path.append(os.environ.get(ST_PROJECT))
