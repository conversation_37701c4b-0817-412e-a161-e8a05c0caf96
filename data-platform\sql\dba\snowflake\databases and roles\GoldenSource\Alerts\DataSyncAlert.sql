--Create alert
CREATE OR REPLACE ALERT GOLDENSOURCE.METADATA.GOLDENSOURCE_OMNI_DATA_SYNC_ALERT
WAREHOUSE = 'GS_DAILY_LOAD_WH'
SCHEDULE = 'USING CRON 0 * * * * UTC'
IF(
  EXISTS(
    select
      run_id,
      syn.value:"schema"::varchar schema_name,
      syn.value:"table"::varchar table_name,
      syn.value:"ods_count"::integer ods_count,
      syn.value:"warehouse_count"::integer warehouse_count,
      syn.value:"mismatches"::integer data_mismatches,
      ods_count - warehouse_count as count_mismatches,
      syn.value:"error"::varchar error
    from(
      Select
        *,
        row_number() over(partition by null order by sync_start_time desc) RNK
      from metadata.data_sync_statistics
      where(
        count_mismatch = true
        or data_mismatch = true
        or technical_failures = True
      )
      and status in ('Completed','Failed')
    ),
    lateral flatten(input=>sync_details,mode=>'object') syn
    WHERE RNK=1
    and (data_mismatches > 0 or abs(count_mismatches) > 0)
    and sync_start_time BETWEEN snowflake.alert.last_successful_scheduled_time() AND snowflake.alert.scheduled_time()
  )
)
THEN
  BEGIN
    call system$send_snowflake_notification (snowflake.notification.application_json(
      SELECT TO_JSON(object_construct('type','omni_datasync_alerts','payload',ARRAY_AGG(object_construct(*)) WITHIN GROUP (ORDER BY SCHEMA_NAME,TABLE_NAME))) as data FROM
      TABLE(RESULT_SCAN(SNOWFLAKE.ALERT.GET_CONDITION_QUERY_UUID()))
    ),snowflake.notification.integration('OMNI_PROD_NOTIFICATION_INTEGRATION') );
  END;

--Resume the alert
ALTER ALERT GOLDENSOURCE.METADATA.GOLDENSOURCE_OMNI_DATA_SYNC_ALERT RESUME;
