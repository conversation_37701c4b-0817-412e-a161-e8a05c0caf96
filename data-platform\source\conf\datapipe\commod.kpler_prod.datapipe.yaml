raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/kpler"
  s3_bucket: "jg-data-dp-vendor-data"
  s3_prefix: "usda-agri"
  include_prefix: true
  
  structure: '[
   "liquids_snap_**_DAILY_prod_$DATE$**.csv"
  ]'


snowflake:
  db_name: "COMMOD"
  schema_name: "KPLER_TRADES"

  table_map:
    
    kpler_updates:
      pattern: "^liquids_snap_.*_DAILY_prod_$DATE$.*.csv" 
      col_num: 6
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 
