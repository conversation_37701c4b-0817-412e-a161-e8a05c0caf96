
--- Permission for DB : ARC

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE ARC TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE ARC TO ROLE FR_DATACATALOG_ALATION;
<PERSON>RA<PERSON> USAGE ON FUTURE SCHEMAS IN DATABASE ARC TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE ARC TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE ARC TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON <PERSON><PERSON> VIEWS IN DATABASE ARC TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE ARC TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE ARC TO ROLE FR_DATAC<PERSON>ALOG_ALATION;
GRA<PERSON> USAGE ON ALL STAGES IN DATABASE ARC TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE ARC TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE ARC TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : ARC

--- Permission for DB : BBG_DLPLUS

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE BBG_DLPLUS TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE BBG_DLPLUS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE BBG_DLPLUS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE BBG_DLPLUS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE BBG_DLPLUS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE BBG_DLPLUS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE BBG_DLPLUS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE BBG_DLPLUS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE BBG_DLPLUS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE BBG_DLPLUS TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE BBG_DLPLUS TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : BBG_DLPLUS

--- Permission for DB : BLOOMBERG

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE BLOOMBERG TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE BLOOMBERG TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE BLOOMBERG TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE BLOOMBERG TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE BLOOMBERG TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE BLOOMBERG TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE BLOOMBERG TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE BLOOMBERG TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE BLOOMBERG TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE BLOOMBERG TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE BLOOMBERG TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : BLOOMBERG

--- Permission for DB : BROKER_QUOTES

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE BROKER_QUOTES TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE BROKER_QUOTES TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE BROKER_QUOTES TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE BROKER_QUOTES TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE BROKER_QUOTES TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE BROKER_QUOTES TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE BROKER_QUOTES TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE BROKER_QUOTES TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE BROKER_QUOTES TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE BROKER_QUOTES TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE BROKER_QUOTES TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : BROKER_QUOTES

--- Permission for DB : CITI

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE CITI TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE CITI TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE CITI TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE CITI TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE CITI TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE CITI TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE CITI TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE CITI TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE CITI TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE CITI TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE CITI TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : CITI

--- Permission for DB : CME

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE CME TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE CME TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE CME TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE CME TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE CME TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE CME TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE CME TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE CME TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE CME TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE CME TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE CME TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : CME

--- Permission for DB : COMMOD

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE COMMOD TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE COMMOD TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE COMMOD TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE COMMOD TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE COMMOD TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE COMMOD TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE COMMOD TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE COMMOD TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE COMMOD TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE COMMOD TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE COMMOD TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : COMMOD

--- Permission for DB : COMMON

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE COMMON TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE COMMON TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE COMMON TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE COMMON TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE COMMON TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE COMMON TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE COMMON TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE COMMON TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE COMMON TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE COMMON TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE COMMON TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : COMMON

--- Permission for DB : COPPCLARK

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE COPPCLARK TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE COPPCLARK TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE COPPCLARK TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE COPPCLARK TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE COPPCLARK TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE COPPCLARK TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE COPPCLARK TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE COPPCLARK TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE COPPCLARK TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE COPPCLARK TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE COPPCLARK TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : COPPCLARK

--- Permission for DB : DBT_HOL_PROD

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE DBT_HOL_PROD TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE DBT_HOL_PROD TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE DBT_HOL_PROD TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE DBT_HOL_PROD TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE DBT_HOL_PROD TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE DBT_HOL_PROD TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE DBT_HOL_PROD TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE DBT_HOL_PROD TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE DBT_HOL_PROD TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE DBT_HOL_PROD TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE DBT_HOL_PROD TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : DBT_HOL_PROD

--- Permission for DB : EEX

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE EEX TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE EEX TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE EEX TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE EEX TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE EEX TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE EEX TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE EEX TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE EEX TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE EEX TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE EEX TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE EEX TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : EEX

--- Permission for DB : EVENTS_DATABASE

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE EVENTS_DATABASE TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE EVENTS_DATABASE TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE EVENTS_DATABASE TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE EVENTS_DATABASE TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE EVENTS_DATABASE TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE EVENTS_DATABASE TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE EVENTS_DATABASE TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE EVENTS_DATABASE TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE EVENTS_DATABASE TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE EVENTS_DATABASE TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE EVENTS_DATABASE TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : EVENTS_DATABASE

--- Permission for DB : EXT_FACTSET

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE EXT_FACTSET TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE EXT_FACTSET TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE EXT_FACTSET TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE EXT_FACTSET TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE EXT_FACTSET TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE EXT_FACTSET TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE EXT_FACTSET TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE EXT_FACTSET TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE EXT_FACTSET TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE EXT_FACTSET TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE EXT_FACTSET TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : EXT_FACTSET

--- Permission for DB : JG_SNP

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE JG_SNP TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE JG_SNP TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE JG_SNP TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE JG_SNP TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE JG_SNP TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE JG_SNP TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE JG_SNP TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE JG_SNP TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE JG_SNP TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE JG_SNP TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE JG_SNP TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : JG_SNP

--- Permission for DB : KPLER

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE KPLER TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE KPLER TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE KPLER TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE KPLER TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE KPLER TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE KPLER TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE KPLER TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE KPLER TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE KPLER TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE KPLER TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE KPLER TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : KPLER

--- Permission for DB : METEOLOGICA

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE METEOLOGICA TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE METEOLOGICA TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE METEOLOGICA TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE METEOLOGICA TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE METEOLOGICA TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE METEOLOGICA TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE METEOLOGICA TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE METEOLOGICA TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE METEOLOGICA TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE METEOLOGICA TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE METEOLOGICA TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : METEOLOGICA

--- Permission for DB : MSCI

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE MSCI TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE MSCI TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE MSCI TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE MSCI TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE MSCI TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE MSCI TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE MSCI TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE MSCI TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE MSCI TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE MSCI TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE MSCI TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : MSCI

--- Permission for DB : SECURITY_MASTER

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SECURITY_MASTER TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SECURITY_MASTER TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SECURITY_MASTER TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SECURITY_MASTER TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SECURITY_MASTER TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SECURITY_MASTER TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SECURITY_MASTER TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SECURITY_MASTER TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SECURITY_MASTER TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SECURITY_MASTER TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SECURITY_MASTER TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SECURITY_MASTER

--- Permission for DB : SPACINSIDER

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPACINSIDER TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPACINSIDER TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPACINSIDER TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPACINSIDER TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPACINSIDER TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPACINSIDER TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPACINSIDER TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPACINSIDER TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPACINSIDER TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPACINSIDER TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPACINSIDER TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPACINSIDER
