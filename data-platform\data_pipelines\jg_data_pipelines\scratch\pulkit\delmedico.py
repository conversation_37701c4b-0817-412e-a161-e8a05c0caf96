import logging
import json
import time
import pytz
import requests
from datetime import datetime
import pandas as pd
from utils.snowflake.adaptor import SnowflakeAdaptor

logging.basicConfig(level=logging.ERROR)
logger = logging.getLogger(__name__)


def _get_sample_future_tickers(n=1):
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="DELMEDICO_WH", role="FR_DELMEDICO"
    )
    df_futures = sf_adaptor.read_data(schema="BBGH_FUTURES", query=f"select BBG_FULL_TICKER, FUT_CONTRACT_DT from BLOOMBERG.BBGH_FUTURES.VW_FUTURE_REF WHERE FUTURE_ROOT IN ('TY', 'FV', 'TU', 'UXY', 'US', 'WN', 'FF', 'SER', 'SFR') and FUT_CONTRACT_DT >= '2025-04-01' ORDER BY FUT_CONTRACT_DT")
    tickers = df_futures["BBG_FULL_TICKER"].tolist()
    tickers_n = tickers[:n]
    logger.info(f"Number of Tickers: {len(tickers_n)}")
    return tickers_n

def _normalize_dates(date_str: str, take_ceiling: bool = False, timezone: str = "UTC"):
    date_format = "%Y-%m-%d"
    datetime_format = "%Y-%m-%d %H:%M:%S"

    try:
        local_tz = pytz.timezone(timezone)
    except pytz.UnknownTimeZoneError:
        raise ValueError(f"Unknown timezone: {timezone}")

    try:
        normalized_date = datetime.strptime(date_str, datetime_format)
        has_time = True
    except ValueError:
        normalized_date = datetime.strptime(date_str, date_format)
        has_time = False
    
    if not has_time:
        if take_ceiling:
            normalized_date = normalized_date.replace(hour=23, minute=59, second=59)
        else:
            normalized_date = normalized_date.replace(hour=0, minute=0, second=0)

    normalized_date_tz = local_tz.localize(normalized_date)
    date_utc = normalized_date_tz.astimezone(pytz.utc)

    return date_utc.replace(tzinfo=None)


def _list_to_sql_in_clause(values):
    return ", ".join(f"'{value}'" for value in values)


def get_bbg_intraday_bars_for_range(from_date, to_date, bbg_tickers, timezone: str = "UTC"):
    if not isinstance(bbg_tickers, list):
        bbg_tickers = [bbg_tickers]

    if not bbg_tickers:
        raise ValueError("No Bloomberg tickers provided")

    bbg_tickers_str = _list_to_sql_in_clause(bbg_tickers)

    logger.info(f"Fetching Bloomberg intraday bars for tickers: {bbg_tickers_str}")
    logger.info(
        f"Dates Requested From: {from_date}, To: {to_date}, Timezone: {timezone}"
    )

    from_date_str = _normalize_dates(from_date, timezone=timezone).strftime(
        "%Y-%m-%d %H:%M:%S"
    )
    to_date_str = _normalize_dates(
        to_date, take_ceiling=True, timezone=timezone
    ).strftime("%Y-%m-%d %H:%M:%S")

    logger.info(
        f"Normalized DatesFrom: {from_date_str}, To: {to_date_str}, Timezone: {timezone}"
    )

    query_bid = f"""
    select * 
    from VW_FUTURE_1M_BAR_BID
    WHERE BBG_FULL_TICKER IN ({bbg_tickers_str})
    and TH_BAR_TIME >= '{from_date_str}'
    and TH_BAR_TIME <= '{to_date_str}'
    """

    query_ask = f"""
    select * 
    from VW_FUTURE_1M_BAR_ASK
    WHERE BBG_FULL_TICKER IN ({bbg_tickers_str})
    and TH_BAR_TIME >= '{from_date_str}'
    and TH_BAR_TIME <= '{to_date_str}'
    """

    logger.info(f"Query Bid: {query_bid}")
    logger.info(f"Query Ask: {query_ask}")

    return _fetch_1mb_from_snowflake(query_bid, query_ask, timezone)

def get_bbg_intraday_bars_for_timestamps(timestamps, bbg_tickers, timezone: str = "UTC"):
    if not isinstance(bbg_tickers, list):
        bbg_tickers = [bbg_tickers]

    if not bbg_tickers:
        raise ValueError("No Bloomberg tickers provided")

    bbg_tickers_str = _list_to_sql_in_clause(bbg_tickers)
    
    normalized_timestamps = [_normalize_dates(ts, timezone=timezone).strftime("%Y-%m-%d %H:%M:%S") for ts in timestamps]
    norm_timestamps_str = _list_to_sql_in_clause(normalized_timestamps)
    
    logger.info(f"Fetching Bloomberg intraday bars for tickers: {bbg_tickers_str}, Timestamps Requested {norm_timestamps_str}, Timezone: {timezone}")
    
    query_bid = f"""
    select * 
    from VW_FUTURE_1M_BAR_BID
    WHERE BBG_FULL_TICKER IN ({bbg_tickers_str})
    and TH_BAR_TIME IN ({norm_timestamps_str})
    
    """

    query_ask = f"""
    select * 
    from VW_FUTURE_1M_BAR_ASK
    WHERE BBG_FULL_TICKER IN ({bbg_tickers_str})
    and TH_BAR_TIME IN ({norm_timestamps_str})
    """

    logger.info(f"Query Bid: {query_bid}")
    logger.info(f"Query Ask: {query_ask}")

    return _fetch_1mb_from_snowflake(query_bid, query_ask, timezone)

def _fetch_1mb_from_snowflake(query_bid, query_ask, timezone):
    start_time = time.time()
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="DELMEDICO_WH", role="FR_DELMEDICO"
    )
    df_1mb_ask = sf_adaptor.read_data(schema="BBGH_FUTURES", query=query_ask)
    df_1mb_bid = sf_adaptor.read_data(schema="BBGH_FUTURES", query=query_bid)
    end_time = time.time()
    execution_time = end_time - start_time
    logger.info(f"Time taken to fetch data: {execution_time:.4f} secs")

    if timezone != "UTC":
        start_time = time.time()
        timestamp_cols = [
            "TH_BAR_TIME",
            "TH_BAR_OPEN_TIME",
            "TH_BAR_CLOSE_TIME",
            "TH_BAR_LOW_TIME",
            "TH_BAR_HIGH_TIME",
            "TH_BAR_SNAP_TIME",
        ]
        for col in timestamp_cols:
            
            if col == "TH_BAR_TIME":
                df_1mb_ask["TH_BAR_TIME_UTC"] = df_1mb_ask["TH_BAR_TIME"]
                df_1mb_bid["TH_BAR_TIME_UTC"] = df_1mb_bid["TH_BAR_TIME"]
                
            df_1mb_ask[col] = (
                df_1mb_ask[col]
                .dt.tz_localize("UTC")
                .dt.tz_convert(timezone)
                .dt.tz_localize(None)
            )

            df_1mb_bid[col] = (
                df_1mb_bid[col]
                .dt.tz_localize("UTC")
                .dt.tz_convert(timezone)
                .dt.tz_localize(None)
            )
        end_time = time.time()
        execution_time = end_time - start_time
        logger.info(f"Time taken for timezone conversions: {execution_time:.4f} secs")
    else:
        logger.info("No timezone conversion required")

    return df_1mb_bid, df_1mb_ask


if __name__ == "__main__":
    # future_tickers = _get_sample_future_tickers(n=10)

    # timestamps = ['2024-12-03 11:00:00','2024-12-06 14:58:00','2024-12-02 09:00:00','2024-12-05 13:01:00','2024-12-11 16:00:00','2024-12-12 10:59:00','2024-12-09 15:00:00','2024-12-04 12:01:00','2024-12-10 11:00:00','2024-12-13 17:59:00','2024-12-02 14:00:00','2024-12-03 09:07:00','2024-12-06 13:00:00','2024-12-12 15:54:00','2024-12-04 10:00:00']

    df_timestamps = pd.read_csv("/jfs/tech1_share/pulkit.vora/delmedico_timestamps_may5/timestamps.csv")
    timestamps = df_timestamps["timestamps"].unique()
    # future_tickers = ['TUM25 Comdty']
    future_tickers = ['TUM25 Comdty','TUU25 Comdty','FVM25 Comdty','FVU25 Comdty','TYM25 Comdty','TYU25 Comdty','UXYM25 Comdty','UXYU25 Comdty','USM25 Comdty','USU25 Comdty','WNM25 Comdty','WNU25 Comdty','SFRM25 Comdty','SFRU25 Comdty','SFRZ25 Comdty','SFRH26 Comdty','SFRM26 Comdty','SFRU26 Comdty','SFRZ26 Comdty','SFRH27 Comdty','SFRM27 Comdty','SFRU27 Comdty','SFRZ27 Comdty','SFRH28 Comdty','SFRM28 Comdty','SFRU28 Comdty','SFRZ28 Comdty','SFRH29 Comdty','SFRM29 Comdty','SFRU29 Comdty','SFRZ29 Comdty','SFRH30 Comdty','SFRM30 Comdty']
    # future_tickers = ['TUM25 Comdty','TUU25 Comdty','FVM25 Comdty']
    # print(timestamps)

    exclude_days =  {"12/31/24"}
    converted = [
    datetime.strptime(d, "%m/%d/%y %H:%M").strftime("%Y-%m-%d %H:%M:%S")
    for d in timestamps
    if d.split()[0] not in exclude_days
    ]

    # converted = [
    # datetime.strptime(d, "%m/%d/%y %H:%M").strftime("%Y-%m-%d %H:%M:%S")
    # for d in timestamps
    # ]
    (df_1mb_bid, df_1mb_ask) = get_bbg_intraday_bars_for_timestamps(converted, future_tickers, timezone="America/Chicago")
    print(df_1mb_bid.shape[0], df_1mb_ask.shape[0])
    # print(converted)
    # counts = []
    # for future in future_tickers:
    #     (df_1mb_bid, df_1mb_ask) = get_bbg_intraday_bars_for_timestamps(converted, [future], timezone="America/Chicago")
    #     counts.append((future, df_1mb_bid.shape[0], df_1mb_ask.shape[0]))
    #     # df_1mb_ask.set_index("TH_BAR_TIME", inplace=True)
    #     # df_1mb_bid.set_index("TH_BAR_TIME", inplace=True)
    #     # df = pd.merge(df_1mb_ask, df_1mb_bid, how="outer")
    #     # print(df)
    # df = pd.DataFrame(counts, columns=["ticker", "bid", "ask"])
    # df.to_csv("delmedico_stats.csv")