{{ config(
    materialized='incremental',
    unique_key=['trade_date','contract', 'strip', 'contract_type', 'strike'],
    tags=['ice', 'options','flash'],
    cluster_by=['trade_date']
) }}

WITH staged AS (
    SELECT
        trade_date,
        hub,
        product,
        strip,
        contract,
        contract_type,
        CAST(strike AS FLOAT) as strike,
        settlement_price,
        net_change,
        expiration_date,
        product_id,
        CAST(option_volatility AS FLOAT) as option_volatility,
        delta_factor,
        filename,
        start_scan_time
    FROM {{ ref('stg_flash_options') }}
    {% if is_incremental() %}
    WHERE start_scan_time > (SELECT COALESCE(MAX(start_scan_time),'1900-01-01') FROM {{ this }} )
    {% endif %}
    qualify ROW_NUMBER() OVER (PARTITION BY trade_date, contract, strip, contract_type, strike ORDER BY start_scan_time DESC) =1 
)

SELECT
        trade_date,
        hub,
        product,
        strip,
        contract,
        contract_type,
        strike,
        settlement_price,
        net_change,
        expiration_date,
        product_id,
        option_volatility,
        delta_factor,
        filename,
        start_scan_time,
FROM staged
