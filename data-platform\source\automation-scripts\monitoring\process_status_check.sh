#!/bin/bash

OUTPUT_FILE="/etc/node_exporter/server_process_status.prom"

# Check if the current time matches the cron expression
function cron_matches_now() {
    local cron_expr="$1"
    result=$(python3 -c "
from croniter import croniter, CroniterBadCronError
from datetime import datetime, timedelta
import sys

try:
    expr = sys.argv[1]
    now = datetime.now()

    cron = croniter(expr, now)
    cron_time = cron.get_next(datetime)
    if now.second > 0:
        now += timedelta(minutes=1)
    date_time = now.replace(second=0, microsecond=0)

    if date_time == cron_time:
        print('yes')
    else:
        print('no')

except CroniterBadCronError:
    print('invalid')
" "$cron_expr")
    echo "$result"
}

# Function to read the YAML config file
function read_config() {
    local file="$1"
    mapfile -t servers < <(yq eval -r '.server_ip[]' "$file")
}

CONFIG_FILE="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/conf/connectors_services_process.yaml"
SERVER_CONFIG_FILE="/config/config.yaml"

read_config "$SERVER_CONFIG_FILE"

# Verify servers loaded
if [[ ${#servers[@]} -eq 0 ]]; then
    echo "❌ ERROR: No servers found in configuration. Exiting."
    exit 1
fi

config_data=$(yq eval '.process' "$CONFIG_FILE")

while true; do
    echo "=== $(date): Starting process status check ==="
    TEMP_FILE="/tmp/server_process_status.prom.$$"

    {
        echo "# HELP process_status Process status (1=up, 0=down)"
        echo "# TYPE process_status gauge"
        echo "# HELP node_textfile_mtime_seconds Timestamp of last update"
        echo "# TYPE node_textfile_mtime_seconds gauge"
    } > "$TEMP_FILE"

    process_count=$(echo "$config_data" | yq eval 'length' -)

    for ((i = 0; i < process_count; i++)); do
        process=$(echo "$config_data" | yq eval ".[$i].process_name" -)
        cron_expr=$(echo "$config_data" | yq eval ".[$i].cron_expression" -)
        server=$(echo "$config_data" | yq eval ".[$i].process_server" -)

        if [[ -z "$cron_expr" || "$cron_expr" == "null" ]]; then
            echo "⚠️  Skipping $process: missing cron_expression"
            continue
        fi

        match_result=$(cron_matches_now "$cron_expr")
        echo "🕒 Now: $(date '+%Y-%m-%d %H:%M:%S'), Cron: $cron_expr → Match: $match_result"

        if [[ "$match_result" != "yes" ]]; then
            echo "⏩ Skipping $process due to cron schedule mismatch"
            continue
        fi

        IFS=',' read -ra server_list <<< "$server"
        for server in "${server_list[@]}"; do
            if ssh -i ~/.ssh/id_rsa \
                -o StrictHostKeyChecking=no \
                -o UserKnownHostsFile=/dev/null \
                -o LogLevel=ERROR \
                "$server" "ps aux | grep -v grep | grep $process" > /dev/null; then
                echo "✅ Process $process is running on $server"
                echo "process_status{server=\"$server\", process=\"$process\"} 1" >> "$TEMP_FILE"
            else
                echo "❌ Process $process is NOT running on $server"
                echo "process_status{server=\"$server\", process=\"$process\"} 0" >> "$TEMP_FILE"
            fi
        done
    done

    current_time=$(date +%s)
    echo "node_textfile_mtime_seconds{file=\"$OUTPUT_FILE\"} $current_time" >> "$TEMP_FILE"

    mv "$TEMP_FILE" "$OUTPUT_FILE"
    chmod 644 "$OUTPUT_FILE"
    echo "📦 Metrics updated at $(date) → $OUTPUT_FILE"
    echo

    sleep 60
done
