use role accountadmin; 

CREATE ROLE IF NOT EXISTS DR_EXT_FACTSET_UAT_OWNER;
CREATE ROLE IF NOT EXISTS DR_EXT_FACTSET_UAT_READER;
CREATE ROLE IF NOT EXISTS DR_EXT_FACTSET_UAT_WRITER;

GRANT ROLE DR_EXT_FACTSET_UAT_OWNER,DR_EXT_FACTSET_UAT_READER, DR_EXT_FACTSET_UAT_WRITER to ROLE FR_DATA_PLATFORM_UAT;
GRANT ROLE DR_EXT_FACTSET_UAT_OWNER,DR_EXT_FACTSET_UAT_READER, DR_EXT_FACTSET_UAT_WRITER to ROLE FR_DATA_PLATFORM;

CREATE DATABASE IF NOT EXISTS EXT_FACTSET_UAT;

GRANT OWNERSHIP ON DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_OWNER;
GRANT ROLE DR_EXT_FACTSET_UAT_OWNER TO ROLE ACCOUNTADMIN;

USE ROLE DR_EXT_FACTSET_UAT_OWNER;

GRANT USAGE ON DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_READER;
GRANT USAGE ON DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_WRITER;

USE DATABASE EXT_FACTSET_UAT;

USE ROLE AR_GRANT_ADMIN;

GRANT OWNERSHIP ON FUTURE SCHEMAS IN DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE TABLES  IN DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS   IN DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_OWNER;

GRANT USAGE ON FUTURE SCHEMAS IN DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_READER;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_WRITER;

GRANT SELECT ON FUTURE TABLES IN DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_READER;
GRANT SELECT ON FUTURE TABLES IN DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_WRITER;

GRANT SELECT ON FUTURE VIEWS IN DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_READER;
GRANT SELECT ON FUTURE VIEWS IN DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_WRITER;


GRANT INSERT, UPDATE, DELETE ON FUTURE TABLES IN DATABASE EXT_FACTSET_UAT TO ROLE DR_EXT_FACTSET_UAT_WRITER;
