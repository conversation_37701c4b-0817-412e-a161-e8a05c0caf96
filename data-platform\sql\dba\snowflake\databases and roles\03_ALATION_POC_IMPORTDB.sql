
--- Permission for DB : ARCESIUM

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE ARCESIUM TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE ARCESIUM TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE ARCESIUM TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE ARCESIUM TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE ARCESIUM TO ROLE FR_DATACATALOG_ALATION;


<PERSON><PERSON><PERSON> REFERENCES ON ALL VIEWS IN DATABASE ARCESIUM TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE ARCESIUM TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE ARCESIUM TO ROLE FR_DATACATALOG_ALATION;
<PERSON><PERSON><PERSON> USAGE ON ALL STAGES IN DATABASE ARCESIUM TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE ARCESIUM TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE ARCESIUM TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : ARCESIUM

--- Permission for DB : EQUITY_KINETICS

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE EQUITY_KINETICS TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE EQUITY_KINETICS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE EQUITY_KINETICS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE EQUITY_KINETICS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE EQUITY_KINETICS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE EQUITY_KINETICS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE EQUITY_KINETICS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE EQUITY_KINETICS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE EQUITY_KINETICS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE EQUITY_KINETICS TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE EQUITY_KINETICS TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : EQUITY_KINETICS

--- Permission for DB : FACTSET

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE FACTSET TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE FACTSET TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE FACTSET TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE FACTSET TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE FACTSET TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE FACTSET TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE FACTSET TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE FACTSET TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE FACTSET TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE FACTSET TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE FACTSET TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : FACTSET

--- Permission for DB : IVYDBUS

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE IVYDBUS TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE IVYDBUS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE IVYDBUS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE IVYDBUS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE IVYDBUS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE IVYDBUS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE IVYDBUS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE IVYDBUS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE IVYDBUS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE IVYDBUS TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE IVYDBUS TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : IVYDBUS

--- Permission for DB : SHR_AMPGRID

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SHR_AMPGRID TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SHR_AMPGRID TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SHR_AMPGRID TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SHR_AMPGRID TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SHR_AMPGRID TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SHR_AMPGRID TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SHR_AMPGRID TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SHR_AMPGRID TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SHR_AMPGRID TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SHR_AMPGRID TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SHR_AMPGRID TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SHR_AMPGRID

--- Permission for DB : SHR_GLOBAL_GOVERNMENT

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SHR_GLOBAL_GOVERNMENT TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SHR_GLOBAL_GOVERNMENT TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SHR_GLOBAL_GOVERNMENT TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SHR_GLOBAL_GOVERNMENT TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SHR_GLOBAL_GOVERNMENT TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SHR_GLOBAL_GOVERNMENT TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SHR_GLOBAL_GOVERNMENT TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SHR_GLOBAL_GOVERNMENT TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SHR_GLOBAL_GOVERNMENT TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SHR_GLOBAL_GOVERNMENT TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SHR_GLOBAL_GOVERNMENT TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SHR_GLOBAL_GOVERNMENT

--- Permission for DB : SHR_WEATHER_ENVIRONMENT

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SHR_WEATHER_ENVIRONMENT TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SHR_WEATHER_ENVIRONMENT TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SHR_WEATHER_ENVIRONMENT TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SHR_WEATHER_ENVIRONMENT TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SHR_WEATHER_ENVIRONMENT TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SHR_WEATHER_ENVIRONMENT TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SHR_WEATHER_ENVIRONMENT TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SHR_WEATHER_ENVIRONMENT TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SHR_WEATHER_ENVIRONMENT TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SHR_WEATHER_ENVIRONMENT TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SHR_WEATHER_ENVIRONMENT TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SHR_WEATHER_ENVIRONMENT

--- Permission for DB : SHR_YES_ENERGY

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SHR_YES_ENERGY TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SHR_YES_ENERGY TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SHR_YES_ENERGY TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SHR_YES_ENERGY TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SHR_YES_ENERGY TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SHR_YES_ENERGY TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SHR_YES_ENERGY TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SHR_YES_ENERGY TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SHR_YES_ENERGY TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SHR_YES_ENERGY TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SHR_YES_ENERGY TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SHR_YES_ENERGY

--- Permission for DB : SPGICI_ARBFLOW

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_ARBFLOW TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_ARBFLOW TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_ARBFLOW TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_ARBFLOW TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_ARBFLOW TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_ARBFLOW TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_ARBFLOW TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_ARBFLOW TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_ARBFLOW TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_ARBFLOW TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_ARBFLOW TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_ARBFLOW

--- Permission for DB : SPGICI_CHEMICALANALYTICS

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_CHEMICALANALYTICS TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_CHEMICALANALYTICS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_CHEMICALANALYTICS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_CHEMICALANALYTICS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_CHEMICALANALYTICS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_CHEMICALANALYTICS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_CHEMICALANALYTICS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_CHEMICALANALYTICS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_CHEMICALANALYTICS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_CHEMICALANALYTICS TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_CHEMICALANALYTICS TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_CHEMICALANALYTICS

--- Permission for DB : SPGICI_CRUDESUPPLYRISK

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_CRUDESUPPLYRISK TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_CRUDESUPPLYRISK TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_CRUDESUPPLYRISK TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_CRUDESUPPLYRISK TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_CRUDESUPPLYRISK TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_CRUDESUPPLYRISK TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_CRUDESUPPLYRISK TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_CRUDESUPPLYRISK TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_CRUDESUPPLYRISK TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_CRUDESUPPLYRISK TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_CRUDESUPPLYRISK TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_CRUDESUPPLYRISK

--- Permission for DB : SPGICI_ENERGYPRICE_FORECAST

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_ENERGYPRICE_FORECAST TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_ENERGYPRICE_FORECAST TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_ENERGYPRICE_FORECAST TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_ENERGYPRICE_FORECAST TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_ENERGYPRICE_FORECAST TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_ENERGYPRICE_FORECAST TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_ENERGYPRICE_FORECAST TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_ENERGYPRICE_FORECAST TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_ENERGYPRICE_FORECAST TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_ENERGYPRICE_FORECAST TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_ENERGYPRICE_FORECAST TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_ENERGYPRICE_FORECAST

--- Permission for DB : SPGICI_EUGAS

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_EUGAS TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_EUGAS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_EUGAS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_EUGAS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_EUGAS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_EUGAS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_EUGAS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_EUGAS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_EUGAS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_EUGAS TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_EUGAS TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_EUGAS

--- Permission for DB : SPGICI_EWINDOW_MARKETDATA

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_EWINDOW_MARKETDATA TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_EWINDOW_MARKETDATA TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_EWINDOW_MARKETDATA TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_EWINDOW_MARKETDATA TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_EWINDOW_MARKETDATA TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_EWINDOW_MARKETDATA TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_EWINDOW_MARKETDATA TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_EWINDOW_MARKETDATA TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_EWINDOW_MARKETDATA TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_EWINDOW_MARKETDATA TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_EWINDOW_MARKETDATA TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_EWINDOW_MARKETDATA

--- Permission for DB : SPGICI_GLOBALLNGANALYTICSPLUS_SHARE

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_GLOBALLNGANALYTICSPLUS_SHARE TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_GLOBALLNGANALYTICSPLUS_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_GLOBALLNGANALYTICSPLUS_SHARE TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_GLOBALLNGANALYTICSPLUS_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_GLOBALLNGANALYTICSPLUS_SHARE TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_GLOBALLNGANALYTICSPLUS_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_GLOBALLNGANALYTICSPLUS_SHARE TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_GLOBALLNGANALYTICSPLUS_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_GLOBALLNGANALYTICSPLUS_SHARE TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_GLOBALLNGANALYTICSPLUS_SHARE TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_GLOBALLNGANALYTICSPLUS_SHARE TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_GLOBALLNGANALYTICSPLUS_SHARE

--- Permission for DB : SPGICI_GLOBALLNGANALYTICS_SHARE

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_GLOBALLNGANALYTICS_SHARE TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_GLOBALLNGANALYTICS_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_GLOBALLNGANALYTICS_SHARE TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_GLOBALLNGANALYTICS_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_GLOBALLNGANALYTICS_SHARE TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_GLOBALLNGANALYTICS_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_GLOBALLNGANALYTICS_SHARE TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_GLOBALLNGANALYTICS_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_GLOBALLNGANALYTICS_SHARE TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_GLOBALLNGANALYTICS_SHARE TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_GLOBALLNGANALYTICS_SHARE TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_GLOBALLNGANALYTICS_SHARE

--- Permission for DB : SPGICI_GLOBALOILDEMAND

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_GLOBALOILDEMAND TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_GLOBALOILDEMAND TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_GLOBALOILDEMAND TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_GLOBALOILDEMAND TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_GLOBALOILDEMAND TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_GLOBALOILDEMAND TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_GLOBALOILDEMAND TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_GLOBALOILDEMAND TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_GLOBALOILDEMAND TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_GLOBALOILDEMAND TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_GLOBALOILDEMAND TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_GLOBALOILDEMAND

--- Permission for DB : SPGICI_MARKETDATA_SHARE

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_MARKETDATA_SHARE TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_MARKETDATA_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_MARKETDATA_SHARE TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_MARKETDATA_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_MARKETDATA_SHARE TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_MARKETDATA_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_MARKETDATA_SHARE TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_MARKETDATA_SHARE TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_MARKETDATA_SHARE TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_MARKETDATA_SHARE TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_MARKETDATA_SHARE TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_MARKETDATA_SHARE

--- Permission for DB : SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_MARKETINSIGHTS_NAGASPIPELINEFLOWS

--- Permission for DB : SPGICI_WORLDOILSUPPLY

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_WORLDOILSUPPLY TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_WORLDOILSUPPLY TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_WORLDOILSUPPLY TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_WORLDOILSUPPLY TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_WORLDOILSUPPLY TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_WORLDOILSUPPLY TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_WORLDOILSUPPLY TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_WORLDOILSUPPLY TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_WORLDOILSUPPLY TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_WORLDOILSUPPLY TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_WORLDOILSUPPLY TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_WORLDOILSUPPLY

--- Permission for DB : SPGICI_WORLDREFINERYDATA

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGICI_WORLDREFINERYDATA TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGICI_WORLDREFINERYDATA TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGICI_WORLDREFINERYDATA TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGICI_WORLDREFINERYDATA TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGICI_WORLDREFINERYDATA TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGICI_WORLDREFINERYDATA TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGICI_WORLDREFINERYDATA TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGICI_WORLDREFINERYDATA TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGICI_WORLDREFINERYDATA TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGICI_WORLDREFINERYDATA TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGICI_WORLDREFINERYDATA TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGICI_WORLDREFINERYDATA

--- Permission for DB : SPGLOBAL_CDS_PRICING

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGLOBAL_CDS_PRICING TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGLOBAL_CDS_PRICING TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGLOBAL_CDS_PRICING TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGLOBAL_CDS_PRICING TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGLOBAL_CDS_PRICING TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGLOBAL_CDS_PRICING TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGLOBAL_CDS_PRICING TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGLOBAL_CDS_PRICING TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGLOBAL_CDS_PRICING TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGLOBAL_CDS_PRICING TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGLOBAL_CDS_PRICING TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGLOBAL_CDS_PRICING

--- Permission for DB : SPGLOBAL_XPRESS

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE SPGLOBAL_XPRESS TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DATABASE SPGLOBAL_XPRESS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPGLOBAL_XPRESS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE SPGLOBAL_XPRESS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE TABLES IN DATABASE SPGLOBAL_XPRESS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE SPGLOBAL_XPRESS TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE SPGLOBAL_XPRESS TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE SPGLOBAL_XPRESS TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE SPGLOBAL_XPRESS TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE SPGLOBAL_XPRESS TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE SPGLOBAL_XPRESS TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : SPGLOBAL_XPRESS
