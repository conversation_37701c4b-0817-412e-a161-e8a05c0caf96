import stcommon
from stcommon.infra.datalake import *

#!/usr/bin/env python
# coding: utf-8
"""
Copyright 2024 Bloomberg Finance L.P.

Sample code provided by Bloomberg is made available for illustration
purposes only. Sample code is modifiable by individual users and is not
reviewed for reliability, accuracy and is not supported as part of any
Bloomberg service.  Users are solely responsible for the selection of and
use or intended use of the sample code, its applicability, accuracy and
adequacy, and the resultant output thereof. Sample code is proprietary and
confidential to Bloomberg and neither the recipient nor any of its
representatives may distribute, publish or display such code to any other
party, other than information disclosed to its employees on a need-to-know
basis in connection with the purpose for which such code was provided.
Sample code provided by Bloomberg is provided without any representations or
warranties and subject to modification by Bloomberg in its sole discretion.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
BLOOMBERG BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
"""
# # Request a Timeseries of End-of-Day Values
# 
# This sample creates a historical data request for a custom universe.
# See [README](../../../README.html) for more details.


################################################################################
# - Import related libraries.
import datetime
import io
import json
import logging
import os
import shutil
import time
import uuid
from urllib.parse import urljoin

from oauthlib.oauth2 import BackendApplicationClient, TokenExpiredError
from requests_oauthlib import OAuth2Session


################################################################################
# - Set up logging.
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)-8s] [%(name)s:%(lineno)s]: %(message)s',
)
LOG = logging.getLogger(__name__)

import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root_temp = os.path.abspath(os.path.join(current_dir, '../../../../../../../..'))
sys.path.append(project_root_temp)
print(project_root_temp)

import jglib.infra.python.fileio as fio
import jglib.paths as pth
import fnmatch
import pandas
import tarfile
from IPython.display import display

################################################################################
# - Load credentials from credential file you have obtained from
# *https://console.bloomberg.com*
# ASSETS_PATH = os.path.realpath('../../../../')
# CREDENTIALS_PATH = os.path.join(ASSETS_PATH, 'credentials/bloomberg/ps-credential.txt')

# with io.open(CREDENTIALS_PATH, encoding="utf-8") as credential_file:
#     CREDENTIALS = json.load(credential_file)

# current_path = os.path.abspath(__file__)
# ASSETS_PATH = os.path.abspath(os.path.join(current_path, "../../../../../"))
# CREDENTIALS_PATH = os.path.join(ASSETS_PATH, 'credentials/bloomberg/ps-credential.txt')

# with io.open(CREDENTIALS_PATH, encoding="utf-8") as credential_file:
#     CREDENTIALS = json.load(credential_file) 
    
    
ASSETS_PATH = f'{pth.jg_data_path()}/bloomberg_snap/309_tick_history'
CREDENTIALS_PATH = f'{pth.jg_config_path()}/pem/ps-credential.txt'
with io.open(CREDENTIALS_PATH, encoding="utf-8") as credential_file:
    CREDENTIALS = json.load(credential_file)

with io.open(CREDENTIALS_PATH, encoding="utf-8") as credential_file:
    CREDENTIALS = json.load(credential_file)

EXPIRES_IN = datetime.datetime.fromtimestamp(CREDENTIALS['expiration_date'] / 1000) - datetime.datetime.utcnow()
if EXPIRES_IN.days < 0:
    LOG.warning("Credentials expired %s days ago", EXPIRES_IN.days)
elif EXPIRES_IN < datetime.timedelta(days=30):
    LOG.warning("Credentials expiring in %s", EXPIRES_IN)

################################################################################
# - Define class with additional logging and token refreshing.


class DLRestApiSession(OAuth2Session):
    """Custom session class for making requests to a DL REST API using OAuth2 authentication."""

    def __init__(self, *args, **kwargs):
        """
        Initialize a DLRestApiSession instance.
        """
        super().__init__(*args, **kwargs)

    def request_token(self):
        """
        Fetch an OAuth2 access token by making a request to the token endpoint.
        """
        oauth2_endpoint = 'https://bsso.blpprofessional.com/ext/api/as/token.oauth2'
        self.token = self.fetch_token(
            token_url=oauth2_endpoint,
            client_secret=CREDENTIALS['client_secret']
        )

    def request(self, *args, **kwargs):
        """
        Override the parent class method to handle TokenExpiredError by refreshing the token.
        :return: response object from the API request
        """
        try:
            response = super().request(*args, **kwargs)
        except TokenExpiredError:
            self.request_token()
            response = super().request(*args, **kwargs)

        return response

    def send(self, request, **kwargs):
        """
        Override the parent class method to log request and response information.
        :param request: prepared request object
        :return: response object from the API request
        """
        LOG.info("Request being sent to HTTP server: %s, %s, %s", request.method, request.url, request.headers)

        response = super().send(request, **kwargs)

        LOG.info("Response status: %s", response.status_code)
        LOG.info("Response x-request-id: %s", response.headers.get("x-request-id"))

        if response.ok:
            if not kwargs.get("stream"):
                LOG.info("Response content: %s", response.text)
        else:
            raise RuntimeError('\n\tUnexpected response status code: {c}\nDetails: {r}'.format(
                    c=str(response.status_code), r=response.json()))

        return response

################################################################################
# - Initialize HTTP session with OAuth2 client and fetch a token. If the token
# expires, request a fresh access token.
CLIENT = BackendApplicationClient(client_id=CREDENTIALS['client_id'])

SESSION = DLRestApiSession(client=CLIENT)
# This is a required header for each call to DL Rest API.
SESSION.headers['api-version'] = '2'
SESSION.request_token()

################################################################################
# - Define HOST address.
HOST = 'https://api.bloomberg.com'


def create_payload(payload_type, dt):
    print("Getting the existing config payload data")
    df_provided = datalake.readTable('gl','bloomberg.cusipreference','cusip_reference',version='mk1')
    print("Cusip Reference Data are")
    print(df_provided)
    # df_provided = df_provided[df_provided['is_active'] == 'True']
    df_provided = df_provided[(df_provided['is_active'] == 'True') & (df_provided['config_type'] == payload_type)]
    print(df_provided)

    identifier_list = [
            {
                '@type': 'Identifier',
                'identifierType': 'TICKER',
                'identifierValue': config_value
            }
            for config_value in df_provided['config_value'].unique().tolist()
        ] 
    
    mnemonic = payload_type
    request_name = "Python309HistoryRequest" + str(uuid.uuid1())[:6]
    request_payload = {
        '@type': 'HistoryRequest',
        'name': request_name,
        'description': 'Some description',
        'universe': {
            '@type': 'Universe',
            'contains': identifier_list,
        },
        'fieldList': {
            '@type': 'HistoryFieldList',
            'contains': [
                {'mnemonic': mnemonic},
                # {'mnemonic': 'FUT_AGGTE_VOL'},
            ],
        },
        'trigger': {
            '@type': 'SubmitTrigger'
        },
        'runtimeOptions': {
            '@type': 'HistoryRuntimeOptions',
            'dateRange': {
                '@type': 'IntervalDateRange',
                'startDate': '2023-01-01',
                'endDate': '2024-10-07'
            },
        },
        'formatting': {
            '@type': 'MediaType',
            'outputMediaType': 'application/json',
        },
    }
    return request_name, request_payload

def call_main_309_api(payload_type, dt):
    ############################################################################
    # - Discover catalog identifier for scheduling requests.
    catalogs_url = urljoin(HOST, '/eap/catalogs/')
    response = SESSION.get(catalogs_url)
    
    # We got back a good response. Let's extract our account number.
    catalogs = response.json()['contains']
    for catalog in catalogs:
        if catalog['subscriptionType'] == 'scheduled':
            # Take the catalog having "scheduled" subscription type,
            # which corresponds to the Data License account number.
            catalog_id = catalog['identifier']
            break
    else:
        # We exhausted the catalogs, but didn't find a non-'bbg' catalog.
        LOG.error('Scheduled catalog not in %r', response.json()['contains'])
        raise RuntimeError('Scheduled catalog not found')

    ############################################################################
    # - Construct the URL that will be the prefix for the other requests.
    account_url = urljoin(HOST, '/eap/catalogs/{c}/'.format(c=catalog_id))
    LOG.info("Scheduled catalog URL: %s", account_url)

    ############################################################################
    # # Request

    ############################################################################
    # - Create the request component.
    
    print(f"dt passed to payload is {dt}")
    request_name, request_payload  = create_payload(payload_type,dt)
    print(f"Requested Payload is : {request_payload}")
    print(f"Requested Name is : {request_name}")
    
    # request_name = "Python309HistoryRequest" + str(uuid.uuid1())[:6]
    
    # request_payload = {
    #     '@type': 'HistoryRequest',
    #     'name': request_name,
    #     'description': 'My favorite history request',
    #     'universe': {
    #         '@type': 'Universe',
    #         'contains': [
    #             {
    #                 '@type': 'Identifier',
    #                 'identifierType': 'TICKER',
    #                 'identifierValue': 'TYH23 Comdty',
                    
    #             },
    #             {
    #                 '@type': 'Identifier',
    #                 'identifierType': 'TICKER',
    #                 'identifierValue': 'TYM23 Comdty',
                    
    #             },
    #             {
    #                 '@type': 'Identifier',
    #                 'identifierType': 'TICKER',
    #                 'identifierValue': 'TYU23 Comdty',
                    
    #             },
    #             {
    #                 '@type': 'Identifier',
    #                 'identifierType': 'TICKER',
    #                 'identifierValue': 'TYZ23 Comdty',
                    
    #             },
    #             {
    #                 '@type': 'Identifier',
    #                 'identifierType': 'TICKER',
    #                 'identifierValue': 'TYH24 Comdty',
                    
    #             },
    #             {
    #                 '@type': 'Identifier',
    #                 'identifierType': 'TICKER',
    #                 'identifierValue': 'TYM24 Comdty',
                    
    #             },
    #             {
    #                 '@type': 'Identifier',
    #                 'identifierType': 'TICKER',
    #                 'identifierValue': 'TYU24 Comdty',
                    
    #             },
    #             {
    #                 '@type': 'Identifier',
    #                 'identifierType': 'TICKER',
    #                 'identifierValue': 'TYZ24 Comdty',
                    
    #             },
    #         ]
    #     },
    #     'fieldList': {
    #         '@type': 'HistoryFieldList',
    #         'contains': [
    #             {'mnemonic': 'PX_LAST'},
    #             # {'mnemonic': 'FUT_AGGTE_VOL'},
    #         ],
    #     },
    #     'trigger': {
    #         '@type': 'SubmitTrigger'
    #     },
    #     'runtimeOptions': {
    #         '@type': 'HistoryRuntimeOptions',
    #         'dateRange': {
    #             '@type': 'IntervalDateRange',
    #             'startDate': '2023-01-01',
    #             'endDate': '2024-10-07'
    #         },
    #     },
    #     'formatting': {
    #         '@type': 'MediaType',
    #         'outputMediaType': 'application/json',
    #     },
    # }
    LOG.info('Request component payload:\n%s', json.dumps(request_payload, indent=2))
    
    # Create a new request resource.
    requests_url = urljoin(account_url, 'requests/')
    response = SESSION.post(requests_url, json=request_payload)
    
    # Extract the URL of the created resource.
    request_location = response.headers['Location']
    request_url = urljoin(HOST, request_location)
    
    # Extract the identifier of the created resource.
    request_id = json.loads(response.text)['request']['identifier']
    
    LOG.info('%s resource has been successfully created at %s',
             request_name,
             request_url)

    ############################################################################
    # - Inspect the newly-created request component.
    SESSION.get(request_url)

    ############################################################################
    # ## Poll the Content Endpoint with Prefix and Show Listing

    ############################################################################
    # - Poll '/content/responses/' endpoint to find out if the output is ready
    # for download.
    responses_url = urljoin(HOST, '/eap/catalogs/{c}/content/responses/'.format(c=catalog_id))
    
    # Filter the required output from the available content by passing the request_name as prefix 
    # and request_id as unique requestIdentifier query parameters respectively.
    params = {
        'prefix': request_name,
        'requestIdentifier': request_id,
    }
    
    # We recommend adjusting the polling frequency and timeout based on the amount of data or the time range requested.
    
    reply_timeout_minutes = 45
    reply_timeout = datetime.timedelta(minutes=reply_timeout_minutes)
    expiration_timestamp = datetime.datetime.utcnow() + reply_timeout
    
    while datetime.datetime.utcnow() < expiration_timestamp:
        content_responses = SESSION.get(responses_url, params=params)
        response_contains = json.loads(content_responses.text)['contains']
        if len(response_contains) > 0 :
            output = response_contains[0]
            LOG.info('Response listing:\n%s', json.dumps(output, indent=2))
    
            output_key = output['key']
            output_url = urljoin(
                HOST,
                '/eap/catalogs/{c}/content/responses/{key}'.format(c=catalog_id, key=output_key)
            )
            output_file_path = os.path.join(os.path.join(ASSETS_PATH, 'downloads'), output_key)
            break
        else:
            # We make a delay between polls to reduce network usage.
            time.sleep(60)
    else:
        LOG.info('Response not received within %s minutes. Exiting.', reply_timeout_minutes)

    ############################################################################
    # - Download the file.
    with SESSION.get(output_url, stream=True) as response:
        output_filename = output_key
        if 'content-encoding' in response.headers:
            if response.headers['content-encoding'] == 'gzip':
                output_filename = output_filename + '.gz'
            elif response.headers['content-encoding'] == '':
                pass
            else:
                raise RuntimeError('Unsupported content encoding received in the response')
    
        downloads_path = os.path.join(ASSETS_PATH, 'downloads')
        output_file_path = os.path.join(downloads_path, output_filename)
    
        # vkm comment
        with open(output_file_path, 'wb') as output_file:
            LOG.info('Loading file from: %s (can take a while) ...', output_url)
            shutil.copyfileobj(response.raw, output_file)
    
    LOG.info('File downloaded: %s', output_filename)
    LOG.debug('File location: %s', output_file_path)

    ############################################################################
    # - Print out content of the downloaded file.
    import pandas
    from IPython.display import display
    
    with open(output_file_path, 'rb') as output_file:
        display(pandas.read_json(output_file, compression='gzip'))
    
    try:
        df_treasury_data = pandas.read_json(output_file_path, compression='gzip')
        # Display the first few rows of the DataFrame
        print(df_treasury_data.head())
    except ValueError as e:
        print(f"Error reading the JSON file: {e}")
        

    return df_treasury_data


def create_payload_for_ref_date_api(payload_type, dt_start, dt_end, ticker_list, user_terminal_number):
    print("Inside the 309 API payload creation:")
    identifier_list = [
            {
                '@type': 'Identifier',
                'identifierType': 'TICKER',
                'identifierValue': config_value
            }
            for config_value in ticker_list
        ] 
    
    mnemonic = payload_type
    request_name = "Python309HistoryRequest" + str(uuid.uuid1())[:6]
    request_payload = {
        '@type': 'HistoryRequest',
        'name': request_name,
        'description': 'Some description',
        'universe': {
            '@type': 'Universe',
            'contains': identifier_list,
        },
        'fieldList': {
            '@type': 'HistoryFieldList',
            'contains': [
                {'mnemonic': payload_type},
                # {'mnemonic': 'FUT_AGGTE_VOL'},
            ],
        },
        'trigger': {
            '@type': 'SubmitTrigger'
        },
        'runtimeOptions': {
            '@type': 'HistoryRuntimeOptions',
            'dateRange': {
                '@type': 'IntervalDateRange',
                'startDate': str(dt_start),
                'endDate': str(dt_end)
            },
        },
        'terminalIdentity': {
            '@type': "BbaTerminalIdentity", 
            'userNumber': user_terminal_number
                # '31289703'
        },
        'formatting': {
            '@type': 'MediaType',
            'outputMediaType': 'application/json',
        },
    }
    return request_name, request_payload


def call_main_309_for_ref_date_api(payload_type, dt_start, dt_end, ticker_list, user_terminal_number):
    ############################################################################
    # - Discover catalog identifier for scheduling requests.
    catalogs_url = urljoin(HOST, '/eap/catalogs/')
    response = SESSION.get(catalogs_url)
    
    # We got back a good response. Let's extract our account number.
    catalogs = response.json()['contains']
    for catalog in catalogs:
        if catalog['subscriptionType'] == 'scheduled':
            # Take the catalog having "scheduled" subscription type,
            # which corresponds to the Data License account number.
            catalog_id = catalog['identifier']
            break
    else:
        # We exhausted the catalogs, but didn't find a non-'bbg' catalog.
        LOG.error('Scheduled catalog not in %r', response.json()['contains'])
        raise RuntimeError('Scheduled catalog not found')

    account_url = urljoin(HOST, '/eap/catalogs/{c}/'.format(c=catalog_id))
    LOG.info("Scheduled catalog URL: %s", account_url)
    
    print(f"dt_start passed to payload is {dt_start}")
    print(f"dt_end passed to payload is {dt_end}")
    
    request_name, request_payload  = create_payload_for_ref_date_api(payload_type,dt_start, dt_end,ticker_list, user_terminal_number)
    print(f"Requested Payload is : {request_payload}")
    print(f"Requested Name is : {request_name}")
    
    LOG.info('Request component payload:\n%s', json.dumps(request_payload, indent=2))
    
    # Create a new request resource.
    requests_url = urljoin(account_url, 'requests/')
    response = SESSION.post(requests_url, json=request_payload)
    
    # Extract the URL of the created resource.
    request_location = response.headers['Location']
    request_url = urljoin(HOST, request_location)
    
    # Extract the identifier of the created resource.
    request_id = json.loads(response.text)['request']['identifier']
    
    LOG.info('%s resource has been successfully created at %s',
             request_name,
             request_url)

    SESSION.get(request_url)

    responses_url = urljoin(HOST, '/eap/catalogs/{c}/content/responses/'.format(c=catalog_id))
    
    params = {
        'prefix': request_name,
        'requestIdentifier': request_id,
    }
    
    reply_timeout_minutes = 45
    reply_timeout = datetime.timedelta(minutes=reply_timeout_minutes)
    expiration_timestamp = datetime.datetime.utcnow() + reply_timeout
    
    while datetime.datetime.utcnow() < expiration_timestamp:
        content_responses = SESSION.get(responses_url, params=params)
        response_contains = json.loads(content_responses.text)['contains']
        if len(response_contains) > 0 :
            output = response_contains[0]
            LOG.info('Response listing:\n%s', json.dumps(output, indent=2))
    
            output_key = output['key']
            output_url = urljoin(
                HOST,
                '/eap/catalogs/{c}/content/responses/{key}'.format(c=catalog_id, key=output_key)
            )
            output_file_path = os.path.join(os.path.join(ASSETS_PATH, 'downloads'), output_key)
            break
        else:
            # We make a delay between polls to reduce network usage.
            time.sleep(60)
    else:
        LOG.info('Response not received within %s minutes. Exiting.', reply_timeout_minutes)

    ############################################################################
    # - Download the file.
    with SESSION.get(output_url, stream=True) as response:
        output_filename = output_key
        if 'content-encoding' in response.headers:
            if response.headers['content-encoding'] == 'gzip':
                output_filename = output_filename + '.gz'
            elif response.headers['content-encoding'] == '':
                pass
            else:
                raise RuntimeError('Unsupported content encoding received in the response')
    
        downloads_path = os.path.join(ASSETS_PATH, 'downloads')
        output_file_path = os.path.join(downloads_path, output_filename)
    
        with open(output_file_path, 'wb') as output_file:
            LOG.info('Loading file from: %s (can take a while) ...', output_url)
            shutil.copyfileobj(response.raw, output_file)
    
    LOG.info('File downloaded: %s', output_filename)
    LOG.debug('File location: %s', output_file_path)

    ############################################################################
    # - Print out content of the downloaded file.
    import pandas
    from IPython.display import display
    
    with open(output_file_path, 'rb') as output_file:
        display(pandas.read_json(output_file, compression='gzip'))
    
    try:
        df_treasury_data = pandas.read_json(output_file_path, compression='gzip')
        # Display the first few rows of the DataFrame
        print(df_treasury_data.head())
    except ValueError as e:
        print(f"Error reading the JSON file: {e}")
        

    return df_treasury_data