{"id": 5360735, "name": "enforce-commit-messages", "target": "branch", "source_type": "Repository", "source": "Jain-Global/data-platform", "enforcement": "active", "conditions": {"ref_name": {"exclude": [], "include": ["~ALL"]}}, "rules": [{"type": "commit_message_pattern", "parameters": {"operator": "regex", "pattern": "^(?:[A-Z][A-Z0-9]{1,9}-[0-9]+ .+|Merge (?:branch|pull request).*)", "negate": false, "name": "Commit message must start with JIRA id, e.g. DATADEV-123, or \"Merge [branch|pull request]\""}}], "bypass_actors": []}