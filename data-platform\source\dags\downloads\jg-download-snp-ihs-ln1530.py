from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.trigger_rule import TriggerRule
from airflow.timetables.trigger import CronTriggerTimetable
from airflow.hooks.base import BaseHook
from datetime import timedelta
import os, sys, pendulum, subprocess, logging, requests
from util.validate import check_for_anomalies

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()
from stcommon.email_util_k8s import EmailUtility

def download_and_validate():
    run_snp_ihs_ln1530_script()  # try download
    
    # Fetch the Airflow connection by conn_id
    conn = BaseHook.get_connection("vendor_http")
    
    # Build full URL using connection details + endpoint
    endpoint = "getJFSFeedAvailabilitySatusDailyDownload/jg-download-snp-ihs-ln1530"
    base_url = f"{conn.host}"
    if conn.port:
        base_url += f":{conn.port}"
    full_url = f"{base_url}/{endpoint}"
    
    # Set headers (you can also pull these from conn.extra if stored there)
    headers = {"Content-Type": "application/json"}

    # Make the request
    response = requests.get(full_url, headers=headers)

    # Validate response
    try:
        success, message = check_for_anomalies(response)
        if success:
            logging.info(message) 
    except Exception as e:
        logging.error("Validation failed: %s", str(e))
        raise e
    
    return "success" 

def run_snp_ihs_ln1530_script():
    JGDATA_PATH = os.environ.get("JGDATA_PATH")

    command = (
        f'python3 {JGDATA_PATH}/bin/download.py --dataset spglobal.markit.ihs.ln1530'
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.warning("Command failed with exit status %d", e.returncode)
        logging.warning("STDOUT: %s", e.stdout)
        logging.warning("STDERR: %s", e.stderr)
        raise

def send_email(body: str):
    email_util = EmailUtility()
    
    email_util.send_email(
        to_recipient=["<EMAIL>", "<EMAIL>"],
        subject="S&P Global IHS - CDS Markit Data upload complete for LN1530",
        body=body,
        cc_recipient=["<EMAIL>", "<EMAIL>"]
    )
    return

def send_success_email():
    body = """ Hello!<br><br> 
        The process to download the S&P Global IHS Markit Data upload for <b>LN1530 batch</b> is <b>COMPLETE</b>.<br><br> 
        Please find the daily files in your local Windows network mount directory:<br> 
        \\jainglobal.local\\DFS\\Markit\\Options\\Credit_Index_Options_L-1530<br> 
        \\jainglobal.local\\DFS\\Markit\\Tranches\\Credit_Index_Tranche_Composites_L-1530<br><br> 
    """
    send_email(body)

def send_failure_email():
    body = """ Hello!<br><br> 
        The process to download the S&P Global IHS Markit Data upload for <b>LN1530 batch</b> has <b>FAILED</b> after multiple attempts.<br><br> 
        The Data Platform Support team has been identified and is looking into this issue.<br><br> 
    """
    send_email(body)

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2025, 4, 18, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 11,
    "retry_delay": timedelta(minutes=5)
}

dag = DAG(
    dag_id="jg-download-snp-ihs-ln1530",
    description="This DAG runs file download for S&P Global IHS Markit Data upload for LN 1530 batch",
    default_args=default_args,
    schedule = CronTriggerTimetable('35 10 * * 1-5', timezone='America/New_York'),
    dagrun_timeout = timedelta(minutes=40),
    tags=["jgdata","CDS", "IHS"],
    catchup=False
)

file_upload_job = PythonOperator(
    task_id="download_and_validate",
    python_callable=download_and_validate,
    execution_timeout=timedelta(minutes=5),
    dag=dag
)

send_email_success = PythonOperator(
    task_id="send_email_on_success",
    python_callable=send_success_email,
    execution_timeout=timedelta(minutes=5),
    dag=dag,
)

send_email_failure = PythonOperator(
    task_id="send_email_on_failure",
    python_callable=send_failure_email,
    execution_timeout=timedelta(minutes=5),
    trigger_rule=TriggerRule.ONE_FAILED,
    dag=dag,
)

file_upload_job >> [send_email_success, send_email_failure]