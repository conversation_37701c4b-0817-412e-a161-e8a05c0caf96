import argparse
import json
import os
import requests
import pandas as pd
import time
import logging
from strunner import *
from datetime import datetime, timedelta
from collections import defaultdict
import re


logging.basicConfig(level=logging.INFO)

setupEnvironment()

BASE_URL = 'https://www.globaltradetracker.com/api/rest'

def read_config_secrets(path=None):
    """
    Load GTT credentials from secure JSON file
    """
    secret_dir = path or '/jfs/tech1/apps/datait/jg-code/secure/prod'
    secret_file = os.path.join(secret_dir, 'config_secret.json')
    with open(secret_file, 'r') as f:
        return json.load(f)

class GTTClientBase:
    """Base class with shared GTT API logic."""
    def __init__(self, args):
        self.args = args
        self.log = logging.getLogger()
        self.log.info("Initializing GTTClientBase")
        self.config = read_config_secrets()
        self.log.info("Loaded config secrets")
        self.token = self._get_token()
        self.log.info("Fetched authentication token")
        self.fields = self._get_fields()
        self.date_to_updated_data = self._get_date_to_updated_data()
        self.monthly_countries = self._get_monthly_countries()
        self.countries_to_reload = self._get_updated_data_to_run()

    def _get_date_to_updated_data(self):
        """Determine which dates need reloading based on the last 7 runs."""
        self.log.info("Determining date range for updates")
        today = datetime.strptime(self.args.date, '%Y%m%d').date()

        last_seven_raw = [(today - timedelta(days=offset)).strftime('%Y%m%d') for offset in range(1, 8)]
        last_seven = sorted(last_seven_raw)

        log_dir = os.path.join(self.args.output_path, 'dates')
        log_file = os.path.join(log_dir, 'run_dates.csv')
        if os.path.exists(log_file):
            df_log = pd.read_csv(log_file, header=None, names=['date', 'flag'], dtype=str)
            logged = df_log['date'].tolist()
        else:
            logged = []
        self.log.info(f"Previously logged dates: {logged}")

        missing = [d for d in last_seven if d not in logged]
        self.log.info(f"Missing dates to fetch: {missing}")
        return missing if missing else [last_seven[0]]

    def _get_token(self):
        url = (
            f"{BASE_URL}/gettoken?userid={self.config['gtt_userid']}"
            f"&password={self.config['gtt_password']}"
        )
        resp = requests.get(url)
        resp.raise_for_status()
        return resp.text

    def _get_fields(self):
        url = f"{BASE_URL}/fields?token={self.token}&periods=true"
        response = requests.get(url)
        response.raise_for_status()
        return [f['id'] for f in response.json()]
    
    def _get_all_countries(self):
        """Fetch and return JSON data from the GTT countries API."""
        self.log.info("Fetching all countries from GTT API")
        url = (
            f"{BASE_URL}/countries?token={self.token}"
        )
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    

    def _get_monthly_countries(self):
        """Fetch countries with monthly update frequency."""
        self.log.info("Fetching all countries with monthly update frequency")
        data = self._get_all_countries()
        
        # Filter countries with monthly update frequency
        monthly_countries = [
            entry["country"]["alphageonom2"].lower()
            for entry in data
            if entry["country"].get("update_frequency") == "M"
        ]
        return monthly_countries
    

    def _get_updated_data_to_run(self):
        """Fetch countries and periods with new or updated data."""
        url = (
            f"{BASE_URL}/dataupdates?token={self.token}"
            f"&countryCode=ZZ&updatedAfter={self.date_to_updated_data[0]}"
        )
        data_updates = requests.get(url).json()
        raw_map = defaultdict(list)

        for country in data_updates:
            code = country.get('countryCode').lower()
            
            if code not in self.monthly_countries:
                # Skip countries that are not in the monthly update list
                self.log.info(f"Extracting only monthy report, contry {code} not in monthly update list, skipping")
           
                continue

            periods = set(country.get('periodsWithNewData', []) +
                        country.get('periodsWithUpdatedData', []))
            for period in periods:
                raw_map[period].append(code)
        periods_to_countries = {
            period: ",".join(sorted(set(codes)))
            for period, codes in raw_map.items()
        }
        return periods_to_countries


    def fetch_json(self, url):
        resp = requests.get(url)
        resp.raise_for_status()
        return resp.json()

    def save_csv(self, df, filename):
        os.makedirs(self.args.output_path, exist_ok=True)
        output_file = os.path.join(self.args.output_path, filename)
        df.to_csv(output_file, index=False)
        self.log.info(f"Saved {output_file}")

    def check_file_exists(self, filename):
        file_path = os.path.join(self.args.output_path, filename)
        if os.path.exists(file_path):
            self.log.info(f"File {file_path} already exists.")
            return True
        return False    
    
    def _append_run_log(self):
        """Append all dates from the oldest missing date through today."""
        self.log.info("Appending run log for all dates in range")
        missing_dates = self._get_date_to_updated_data()
        start_str = missing_dates[0]  # oldest date needing reload
        start_date = datetime.strptime(start_str, '%Y%m%d').date()
        end_date = datetime.strptime(self.args.date, '%Y%m%d').date()
        # Prepare log file and load already-logged dates
        log_dir = os.path.join(self.args.output_path, 'dates')
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, 'run_dates.csv')
        if os.path.exists(log_file):
            df_log = pd.read_csv(log_file, header=None, names=['date', 'flag'], dtype=str)
            logged = set(df_log['date'].tolist())
        else:
            logged = set()
        with open(log_file, 'a') as f:
            curr = start_date
            while curr <= end_date:
                ds = curr.strftime('%Y%m%d')
                if ds not in logged:
                    f.write(f"{ds},true\n")
                    self.log.info(f"Logged new date: {ds}")
                else:
                    logging.debug(f"Already logged: {ds}")
                curr += timedelta(days=1)


class TradeDetails(GTTClientBase):
    """Fetches HS-code-based trade details."""
    HS_CODES = '09,17,18,52,53,54,55,56,57,58,59,60,61,62,63,64'
    FLOWS = ['E', 'I']
    CURRENCIES = ['USD']
    TRADEDETAILS = (
        'SUBDIVISION,PORT,TRANSPORT,FOREIGN_PORT,'
        'PARTNER_US_STATE,CUSTOMS_REGIME,SUPPRESSION,'
        'CURRENCY,REGION,REPORTER_ISO,PARTNER_ISO'
    )
    PERIOD_TYPE = 'M'
    LAYOUT = 'brief'
    HSLEVEL = '-1'
    DECIMALSCALE = '8'

    def run(self):
        for period, countries_str in self.countries_to_reload.items():
            for flow in self.FLOWS:
                for currency in self.CURRENCIES:
                    self.log.info(f"Processing {countries_str} {flow} {currency} {period}")
                    filename = (
                        f"gtt_report_{period}_{flow}_{self.args.date}.csv"
                    )
                    if self.check_file_exists(filename):
                        self.log.info(f"Skipping {flow} {currency}")
                        continue

    
                    url = (
                        f"{BASE_URL}/getreport?token={self.token}"
                        f"&impexp={flow}"
                        f"&hscode={self.HS_CODES}"
                        f"&reporter={countries_str}"
                        f"&from={period}"
                        f"&to={period}"
                        f"&field={','.join(self.fields)}"
                        f"&periodtype={self.PERIOD_TYPE}"
                        f"&layout={self.LAYOUT}"
                        f"&tradedetails={self.TRADEDETAILS}"
                        f"&hslevel={self.HSLEVEL}"
                        f"&decimalscale={self.DECIMALSCALE}"
                        f"&precision=8"
                        f"&currency={currency}"
                        f"&ignoreCountriesWithoutTradeData=true"
                        f"&useMirrorDataForMissingPeriods=true"
                        f"&secondary_quantity=KG"
                    )                    
                    data = self.fetch_json(url).get('reportData', [])
                    df = pd.DataFrame(data)
                    self.save_csv(df, filename)
        self._append_run_log()
        self.log.info(f"Completed run for {self.args.date}")



def parse_args():
    parser = argparse.ArgumentParser(
        description="Download GTT trade_details CSVs"
    )
    parser.add_argument(
        '--date', required=True, help='Run date YYYYMMDD'
    )
    parser.add_argument(
        '--output_path', default='.', help='Directory for output CSVs'
    )
    return parser.parse_args()

def main():
    args = parse_args()
    client = TradeDetails(args)
    client.run()

if __name__ == '__main__':
    main()
