from airflow import DAG
from airflow.operators.python import <PERSON>Operator
from datetime import datetime, timedelta, timezone
import os, sys, pendulum, subprocess, logging
from airflow.providers.http.operators.http import HttpOperator
from util.validate import check_for_anomalies
from airflow.timetables.trigger import CronTriggerTimetable

current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from strunner import *
setupEnvironment()
from stcommon.time import *
import stcommon.tools.dates as dates
JGDATA_PATH = os.environ.get("JGDATA_PATH")


def run_dq_check_script():
    
    command = (
        f'python3 {JGDATA_PATH}/bin/bloomberg_snap_dq_check.py'
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2025, 7, 25, tz="UTC"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id="jg-bloombergsnap-dq-runner",
    description="This DAG runs DQ Check for Bloomberg Snap",
    default_args=default_args,
    schedule=CronTriggerTimetable('45 23 * * 1-6', timezone="UTC"),
    tags=["jgdata","BLOOMBERG SNAP","APACST"],
    catchup=False
)

dq_job = PythonOperator(
    task_id="jg-bloombergsnap-dq-runner",
    python_callable=run_dq_check_script,
    execution_timeout=timedelta(minutes=30),
    dag=dag
)

dq_job