import argparse
import re
import sys
import os
import time
import glob
from datetime import datetime
from urllib.parse import urljoin, urlparse
from strunner import *
setupEnvironment

import requests
from bs4 import BeautifulSoup
import pandas as pd

class CottonOnCallHistoricalScraper:
   def __init__(self, output_path, start_year=2001, end_year=None):
       self.output_path = output_path
       self.start_year = start_year
       self.end_year = end_year or datetime.now().year
       self.base_url = "https://www.cftc.gov"
       self.headers = {
           'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
       }
       self.session = requests.Session()
       self.session.headers.update(self.headers)
       self.session.verify = False
       import urllib3
       urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
      
       os.makedirs(self.output_path, exist_ok=True)
      
   def get_page_content(self, url, retries=3):
       """Fetch page content with retry logic"""
       for attempt in range(retries):
           try:
               response = self.session.get(url, timeout=30)
              
               if response.status_code == 403:
                   print(f"Access forbidden (403) for {url}")
                   return None
               elif response.status_code == 404:
                   print(f"Page not found (404) for {url}")
                   return None
                  
               response.raise_for_status()
               return response.content
           except requests.exceptions.HTTPError as e:
               if e.response.status_code in [403, 404]:
                   print(f"HTTP {e.response.status_code} error for {url}")
                   return None
               print(f"HTTP error fetching {url} (attempt {attempt + 1}/{retries}): {e}")
               if attempt < retries - 1:
                   time.sleep(2 ** attempt)
               else:
                   return None
           except Exception as e:
               print(f"Error fetching {url} (attempt {attempt + 1}/{retries}): {e}")
               if attempt < retries - 1:
                   time.sleep(2 ** attempt)
               else:
                   return None
       return None
  
   def get_all_report_links(self, main_page_url):
       """Extract all report links from the main historical page organized by year"""
       print("Fetching main historical index page...")
       content = self.get_page_content(main_page_url)
       soup = BeautifulSoup(content, 'html.parser')
      
       year_reports = {}
      
       for elem in soup.find_all(['p', 'strong', 'h3', 'h4']):
           text = elem.get_text().strip()
           year_match = re.match(r'^(\d{4})$', text)
           if year_match:
               year = int(year_match.group(1))
               if self.start_year <= year <= self.end_year:
                   print(f"Found year section: {year}")
                  
                   current = elem
                   while current:
                       current = current.find_next_sibling()
                       if current and current.name == 'table':
                           links = self.extract_links_from_table(current, year)
                           if links:
                               year_reports[year] = links
                           break
       accordions = soup.find_all('dl', class_='ckeditor-accordion')
       for accordion in accordions:
           dts = accordion.find_all('dt')
           dds = accordion.find_all('dd')
          
           for dt, dd in zip(dts, dds):
               header_text = dt.get_text().strip()
               year_match = re.search(r'(\d{4})', header_text)
               if year_match:
                   year = int(year_match.group(1))
                   if self.start_year <= year <= self.end_year:
                       print(f"Found accordion year: {year}")
                       tables = dd.find_all('table')
                       all_links = []
                       for table in tables:
                           links = self.extract_links_from_table(table, year)
                           all_links.extend(links)
                       if all_links:
                           year_reports[year] = all_links
       if not year_reports:
           print("No structured year data found, extracting all report links...")
           all_links = soup.find_all('a', href=re.compile(r'deaoncall\d+\.html?', re.I))
          
           for link in all_links:
               href = link['href']
               text = link.get_text().strip()
              
               year = self.extract_year_from_context(link)
               if year and self.start_year <= year <= self.end_year:
                   if year not in year_reports:
                       year_reports[year] = []
                  
                   if href.startswith('http'):
                       full_url = href
                   elif href.startswith('/'):
                       full_url = self.base_url + href
                   else:
                       if year and year < 2010:
                           full_url = urljoin(self.base_url + "/dea/cotton/", href.split('/')[-1])
                       else:
                           full_url = urljoin(main_page_url, href)
                  
                   year_reports[year].append({
                       'url': full_url,
                       'text': text,
                       'date_str': self.extract_date_from_link(href, text)
                   })
      
       return year_reports
  
   def extract_links_from_table(self, table, year):
       """Extract all report links from a table"""
       links = []
       for link in table.find_all('a', href=True):
           href = link['href']
           text = link.get_text().strip()
          
           if 'deaoncall' in href.lower():
               if href.startswith('http'):
                   full_url = href
               elif href.startswith('/'):
                   full_url = self.base_url + href
               else:
                   if '/dea/cotton/' in href or year < 2010:
                       full_url = urljoin(self.base_url + "/dea/cotton/", href.split('/')[-1])
                   else:
                       full_url = urljoin(self.base_url + "/MarketReports/CottonOnCall/HistoricalCottonOn-Call/", href)
              
               links.append({
                   'url': full_url,
                   'text': text,
                   'date_str': self.extract_date_from_link(href, text)
               })
      
       return links
  
   def extract_year_from_context(self, link_elem):
       """Try to extract year from link context"""
       parent = link_elem.parent
       max_depth = 5
      
       while parent and max_depth > 0:
           text = parent.get_text()
           year_match = re.search(r'\b(20\d{2}|19\d{2})\b', text)
           if year_match:
               return int(year_match.group(1))
           parent = parent.parent
           max_depth -= 1
      
       href = link_elem.get('href', '')
       date_match = re.search(r'deaoncall\d{4}(\d{2})', href)
       if date_match:
           year_suffix = int(date_match.group(1))
           if year_suffix <= 30:
               return 2000 + year_suffix
           else:
               return 1900 + year_suffix
      
       return None
  
   def extract_date_from_link(self, href, text):
       """Extract date from link href or text"""
       match = re.search(r'deaoncall(\d{6})', href)
       if match:
           return match.group(1)
      
       date_match = re.search(r'(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})', text)
       if date_match:
           return date_match.group(1)
      
       return 'unknown'
  
   def parse_pre_formatted_text(self, text):
       """Parse pre-formatted text from older reports (2001-era)"""
       lines = text.split('\n')
       data = []
      
       # Look for the data section (after the header lines)
       data_started = False
      
       for line in lines:
           line = line.strip()
          
           # Skip empty lines and divider lines
           if not line or line.startswith('|----') or line.startswith('----'):
               continue
              
           # Skip header lines
           if any(x in line.lower() for x in ['weekly report', 'on call positions', 'in contracts', 'futures based', 'unfixed call', 'open futures']):
               continue
              
           # Look for data lines that start with a month
           import re
           if re.match(r'^\|?[A-Z][a-z]+\s+\'?\d{2}', line):
               data_started = True
              
           if data_started and ('totals' in line.lower() or re.match(r'^\|?[A-Z][a-z]+\s+\'?\d{2}', line)):
               # Clean up the line - remove pipes and extra spaces
               clean_line = re.sub(r'\|', '', line)
               clean_line = re.sub(r'\s+', ' ', clean_line).strip()
              
               # Split by whitespace and try to extract numeric data
               parts = clean_line.split()
               if len(parts) >= 7:  # Should have month, year, and 6 numeric columns
                   try:
                       # Extract month and year
                       month_year = f"{parts[0]} {parts[1]}"
                      
                       # Extract numeric values (skip non-numeric parts)
                       numeric_parts = []
                       for part in parts[2:]:
                           # Handle negative numbers and commas
                           clean_part = part.replace(',', '').replace('-', '0') if part == '-' else part.replace(',', '')
                           try:
                               numeric_parts.append(int(clean_part))
                           except:
                               continue
                      
                       if len(numeric_parts) >= 6:
                           row = [month_year] + numeric_parts[:6]
                           data.append(row)
                          
                   except Exception as e:
                       continue
      
       if data and len(data) >= 5:
           columns = [
               "Futures Based On",
               "Unfixed Call Sales",
               "Change From Previous Week Sales",
               "Unfixed Call Purchases",
               "Change From Previous Week Purchases",
               "At Close",
               "Change From Previous Week Close"
           ]
          
           df = pd.DataFrame(data, columns=columns)
           return self.clean_cotton_table(df)
      
       return None
  
   def parse_report_table(self, report_url):
       """Parse table data from a report page"""
       try:
           if report_url.lower().endswith('.pdf'):
               print(f"Skipping PDF file: {report_url}")
               return None, None
          
           content = self.get_page_content(report_url)
           if content is None:
               print(f"Could not fetch content from {report_url}")
               return None, None
              
           soup = BeautifulSoup(content, 'html.parser')
          
           expected_columns = [
               "Futures Based On:",
               "Unfixed Call Sales",
               "Change From Previous Week",
               "Unfixed Call Purchases",
               "Change From Previous Week",
               "At Close",
               "Change From Previous Week"
           ]
          
           target_df = None
           try:
               from io import StringIO
               # Try multiple matching strategies for different year formats
               match_patterns = [
                   'Futures Based On|Unfixed Call|Total',
                   'Call Cotton Based|Open Futures Contracts',
                   'December|March|May|July|October'
               ]
              
               for pattern in match_patterns:
                   try:
                       dfs = pd.read_html(StringIO(soup.prettify()), match=pattern)
                      
                       for df in dfs:
                           # Handle multi-level columns (post-2018 format)
                           if isinstance(df.columns, pd.MultiIndex):
                               # Flatten multi-level columns
                               df.columns = [' '.join(col).strip() if isinstance(col, tuple) else col for col in df.columns]
                          
                           if self.is_cotton_table(df):
                               target_df = self.clean_cotton_table(df)
                               print(f"Found Cotton On Call table with {len(target_df)} rows")
                               break
                      
                       if target_df is not None:
                           break
                          
                   except Exception as e:
                       print(f"pandas read_html with pattern '{pattern}' failed: {e}")
                      
           except Exception as e:
               print(f"pandas read_html failed: {e}")
           if target_df is None:
               tables = soup.find_all('table')
              
               # Look for the cotton-on-call table specifically
               for table in tables:
                   # Check for the specific ID used in 2025 reports
                   if table.get('id') == 'cotton-on-call' or table.get('summary', '').lower().find('on call positions') != -1:
                       try:
                           rows = table.find_all('tr')
                           data = []
                           headers = None
                          
                           for row in rows:
                               cols = row.find_all(['td', 'th'])
                               if not cols:
                                   continue
                                  
                               col_texts = [col.get_text(strip=True) for col in cols]
                              
                               # Skip empty rows
                               if all(not text for text in col_texts):
                                   continue
                                  
                               # Look for header rows
                               if any('Futures' in text for text in col_texts) or any('Call' in text for text in col_texts):
                                   if not headers:
                                       headers = col_texts
                               elif col_texts and any(text for text in col_texts):  # Non-empty row
                                   # This is a data row
                                   data.append(col_texts)
                          
                           if data and len(data) >= 5:  # Reduced minimum for 2025
                               # Check if all rows have consistent column count
                               col_counts = [len(row) for row in data]
                               most_common_count = max(set(col_counts), key=col_counts.count)
                              
                               # Filter data to only include rows with the most common column count
                               filtered_data = [row for row in data if len(row) == most_common_count]
                              
                               if filtered_data:
                                   df = pd.DataFrame(filtered_data)
                                   # Try to set meaningful column names
                                   if most_common_count >= 7:
                                       df.columns = expected_columns[:most_common_count]
                                   elif headers and len(headers) == most_common_count:
                                       df.columns = headers
                                   else:
                                       # Use default column names
                                       pass
                              
                               if self.is_cotton_table(df):
                                   target_df = self.clean_cotton_table(df)
                                   print(f"Found Cotton On Call table via ID/summary extraction with {len(target_df)} rows")
                                   break
                                  
                       except Exception as e:
                           print(f"Error extracting cotton-on-call table: {e}")
                           continue
                  
                   # Fallback to text-based detection
                   table_text = table.get_text().lower()
                   if 'futures based on' in table_text and 'unfixed call' in table_text:
                       try:
                           rows = table.find_all('tr')
                           if len(rows) >= 10:
                               data = []
                               headers = None
                              
                               for row in rows:
                                   cols = row.find_all(['td', 'th'])
                                   if not cols:
                                       continue
                                      
                                   col_texts = [col.get_text(strip=True) for col in cols]
                                  
                                   if any('Futures Based On' in text for text in col_texts):
                                       headers = col_texts
                                   elif col_texts and len(col_texts) >= 6:
                                       data.append(col_texts)
                              
                               if data and len(data) >= 10:
                                   if headers:
                                       df = pd.DataFrame(data, columns=headers)
                                   else:
                                       df = pd.DataFrame(data)
                                       if len(df.columns) >= 7:
                                           df.columns = expected_columns
                                  
                                   if self.is_cotton_table(df):
                                       target_df = self.clean_cotton_table(df)
                                       print(f"Found Cotton On Call table via manual extraction with {len(target_df)} rows")
                                       break
                                      
                       except Exception as e:
                           print(f"Error extracting table manually: {e}")
                           continue
          
           if target_df is None:
               pre_elements = soup.find_all('pre')
               for pre in pre_elements:
                   text = pre.get_text()
                   if ('futures' in text.lower() and 'call' in text.lower()) or ('cotton' in text.lower() and 'contracts' in text.lower()):
                       try:
                           # Parse older format pre-formatted text (2001-era)
                           target_df = self.parse_pre_formatted_text(text)
                           if target_df is not None:
                               print(f"Found Cotton On Call table in <pre> element with {len(target_df)} rows")
                               break
                       except Exception as e:
                           print(f"Error parsing pre-formatted text: {e}")
                           continue
          
           if target_df is None:
               # Additional check for 2025 empty table issue
               # Sometimes the table structure exists but with no data rows
               cotton_table = soup.find('table', {'id': 'cotton-on-call'})
               if cotton_table:
                   print(f"Found cotton-on-call table but it appears to be empty in {report_url}")
                   print("This might be a temporary data availability issue on CFTC website")
               else:
                   print(f"Could not find Cotton On Call table in {report_url}")
               return None, None
          
           week_number = self.extract_week_number(soup)
          
           return target_df, week_number
          
       except Exception as e:
           print(f"Error parsing report {report_url}: {e}")
           import traceback
           traceback.print_exc()
           return None, None
  
   def is_cotton_table(self, df):
       """Check if a dataframe looks like the Cotton On Call table"""
       if df is None or df.empty:
           return False
          
       # Relaxed the row count check - some years might have fewer months
       if len(df) < 5 or len(df) > 25:
           return False
          
       try:
           # Check for 'total' in last few rows (might not be last row exactly)
           last_rows_text = ' '.join(' '.join(str(val) for val in df.iloc[i].values) for i in range(-min(3, len(df)), 0))
           if 'total' not in last_rows_text.lower():
               return False
       except:
           return False
          
       df_text = df.to_string().lower()
      
       # Check for key terms - either old format or new format
       old_format_terms = ['futures', 'unfixed', 'call']
       new_format_terms = ['december', 'march', 'may', 'july']  # Contract months
       cotton_terms = ['cotton', 'call']
      
       # Accept if it has old format terms OR new format terms plus cotton/call
       has_old_format = all(term in df_text for term in old_format_terms)
       has_new_format = any(term in df_text for term in new_format_terms) and any(term in df_text for term in cotton_terms)
      
       if not (has_old_format or has_new_format):
           return False
          
       return True
  
   def clean_cotton_table(self, df):
       """Clean and standardize the Cotton On Call table"""
       df = df.dropna(how='all')
      
       # Remove rows with all zeros, dashes, or empty values
       df = df[~df.apply(lambda row: all(str(val).strip() in ['0', '-', ''] for val in row), axis=1)]
      
       # Remove header rows that got included as data
       # These typically contain text like "Futures Based On:", "Call Cotton", "Unfixed Call"
       df = df[~df.apply(lambda row: any(
           str(val).strip().lower() in ['futures based on:', 'call cotton based new york', 'unfixed call sales', 'open futures contracts', 'ice futures u.s.']
           for val in row
       ), axis=1)]
      
       # Remove rows that are clearly headers (contain mostly text, not contract months)
       df = df[~df.apply(lambda row:
           str(row.iloc[0]).strip().lower() in ['futures based on:', 'futures  based  on:'] or
           any(str(val).strip().lower() in ['change from previous week', 'at close'] for val in row.iloc[1:])
       , axis=1)]
      
       # Handle the case where first column might be unnamed or have index
       if len(df.columns) > 0 and df.columns[0] in [0, '0', 'Unnamed: 0']:
           # First column is likely the row labels/futures months
           df.iloc[:, 0] = df.iloc[:, 0].fillna('')
      
       if len(df.columns) < 7:
           print(f"Warning: Table has only {len(df.columns)} columns, expected 7")
      
       expected_columns = [
           "Futures Based On",
           "Unfixed Call Sales",
           "Change From Previous Week Sales",
           "Unfixed Call Purchases",
           "Change From Previous Week Purchases",
           "At Close",
           "Change From Previous Week Close"
       ]
      
       # Try to intelligently map columns based on content
       if len(df.columns) >= 7:
           # Check if columns already have reasonable names
           col_str = ' '.join(str(c).lower() for c in df.columns)
           if 'futures' in col_str or 'unfixed' in col_str or 'cotton' in col_str:
               # Columns seem to have some meaningful names already, just standardize
               new_columns = []
               for i, col in enumerate(df.columns):
                   if i < len(expected_columns):
                       new_columns.append(expected_columns[i])
                   else:
                       new_columns.append(col)
               df.columns = new_columns
           else:
               # No meaningful column names, use expected
               df.columns = expected_columns[:len(df.columns)]
      
       # Find the "Totals" row and remove everything after it
       totals_idx = None
       for idx, row in df.iterrows():
           if str(row.iloc[0]).strip().lower() == 'totals':
               totals_idx = idx
               break
      
       if totals_idx is not None:
           # Keep everything up to and including the Totals row
           df = df.loc[:totals_idx]
      
       # Additional cleanup: remove footnote rows that contain "Merchants with futures positions"
       df = df[~df.apply(lambda row: any(
           'merchants with futures positions' in str(val).lower() or
           'released after' in str(val).lower() or
           'division of market oversight' in str(val).lower() or
           'email contact' in str(val).lower()
           for val in row
       ), axis=1)]
      
       # Clean numeric values - remove commas and quotes from all columns except the first
       if len(df.columns) > 1:
           for col in df.columns[1:]:  # Skip first column (Futures Based On)
               df[col] = df[col].apply(self.clean_numeric_value)
      
       return df
  
   def clean_numeric_value(self, value):
       """Clean numeric values by removing commas, quotes, and converting to proper format"""
       if pd.isna(value):
           return value
      
       # Convert to string and clean
       str_value = str(value).strip()
      
       # Remove quotes
       str_value = str_value.replace('"', '').replace("'", '')
      
       # Remove commas
       str_value = str_value.replace(',', '')
      
       # Handle special cases like "-" or empty strings
       if str_value in ['-', '', 'nan']:
           return 0
      
       # Handle asterisks (sometimes used as footnote markers)
       str_value = str_value.replace('*', '').strip()
      
       try:
           # Try to convert to integer first
           if '.' not in str_value:
               return int(str_value)
           else:
               # If it has decimal, keep as float
               return float(str_value)
       except ValueError:
           # If conversion fails, return 0
           print(f"Warning: Could not convert '{value}' to number, using 0")
           return 0
  
   def extract_week_number(self, soup):
       """Extract week number from the page"""
       text = soup.get_text()
       patterns = [
           r'Weekly\s+Report\s+No\.\s+(\d+)',  # 2001 format
           r'Week\s+(\d+)',
           r'week\s+(\d+)',
           r'Week\s*#?\s*(\d+)',
           r'Report\s+(\d+)',
           r'report\s+(\d+)',
           r'Report\s*#?\s*(\d+)',
           r'(\d+)\s*(?:st|nd|rd|th)?\s*week',
           r'Week\s*ending.*?(\d+)',
           r'For\s+week\s+(\d+)',
           r'Weekly\s+report\s+(\d+)',
       ]
      
       for pattern in patterns:
           match = re.search(pattern, text, re.IGNORECASE)
           if match:
               week_num = match.group(1)
               if week_num.isdigit() and 1 <= int(week_num) <= 53:
                   return week_num
      
       title = soup.find('title')
       if title:
           title_text = title.get_text()
           for pattern in patterns:
               match = re.search(pattern, title_text, re.IGNORECASE)
               if match:
                   week_num = match.group(1)
                   if week_num.isdigit() and 1 <= int(week_num) <= 53:
                       return week_num
      
       for header in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5']):
           header_text = header.get_text()
           for pattern in patterns:
               match = re.search(pattern, header_text, re.IGNORECASE)
               if match:
                   week_num = match.group(1)
                   if week_num.isdigit() and 1 <= int(week_num) <= 53:
                       return week_num
      
       return "unknown"
  
   def save_report(self, df, year, date_str, week_number):
       """Save report data to CSV"""
       if df is None or df.empty:
           return
      
       filename = f"cotton_on_call_report_{year}_report_week_{week_number}.csv"
       filepath = os.path.join(self.output_path, filename)
      
       existing_files = glob.glob(os.path.join(self.output_path, f"cotton_on_call_report_{year}_report_week_{week_number}.csv"))
       if existing_files:
           print(f"Report already exists, skipping: {filename}")
           return existing_files[0]
      
       df.to_csv(filepath, index=False)
       print(f"Saved: {filepath} ({len(df)} rows)")
      
       return filepath
  
   def scrape_all_historical_data(self):
       """Main method to scrape all historical data"""
       main_url = "https://www.cftc.gov/MarketReports/CottonOnCall/HistoricalCottonOn-Call/index.htm"
      
       year_reports = self.get_all_report_links(main_url)
      
       if not year_reports:
           print("No report links found!")
           return
      
       total_reports = 0
       failed_reports = 0
       year_summary = {}
      
       for year in sorted(year_reports.keys()):
           print(f"\n{'='*50}")
           print(f"Processing year {year}")
           print(f"{'='*50}")
          
           report_links = year_reports[year]
           print(f"Found {len(report_links)} reports for year {year}")
          
           year_success = 0
           year_failed = 0
          
           for i, report_info in enumerate(report_links):
               print(f"\nProcessing report {i+1}/{len(report_links)}: {report_info['text']}")
              
               df, week_number = self.parse_report_table(report_info['url'])
              
               if df is not None:
                   saved_file = self.save_report(df, year, report_info['date_str'], week_number)
                   if saved_file:
                       total_reports += 1
                       year_success += 1
               else:
                   failed_reports += 1
                   year_failed += 1
              
               time.sleep(1)
          
           year_summary[year] = {
               'total': len(report_links),
               'success': year_success,
               'failed': year_failed
           }
      
       print(f"\n{'='*50}")
       print(f"Scraping completed!")
       print(f"Total reports processed: {total_reports}")
       print(f"Failed reports: {failed_reports}")
       print(f"Data saved to: {self.output_path}")
       print(f"\nSummary by year:")
       for year in sorted(year_summary.keys()):
           summary = year_summary[year]
           print(f"  {year}: {summary['success']}/{summary['total']} successful")
       print(f"{'='*50}")

def main():
   parser = argparse.ArgumentParser(description='Scrape CFTC Historical Cotton On Call reports')
   parser.add_argument('--output_path', required=True, help='Directory to save CSV files')
   parser.add_argument('--start_year', type=int, default=2001, help='Starting year (default: 2001)')
   parser.add_argument('--end_year', type=int, default=None, help='Ending year (default: current year)')
  
   args = parser.parse_args()
  
   scraper = CottonOnCallHistoricalScraper(
       output_path=args.output_path,
       start_year=args.start_year,
       end_year=args.end_year
   )
  
   scraper.scrape_all_historical_data()

if __name__ == "__main__":
   main()




