import sys
import os
import pandas as pd
from paramiko import Transport, SFTPClient, RSAKey
from io import BytesIO
from datetime import datetime
from sqlalchemy import create_engine
import re
import fnmatch
import json
# from loguru import logger as log
import argparse
import psycopg2
from urllib.parse import urlparse
import io

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root_temp = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.append(project_root_temp)
print(project_root_temp)

import jglib.infra.python.fileio as fio
import jglib.paths as pth
from jglib.tools.s3Operations import S3_Operations
import boto3



class SFTPDataLoader:
    def __init__(self, pattern, region):
        self.pattern = pattern
        self.region = region
        self.jfs_base_directory = f'{pth.jg_data_path()}/flex_sod_position'
        self.config = fio.read_config_secrets()
        # self.engine = create_engine(f"postgresql://{self.config['pg_user']}:{self.config['pg_password']}@{self.config['pg_host']}:{self.config['pg_port']}/{self.config['pg_database']}")

    def extract_date_from_filename(self, filename):
        match = re.search(r'(\d{8})', filename)
        if match:
            return datetime.strptime(match.group(1), '%Y%m%d').date()
        return None

    def connect_sftp(self):
        # private_key_path = self.config['private_key_path']
        private_key_path = default_password = f'{pth.jg_config_path()}/pem/id_rsa_arcesium'
        # private_key_passphrase = self.config['private_key_passphrase']
        
        # Load the private key
        # private_key = RSAKey.from_private_key_file(private_key_path, password=private_key_passphrase)
        private_key = RSAKey.from_private_key_file(private_key_path)
        
        transport = Transport((self.config['flex_sod_arcesium_sftp_server'], int(self.config['flex_sod_arcesium_sftp_port'])))
        transport.connect(username=self.config['flex_sod_arcesium_sftp_user'], pkey=private_key)
        sftp = SFTPClient.from_transport(transport)
        return sftp, transport

    def get_latest_file(self, sftp):
        files = sftp.listdir(self.config['flex_sod_sftp_directory'])
        filtered_files = fnmatch.filter(files, self.pattern)
        latest_file = None
        latest_date = None

        for file in filtered_files:
            file_date = self.extract_date_from_filename(file)
            if file_date and (latest_date is None or file_date > latest_date):
                latest_date = file_date
                latest_file = file

        print(filtered_files)
        print(file_date)
        print(latest_date)
        print(latest_file)

        if latest_file is None:
            raise ValueError("No valid files found in the directory.")
        
        print(f"File Found: {latest_file}")
        return latest_file, latest_date

    def download_file(self, sftp, file_path):
        bio = BytesIO()
        sftp.getfo(file_path, bio)
        sftp.close()
        bio.seek(0)
        print(f"File Downloaded")
        return bio

    def load_to_dataframe(self, bio, latest_date):
        df = pd.read_csv(bio)
        print(f"DF Ready")
        df['LoadTimestamp'] = datetime.now()
        df['LoadDate'] = datetime.now().date()
        df['FileDate'] = latest_date
        df['region'] = self.region
        df.columns = df.columns.str.lower()
        return df

    def save_df_to_jfs(self, df, latest_file):
        if not os.path.exists(self.jfs_base_directory):
            os.makedirs(self.jfs_base_directory)
        file_with_path = os.path.join(self.jfs_base_directory, latest_file)
        df.to_csv(file_with_path, index=False)

    def run(self):
        sftp, transport = self.connect_sftp()
        latest_file, latest_date = self.get_latest_file(sftp)
        file_path = os.path.join(self.config['flex_sod_sftp_directory'], latest_file)
        bio = self.download_file(sftp, file_path)
        transport.close()

        df = self.load_to_dataframe(bio, latest_date)
        # self.save_df_to_jfs(df, latest_file)
        return df, latest_date, latest_file


class DfDataLoader:
    def __init__(self, rerun_with_delete, df, region):
        self.region = region
        self.rerun_with_delete = rerun_with_delete
        self.config = fio.read_config_secrets()
        # self.engine = create_engine(f"postgresql://{self.config['pg_user']}:{self.config['pg_password']}@{self.config['pg_host']}:{self.config['pg_port']}/{self.config['pg_database']}")
        self.db_params = {
                        'dbname': self.config['pg_database'],
                        'user': self.config['pg_user'],
                        'password': self.config['pg_password'],
                        'host': self.config['pg_host'],
                        'port': '5432'
                    }
        print("Connecting to the database...")
        self.conn = psycopg2.connect(**self.db_params)
        self.cur = self.conn.cursor()
        print("Connected to the database.")
        self.df = df
        

    def delete_existing_data(self, latest_date):
        print(f"Delete the data from {self.config['pg_schema']}.{self.config['pg_table']} if it exists for the date: {latest_date} and region: {self.region}")
        del_stmt = f"DELETE FROM {self.config['pg_schema']}.{self.config['pg_table']} WHERE filedate = '{latest_date}' and region = '{self.region}'"
        self.cur.execute(del_stmt)
        self.conn.commit()

    def data_exists(self, latest_date):
        query = f"""
            SELECT 1 FROM {self.config['pg_schema']}.{self.config['pg_table']}
            WHERE filedate = %s AND region = %s
        """
        self.cur.execute(query, (latest_date, self.region))
        return self.cur.fetchone() is not None
    
    def insert_data(self, df):
        insert_query = """
            INSERT INTO refdata.flex_sod_position (securitytype, custodian, fund, price, quotedccy, symbol, qty, currency, fxrate, strategy, accounttype, loadtimestamp, loaddate, filedate, region)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        print(f"Insert data into PostgreSQL table {self.config['pg_table']}")
       
        print(f"DF Columns: {df.columns}")
        df = df[['securitytype', 'custodian', 'fund', 'price', 'quotedccy', 'symbol', 'qty', 'currency', 'fxrate', 'strategy', 'accounttype', 'loadtimestamp', 'loaddate', 'filedate', 'region']]
        
        print("Insertion start.....")
        for index, row in df.iterrows():
            self.cur.execute(insert_query, tuple(row))
        print("insertion Done")
        # Commit the transaction
        self.conn.commit()
        print("Data inserted successfully.")
        print(f"Data loaded successfully.")

    def run(self, latest_date, latest_file):
        print(self.df)
        self.df.columns = self.df.columns.str.lower()
    
        if not self.data_exists(latest_date):
            self.insert_data(self.df)
            pass
        else:
            if self.rerun_with_delete == 'Y':
                print("Flag for Delete is set to Y")
                self.delete_existing_data(latest_date)
                self.insert_data(self.df)
            else:
                print(f"Data for date {latest_date} and region {self.region} already exists and re-run is also N. Skipping insertion.")
                raise Exception(f"Data for date {latest_date} and region {self.region} already exists. Re-run is also set to N. Skipping insertion.")
            
        config = fio.read_config_secrets()
        s3_location = config['eod_position_s3_feed_path']
        parsed_url = urlparse(s3_location)
        bucket_name = parsed_url.netloc
        s3_prefix = parsed_url.path.lstrip('/')
        s3_feed_path = f"{s3_prefix}/" + latest_file
        s3_client = boto3.client('s3')
        csv_buffer = io.StringIO()
        df.to_csv(csv_buffer, index=False)
        print(f"Bucket Name: {bucket_name}, Key: {s3_feed_path}")
        print(f"Buffer CSV Data: {csv_buffer}")
        s3_client.put_object(Bucket=bucket_name, Key=s3_feed_path, Body=csv_buffer.getvalue())


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process file pattern and region.")
    parser.add_argument("pattern", type=str, help="File pattern to match")
    parser.add_argument("region", type=str, help="Region to update")
    parser.add_argument("--rerun", type=str, required=False, default='N', help="Rerun the process")

    args = parser.parse_args()
    rerun = args.rerun
    region = args.region
    pattern = args.pattern
    print(f"Rerun the Process: {rerun}")
    loader = SFTPDataLoader(pattern, region)
    df, latest_date, latest_file = loader.run()
    print("Data read from source:")
    print(df)
    df_loader_obj = DfDataLoader(rerun_with_delete= rerun, df=df, region=region)
    df_loader_obj.run(latest_date, latest_file)
    
