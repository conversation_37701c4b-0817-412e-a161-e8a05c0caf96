{{ 
    config( 
        tags=["flash"]) 
}}


with weekend_flag as (
    select  
        case
            when date_part('DOW', convert_timezone('UTC', 'America/New_York', current_timestamp())::DATE) IN (0,6) THEN 1
            else 0
        end as is_weekend
),
yesterday_data as (
    select
        file_flag,
        count(*) as cnt
    from {{ ref('pub_ice_mft_options') }}
    where cast( trade_date  as date) = convert_timezone('UTC', 'America/New_York', current_timestamp())::DATE
    group by file_flag
),
all_flags as (
    select distinct file_flag
    from {{ ref('pub_ice_mft_options') }}
)
select 
    af.file_flag,
    yd.cnt,
    wf.is_weekend
from 
all_flags af 
left join yesterday_data yd 
on af.file_flag = yd.file_flag
join weekend_flag wf 
where wf.is_weekend = 0
and coalesce(yd.cnt, 0) = 0 and af.file_flag = 'Flash'