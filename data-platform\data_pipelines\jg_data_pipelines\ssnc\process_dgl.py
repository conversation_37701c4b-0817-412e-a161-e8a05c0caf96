import pandas as pd
import re
import zipfile
import tempfile
import logging
import os, sys,json
from datetime import datetime, timezone
sys.path.append(os.getcwd())
import json
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '../../../source/bin')))
from strunner import *
setupEnvironment()
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)
from utils.snowflake.adaptor import SnowflakeAdaptor

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def process_Strategy_data(file_path:str, fund_name:str, file_date:str):
    expect_currency = False
    capture_data=False
    capture_columns=False
    current_metadata={}
    period_start_date = None
    period_end_date = None
    knowledge_date = None
    currency = None
    metadata = []
    data = []
    temp_data=[]
    columns=[]
    
    with open(file_path,'r') as file:
        lines = file.readlines()
        for line in lines:
            line = line.strip()
            if line.startswith('"SSNC GlobeOp Detailed General Ledger with Strategy'):
                capture_data=False
                continue
    
            if line.startswith('"Jain Global '):
                current_fund_name = line.replace('"', '')
                continue
            
            elif line.startswith('"Period Start Date'):
                period_start_date = line.split(',')[1].replace('"', '').replace(':', ' ', 1)
                continue
            
            elif line.startswith('"Period End Date'):
                period_end_date = line.split(',')[1].replace('"', '').replace(':', ' ', 1)
                continue
            
            elif line.startswith('"Knowledge Date'):
                knowledge_date = line.split(',')[1].replace('"', '').replace(':', ' ', 1)
                expect_currency=True
                continue
                
            elif expect_currency and line.strip():
                currency = line.replace('"', '').strip()
                expect_currency=False
                capture_columns=True
                continue
            
            elif line.startswith('"Beginning Balance'):
                capture_columns=False
                current_metadata['Fund'] = fund_name
                current_metadata['Fund_Name'] = current_fund_name
                current_metadata['File_Date'] = file_date
                current_metadata['Period_Start_Date'] = period_start_date
                current_metadata['Period_End_Date'] = period_end_date
                current_metadata['Knowledge_Date'] = knowledge_date
                current_metadata['Currency'] = currency
                
                current_metadata['Asset_Type'] = line.split(',')[0].split('Beginning Balance - ')[1].replace('"', '').replace(':', ' ', 1)
                current_metadata['Beginning_Balance'] = line.split(',')[2].replace('"', '').replace(':', ' ', 1)
                capture_data=True
                continue
            
            elif line.startswith('"Ending Balance'):
                current_metadata['Ending_Balance'] = line.split(',')[2].replace('"', '').replace(':', ' ', 1)
                capture_data=False
                if current_metadata:
                    metadata.append(current_metadata)
                    if temp_data:
                        data.extend(temp_data)
                    current_metadata={}
                    temp_data = []
                continue
            
            if capture_columns and not columns:
                columns = line.split(',')
                capture_columns=False
            
            elif capture_data and line and not line.startswith('SSNC GlobeOp Detailed General Ledger with Strategy'):
                if columns:
                    columns[0] = "Asset_Type"
                    row=line.split('","')
                    temp_data.append({
                        'Fund': fund_name,
                        'File_Date': file_date,
                        'Fund_Name': current_metadata['Fund_Name'],
                        'Knowledge_Date': current_metadata['Knowledge_Date'],
                        **{columns[i].replace(' ', '_').replace('/', '_').replace('"', ''): row[i].replace('"', '').replace(',', '').strip() for i in range(len(columns)) if i != 1},
                    })
                continue
                    
        metadata_df = pd.DataFrame(metadata)
        data_df = pd.DataFrame(data)
        
        return metadata_df.drop_duplicates(), data_df.drop_duplicates()
    
def find_files_by_date(directory, target_date):
    target_date = datetime.strptime(target_date, "%Y-%m-%d").date()
    date_suffix = target_date.strftime("%m%d")

    # Iterate through files in the directory
    try:
        for filename in os.listdir(directory):
            if filename.startswith("DGL_Report_as_of_") and filename.endswith(f'{date_suffix}.zip'):
                filepath = os.path.join(directory, filename)
                file_timestamp = os.path.getmtime(filepath)
                file_date = datetime.fromtimestamp(file_timestamp).date()
                
                if file_date == target_date:
                    return f'{directory}/{filename}'
        raise FileNotFoundError(f"DGL file is missing for the date {target_date} in rawdata location {directory}")    
    except Exception as e:   
        logger.error(f"An error occurred: {e}")
        raise
        

if __name__ == "__main__":
    
    configPath = os.environ.get('CONFIG_PATH', os.getcwd())
    with open(f'{configPath}/config.json', 'r') as f:
        config = json.load(f)

    def jg_config_path():
        return config["JG_CONFIG_PATH"]
    
    def jg_rawdata_path():
        return config["JG_DATA_PATH"]

    def read_config_secrets():
        config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
        with open(config_secret_path, 'r') as f:
            config_secret = json.load(f)
        return config_secret
    objconfig = read_config_secrets()

    os.environ['SF_USERNAME'] = objconfig['sf_user']
    os.environ['SF_PASSWORD'] = objconfig['sf_password']
    os.environ['SF_DATABASE'] = objconfig['ssnc_database']
    os.environ['SF_WAREHOUSE'] = objconfig['ssnc_warehouse']
    os.environ['SF_ROLE'] = objconfig['ssnc_role']
    os.environ['SF_SCHEMA'] = schema = objconfig['ssnc_schema']
    
    schema_name = objconfig['ssnc_schema']
    balance_table = f'{schema_name}.DGL_BALANCES'
    transactions_table = f'{schema_name}.DGL_TRANSACTIONS'
    stage_name=f'{schema_name}.STG_TRANSACTIONS'
    
    jfs_rawdata_location = f'{jg_rawdata_path()}/SSandC'
    sf_adaptor = SnowflakeAdaptor(database=objconfig['ssnc_database'], warehouse=objconfig['ssnc_warehouse'], role=objconfig['ssnc_role'])   
    sf_adaptor.execute_query(schema_name, f'TRUNCATE {transactions_table}')
    
    # get file path of the DGL Strategy file downloaded in rawdata location
    date = datetime.now(timezone.utc).date().strftime('%Y-%m-%d')
    zip_file_path = find_files_by_date(jfs_rawdata_location, date)
    
    logger.info(f"Processing DGL Strategy ZIP file:: {zip_file_path}")

    with tempfile.TemporaryDirectory() as temp_dir:
        # Unzip and extract
        with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)

        # Loop through extracted files
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                logger.info(f"\n\nProcessing file: {file}")
                parts = file.split("_")
                fund_name = parts[-2]
                file_date = parts[-1].split('.')[0]
                
                month = file_date[:2]
                day = file_date[2:4]
                year = file_date[4:]
    
                formatted_file_date = f"{year}-{month}-{day}"
                
                balances_df, data_df = process_Strategy_data(f'{temp_dir}/{file}', fund_name, formatted_file_date)
                
                sf_adaptor.upsert_df(balances_df, schema_name, balance_table, match_columns=['Fund', 'Knowledge_Date', 'Asset_Type'])
                sf_adaptor.load_df(data_df, file, schema_name, transactions_table, stage_name)
