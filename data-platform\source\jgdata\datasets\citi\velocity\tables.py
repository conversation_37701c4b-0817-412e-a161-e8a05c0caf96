import numpy as np
import pandas as pd
from datetime import date
import sys

from jgdata.datasets.citi.velocity import *
from jgdata.datasets.citi.velocity.__defs__ import *
from jgdata.datasets.citi.velocity.util import *
from stcommon.infra.rds.snowflake_operation import *

def get_expressions(expr_type, obj_sf, config_table, schedule_type):
    config_df = obj_sf.fetch_query(f"SELECT * FROM {config_table} WHERE DATERANGE = '{expr_type}' AND SCHEDULE = '{schedule_type}' AND IS_ACTIVE != False")
    return config_df['EXPRESSION'].tolist()

def insert_to_snowflake(obj_sf, df, output_table):
  print("\nInserting data into snowlake table")
  obj_sf.insert_dataframe(df, output_table)
  
def buildIntraDayTBAData(d, config_table, output_table, schedule_type):
    obj_sf = SnowflakeDML("CITI")
    expressions_list = get_expressions('intra_day', obj_sf, config_table, schedule_type)
    dataFromAPI = getIntraDayTickerData(expressions_list)
    buildTableTBA(d, obj_sf, dataFromAPI, output_table)
    
def buildHistTBAData(d, config_table, output_table, schedule_type):
    obj_sf = SnowflakeDML("CITI")
    expressions_list = get_expressions('10D', obj_sf, config_table, schedule_type)
    dataFromAPI = getData(d, '10D', expressions_list)
    buildTableTBA(d, obj_sf, dataFromAPI, output_table)
    
def build_citi_velocity_adhoc(config_table, output_table, ticker_list):
    from datetime import datetime
    import time
    obj_sf = SnowflakeDML("CITI")
    ticker_list_arr = ticker_list.split(',')
    formatted_tickers = ",".join(f"'{ticker.strip()}'" for ticker in ticker_list_arr)
    df = obj_sf.fetch_query(f"""
                            SELECT  DATE(CURRENT_TIMESTAMP()) as END_DATE, 
                                    DATE(CURRENT_TIMESTAMP()) as START_DATE,
                                    EXPRESSION,
                                    DATERANGE, 
                            FROM    {config_table} sc
                            WHERE   EXPRESSION IN ({formatted_tickers})
                    """)
    
    d = calendar.getCurrentBusDate('gl',calendar.getToday('gl'))
    expressions_by_daterange = {}
    
    for daterange in df['DATERANGE'].unique():
        expressions_by_daterange[daterange] = df[df['DATERANGE'] == daterange]['EXPRESSION'].tolist()
    
    for daterange, expressions in expressions_by_daterange.items():
        if daterange=='intra_day':
            dataFromAPI = getIntraDayTickerData(expressions)
            buildTableTBA(d, obj_sf, dataFromAPI, output_table)
        else:
            dataFromAPI = getData(d, daterange, expressions)
            buildTableTBA(d, obj_sf, dataFromAPI, output_table)

def buildTableTBA(ed, obj_sf, apiResponse, output_table):
    tbaData = pd.DataFrame(columns=['REF_DATE', 'EXPRESSION', 'VALUE', 'AVAILDATE', 'DATE'])
    dataFromAPI = pd.DataFrame(columns=['REF_DATE', 'EXPRESSION', 'VALUE'])
    existing_df = pd.DataFrame(columns=['REF_DATE', 'EXPRESSION', 'VALUE'])
    dataFromAPI = apiResponse
    
    dataFromAPI.rename(columns = {'ref_date':'REF_DATE'}, inplace=True)
    dataFromAPI.rename(columns = {'expression':'EXPRESSION'}, inplace=True)
    dataFromAPI.rename(columns = {'value':'VALUE'}, inplace=True)
    
    latest_records = obj_sf.get_latest_record(output_table, 'AVAILDATE', ['REF_DATE', 'EXPRESSION'])
    
    existing_df = latest_records[['REF_DATE', 'EXPRESSION', 'VALUE']]
    existing_df['REF_DATE'] = pd.to_datetime(existing_df['REF_DATE']).dt.strftime('%Y%m%d').astype('int64')
    existing_df['EXPRESSION'] = existing_df['EXPRESSION'].astype('object')
    existing_df['VALUE'] = existing_df['VALUE'].astype('float64')

    if existing_df.empty:
        df_toBeLoaded = dataFromAPI.copy()
    else:
        df_merged = dataFromAPI.merge(existing_df, on=['REF_DATE', 'EXPRESSION'], how='left', indicator=True)
        
        tolerance = 0.0001
        df_merged['value_diff'] = df_merged['VALUE_x'] - df_merged['VALUE_y']
        df_toBeLoaded = df_merged[(df_merged['_merge'] == 'left_only') | ((df_merged['_merge'] == 'both') & (df_merged['value_diff'].abs() > tolerance))].drop(columns=['_merge', 'VALUE_y', 'value_diff'])
        df_toBeLoaded.rename(columns = {'VALUE_x':'VALUE'}, inplace=True)
        
    
    # Start: Logic for DQ Exception:
    try:

        df_error = df_toBeLoaded[
        (df_merged['_merge'] == 'both') &
        (df_merged['value_diff'].abs() > tolerance) & 
        (df_merged['REF_DATE'] < ed)]
        print(f"DF Error: {df_error}")


        if df_error is not None and not df_error.empty:
            query_dq_check = f'''
                    SELECT DISTINCT
                        PARSE_JSON(config_parameters):schema_name::STRING AS schema_name,
                        PARSE_JSON(config_parameters):mailist::STRING AS mailist
                    FROM   DATA_PLATFORM_CORE.EXCEPTIONS.CHECK_CONFIG cc
                    where     cc.check_cd = 'HISTORY_CHANGE_CHECK'
                    AND    cc.APPLICATION_CD  = 'APACST_CITI'
                    '''
            print(query_dq_check)
            df_dq_check_config = obj_sf.fetch_query(query_dq_check)

            for index, row in df_dq_check_config.iterrows():
                table_name = f"{row['SCHEMA_NAME']}.CITI_VELOCITY_CONFIG"
                maillist = str(row['MAILIST'])
                maillist = maillist.split(",")

                query_expression_list = f'''
                        SELECT DISTINCT
                               expression
                        FROM   {table_name}
                        '''
                print(query_expression_list)
                df_db_expression_list = obj_sf.fetch_query(query_expression_list)
                db_expression_list = df_db_expression_list['EXPRESSION'].tolist()

                df_error = df_error[df_error['EXPRESSION'].isin(db_expression_list)]

                if df_error is not None and not df_error.empty:
                    body = ("Hi team,<br><br>Please find the below tickers which changes its value from last snaped value.<br>")
                    body += format_dataframe_html(df_error, "Exception Details")

                    email_util.send_email(
                        to_recipient = maillist,
                        subject=f"Citi Velocity Value Changes Alert",
                        body=body,
                        df=None
                    )
    except Exception as e:
        print(f"Mail Exception encountered: {str(e)}")    
    # END: Logic for DQ Exception
    
    tbaData = df_toBeLoaded.sort_values(by='REF_DATE').reset_index(drop=True)
    tbaData['date'] = tbaData['REF_DATE']
    tbaData['availDate'] = pd.Timestamp.utcnow()

    df_to_be_loaded = tbaData.drop(columns=['date'])

    df_to_be_loaded.rename(columns = {'availDate':'AVAILDATE'}, inplace=True)
    df_to_be_loaded['AVAILDATE'] = pd.to_datetime(df_to_be_loaded['AVAILDATE']).dt.strftime('%Y-%m-%d %H:%M:%S')
    df_to_be_loaded['REF_DATE'] = pd.to_datetime(df_to_be_loaded['REF_DATE'].astype(str),format='%Y%m%d')
    df_to_be_loaded['REF_DATE'] = df_to_be_loaded['REF_DATE'].dt.strftime('%Y-%m-%d %H:%M:%S')

    insert_to_snowflake(obj_sf, df_to_be_loaded, output_table)

    return tbaData
