from confluent_kafka import Consumer, KafkaError, TopicPartition
import json
import time
from datetime import datetime, timedelta
import os, sys
from snowflake_connection import *
import yaml

print("Starting the script")

def read_config_secrets():
    config_secret_path = os.path.join('/jfs/tech1/apps/datait/jg-code/secure/prod', 'config_secret.json')
    with open(config_secret_path, 'r') as f:
        config_secret = json.load(f)
    return config_secret

def get_kafka_config():
    config_secret = read_config_secrets()
    bootstrap_server = config_secret['kafka_bootstrap_prod']
    group_id = "validation_1"
    security_protocol = config_secret['kafka_sec_protocol']
    kakfa_sasl_username = config_secret['kakfa_sasl_user']
    kafka_sasl_password = config_secret['kafka_sasl_pass']
    kafka_sasl_mechanism = config_secret['kafka_sasl_mech']
    return {
        'bootstrap.servers': bootstrap_server,
        'group.id': group_id,
        'auto.offset.reset': 'latest',
        'security.protocol': security_protocol,
        'sasl.username': kakfa_sasl_username,
        'sasl.password': kafka_sasl_password,
        'sasl.mechanisms': kafka_sasl_mechanism,
        "enable.ssl.certificate.verification": False,
    }
 

consumer_config = get_kafka_config()

exporter_file = "/etc/node_exporter/kafka_snowflake_offset.prom"
config_file = "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/conf/connectors_services_process.yaml"

kafka_messages = {}
snowflake_records = {}
result = ''

def parse_config():
    try:
        hosts_set = set()
        properties_set = set()
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)  # Load YAML safely
        if not config_data or 'connectors' not in config_data:
            raise ValueError("Invalid configuration format: 'connectors' key missing")
        for connector in config_data['connectors']:
            # Extract server(s) and update the set
            servers = connector.get('server', '')
            if isinstance(servers, str):
                hosts_set.update(servers.split(','))  # Handle comma-separated servers
            elif isinstance(servers, list):
                hosts_set.update(servers)  # Directly add if it's a list
            # Extract property location and update the set
            property_loc = connector.get('property_loc', '').strip()
            if property_loc:
                properties_set.add(property_loc)
        return list(hosts_set), list(properties_set)
    except Exception as e:
        print(f"Error reading config file: {e}")
        return [], []

def read_property_file(property_file):
    properties = {}
    try:
        with open(property_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    properties[key.strip()] = value.strip() 
        properties["topic"] = properties.get("topics", "").split(',')
        properties["db_name"] = properties.get("snowflake.database.name", "")
        properties["schema"] = properties.get("snowflake.schema.name", "")
        properties["table"] = properties.get("snowflake.topic2table.map", "").split(':')[-1]
    except Exception as e:
        print(f"Error reading property file {property_file}: {e}")
    return properties

def get_minute_timestamps():
    now = datetime.now()
    lower_limit = now.replace(second=0, microsecond=0).strftime('%Y-%m-%d %H:%M:%S')
    upper_limit = (now.replace(second=0, microsecond=0) + timedelta(minutes=1) - timedelta(seconds=1)).strftime('%Y-%m-%d %H:%M:%S')
    return lower_limit, upper_limit

def get_latest_kafka_message(consumer, topics):
    try:
        partitions = []
        for topic in topics:
            metadata = consumer.list_topics(topic)
            for partition in metadata.topics[topic].partitions:
                partitions.append(TopicPartition(topic, partition))
        watermarks = {tp: consumer.get_watermark_offsets(tp) for tp in partitions}
        latest_offsets = {tp: watermarks[tp][1] - 1 for tp in partitions if watermarks[tp][1] > 0}
        for tp, offset in latest_offsets.items():
            # consumer.assign([TopicPartition(tp.topic, tp.partition, offset)])
            # msg = consumer.poll(timeout=1.0)  # Poll after assigning offset
            # if msg and not msg.error():
            # timestamp_type, timestamp_ms = msg.timestamp() 
            kafka_messages[tp.topic] = {
                'topic': tp.topic,
                'partition': tp.partition,
                'offset': offset,
            }
            print(f"Latest message from {tp.topic} (Partition {tp.partition}): Offset={offset}")
    except Exception as e:
        print(f"Error fetching latest messages: {e}")

def get_latest_kafka_message_for_partition(consumer, topic, partition):
    try:
        metadata = consumer.list_topics(topic, timeout=5.0)
        if topic not in metadata.topics:
            print(f"Topic '{topic}' not found in Kafka.")
            return None
        if partition not in metadata.topics[topic].partitions:
            print(f"Partition {partition} not found in topic '{topic}'.")
            return None

        tp = TopicPartition(topic, partition)
        low, high = consumer.get_watermark_offsets(tp)
        if high > 0:
            return {
                'topic': topic,
                'partition': partition,
                'offset': high - 1,
            }
    except Exception as e:
        print(f"Error fetching latest offset from Kafka: {e}")
    return None

def get_latest_snowflake_record(db_name, schema, table, obj_sf):
    query = f'''SELECT 
                    RECORD_METADATA:topic AS topic, 
                    RECORD_METADATA:partition AS partition, 
                    RECORD_METADATA:offset AS offset, 
                    RECORD_METADATA:CreateTime AS createtime
                FROM {db_name}.{schema}.{table}
                    WHERE RECORD_METADATA:CreateTime IS NOT NULL 
                    AND RECORD_METADATA:offset IS NOT NULL
                ORDER BY createtime DESC LIMIT 1
            '''
    df = obj_sf.fetch_query(query)
    return df

def compare_kafka_snowflake(kafka_messages, snowflake_records, topics):
    global result
    result = ''
    for topic in topics:
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        kafka_data = kafka_messages.get(topic)
        snowflake_data = snowflake_records.get(topic)
        
        if not kafka_data and not snowflake_data:
            metric_line = f'kafka_snowflake_offset{{topic="{topic}", current_time="{current_time}", found="0", status="Missing from Kafka and Snowflake", kafka_partition="-1", kafka_offset="-1", snowflake_partition="-1", snowflake_offset="-1"}} -1\n'
        elif not kafka_data:
            metric_line = f'kafka_snowflake_offset{{topic="{topic}", current_time="{current_time}", found="0", status="Messages are only present in Snowflake, missing in Kafka", kafka_partition="-1", kafka_offset="-1", snowflake_partition="{snowflake_data[0]["partition".upper()]}", snowflake_offset="{snowflake_data[0]["offset".upper()]}"}} -1\n'
        elif not snowflake_data:
            metric_line = f'kafka_snowflake_offset{{topic="{topic}", current_time="{current_time}", found="0", status="Messages are only present in Kafka, missing in Snowflake", kafka_partition="{kafka_data["partition"]}", kafka_offset="{kafka_data["offset"]}", snowflake_partition="-1", snowflake_offset="-1"}} -1\n'
        else:
            offset_difference = abs(int(kafka_data["offset"]) - int(snowflake_data[0]["offset".upper()]))
            metric_line = (
                f'kafka_snowflake_offset{{topic="{topic}", current_time="{current_time}", found="1", status="Messages are consumed from Kafka and present in Snowflake", kafka_partition="{kafka_data["partition"]}", kafka_offset="{kafka_data["offset"]}", snowflake_partition="{snowflake_data[0]["partition".upper()]}", snowflake_offset="{snowflake_data[0]["offset".upper()]}"}} {offset_difference}\n'
            )
        result += metric_line

def write_to_node_exporter(result):
    try:
        with open(exporter_file, "w") as f:
            print(f"Metrics :{result}")
            f.write(result)
        print(f"Metrics written to {exporter_file}")
    except Exception as e:
        print(f"Error writing to Node Exporter file: {e}")

hosts, properties = parse_config()

modified_properties = []
for property_file in properties:
    result = read_property_file(property_file)
    if result:
        modified_properties.append(result)
        
topics = [', '.join(properties['topic']) for properties in modified_properties]
print(f"Parsed topics: {topics}")

if __name__ == '__main__':
    try:
        last_run_time = time.time()
        
        while True:
            current_time = time.time()
            
            if current_time - last_run_time >= 60:
                try:
                    print("Initializing Kafka consumer...")
                    consumer = Consumer(consumer_config)
                    # get_latest_kafka_message(consumer, topics)
                except Exception as e:
                    print(f"Error initializing Kafka consumer: {e}")
                
                try:
                    print("Getting Records From Snowflake...")
                    sf_record_columns = ['topic','partition','offset','createtime']
                    obj_sf = SnowflakeDML('JPM')
                    for properties in modified_properties:
                        db_name = properties['db_name']
                        schema = properties['schema']
                        table = properties['table']
                        topic_list = properties['topic']

                        for topic in topic_list:
                            try:
                                sf_df = get_latest_snowflake_record(db_name, schema, table, obj_sf)
                                if not sf_df.empty:
                                    sf_record = sf_df.iloc[0]
                                    sf_topic = str(sf_record['TOPIC']).strip().strip('"')
                                    sf_partition = int(sf_record['PARTITION'])
                                    sf_offset = int(sf_record['OFFSET'])

                                    kafka_record = get_latest_kafka_message_for_partition(consumer, sf_topic, sf_partition)

                                    if kafka_record:
                                        kafka_messages[sf_topic] = {
                                            'topic': sf_topic,
                                            'partition': sf_partition,
                                            'offset': kafka_record['offset']
                                        }
                                        snowflake_records[sf_topic] = [{
                                            'TOPIC': sf_topic,
                                            'PARTITION': sf_partition,
                                            'OFFSET': sf_offset
                                        }]
                            except Exception as e:
                                print(f"Error comparing Kafka and Snowflake for topic {topic}: {e}")
                except Exception as e:
                    print(f"Error getting records from snowflake: {e}")
                
                compare_kafka_snowflake(kafka_messages, snowflake_records, topics)
                write_to_node_exporter(result)
                kafka_messages.clear()
                snowflake_records.clear()
                result = ''
                
                last_run_time = current_time
            else:
                time.sleep(1)

    except KeyboardInterrupt:
        print("Stopping consumer")
    finally:
        consumer.close()
        print("Kafka consumer closed.")
