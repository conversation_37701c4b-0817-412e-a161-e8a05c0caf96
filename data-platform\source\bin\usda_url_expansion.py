import argparse
import json
import os
import requests
import csv
from strunner import *
from tqdm import tqdm
setupEnvironment() 
from stcommon.infra.rds.snowflake_bulk_loader import SnowflakeBulkLoader

parser = argparse.ArgumentParser(description="Process previous date")
parser.add_argument("--date", required=True, help="Specify the processing date in format YYYYMMDD")
args = parser.parse_args()
current_date = args.date
path="/jfs/tech1/apps/rawdata/usda_agri"


def read_config_secrets():
    config_secret_path = os.path.join('/jfs/tech1/apps/datait/jg-code/secure/prod', 'config_secret.json')
    with open(config_secret_path, 'r') as f:
        config_secret = json.load(f)
    return config_secret

config=read_config_secrets()
api_key=config["api_key"]
raw_db=config["usda_raw_db"]
commod_uat_schema=config["usda_commod_schema_uat"]
commod_schema=config["usda_commod_schema"]

conn = SnowflakeBulkLoader(warehouse="DATA_PLATFORM_WH",database=raw_db,schema=f'{raw_db}.{commod_schema}',role="FR_DATA_PLATFORM")
cur = conn.cursor

cur.callproc(f'{raw_db}.{commod_schema}.identify_usda_urls',[])
result=cur.fetchone()
print("Procedure_executed: ",result[0])
query1=f"select * from {raw_db}.{commod_schema}.USDA_RUN_HISTORY  where status=  'NEW' "
res=cur.execute(query1)
i=0
rows=res.fetch_arrow_all()
tables_names=[]
if rows:
    tables_names=rows.column("TABLE_NAME").to_pylist()
    tables_names=list(set(tables_names))

for table in tables_names:
    print("Table Name:", table)
    query_new=query1+f" and table_name='{table}'"
    print("Query:", query_new)
    tables_url=cur.execute(query_new)
    results=tables_url.fetch_arrow_all()
    if results:
        urls= results.column("URL").to_pylist()
    
        final_data=[]
        for url in tqdm(urls):
            url_res=requests.get(url,headers={'x-api-key':api_key[i]})
            if url_res.status_code==429:
                while url_res.status_code!=200:
                    if i<len(api_key)-1:
                        i=i+1
                    else:
                        i=0
                    url_res=requests.get(url,headers={'x-api-key':api_key[i]})
            if table =="esr_exports":
                final_data.extend([{**item,'market_year': url[-4:]} for  item in  url_res.json()])
            else:
                final_data.extend(url_res.json())

            update_query= f"UPDATE {raw_db}.{commod_schema}.USDA_RUN_HISTORY SET status='DONE'  , time_stamp = current_timestamp where status=  'NEW' and url='{url}' "        
            cur.execute(update_query)


    if final_data :
    
        with open(f'{path}/{table}_{current_date}.csv', 'w', newline='', encoding='utf-8') as output_file:
                dict_writer = csv.DictWriter(output_file, fieldnames=final_data[0].keys())
                dict_writer.writeheader()
                dict_writer.writerows(final_data)
        print(f"DATA SAVED: {path}/{table}_{current_date}.csv")