[Unit]
Description=SpiderRock Expanded Universe Snaps.
After=network.target

[Service]
Type=oneshot
EnvironmentFile=/jfs/tech1/apps/datait/jg-code/secure/prod/jg_data_pipelines.env
ExecStart=/bin/bash -c ' \
export MAMBA_EXE="/jfs/tools/conda/exec/micromamba"; \
export MAMBA_ROOT_PREFIX="/jfs/tech1/conda/"; \
__mamba_setup="$("$MAMBA_EXE" shell hook --shell bash --root-prefix "$MAMBA_ROOT_PREFIX" 2> /dev/null)"; \
eval "$__mamba_setup"; \
unset __mamba_setup; \
micromamba activate tech1-datait-analytics-light; \
python /jfs/tech1/apps/datait/jg-code/prod/data-platform/data_pipelines/jg_data_pipelines/spiderrock/run_spiderrock.py >> /opt/data/process_logs/fe_risk_sr_full_univ_snap_$(date +%%Y%%m%%d).log 2>&1'
Restart=no

[Install]
WantedBy=multi-user.target