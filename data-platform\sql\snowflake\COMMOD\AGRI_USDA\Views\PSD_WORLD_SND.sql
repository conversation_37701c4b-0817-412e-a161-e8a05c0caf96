CREATE OR <PERSON><PERSON><PERSON>CE VIEW COMMOD.AGRI_USDA.PSD_WORLD_SND AS 
SELECT
	PSD.COMMODITY_CODE,
	COM.COMMODITY_NAME,
	PSD.COUNTRY_CODE,
	PSD.MARKET_YEAR,
	PSD.<PERSON><PERSON><PERSON>AR_YEAR,
	PSD.MONTH,
	PSD.ATTRIBUTE_ID,
	ATTR.ATTRIBUTE_NAME,
	PSD.UNIT_ID,
	UOM.UNIT_DESCRIPTION,
	PSD.VALUE,
	PSD.FILE_NAME,
	PSD.TIME_STAMP
FROM
	VENDOR_RAW.USDA_COMMOD.PSD_WORLD_SND_RAW PSD
LEFT JOIN VENDOR_RAW.USDA_REFERENCE.PSD_COMMODITY_RAW COM 
ON
	COM.COMMODITY_CODE = PSD.COMMODITY_CODE
LEFT JOIN VENDOR_RAW.USDA_REFERENCE.PSD_ATTRIBUTE_RAW ATTR
ON
	ATTR.ATTRIBUTE_ID = PSD.ATTRIBUTE_ID
LEFT JOIN VENDOR_RAW.USDA_REFERENCE.PSD_UOM_RAW UOM
ON
	UOM.UNIT_ID = PSD.UNIT_ID