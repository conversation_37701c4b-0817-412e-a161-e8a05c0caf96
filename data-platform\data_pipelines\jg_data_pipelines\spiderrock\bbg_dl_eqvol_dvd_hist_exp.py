import os
import time
import json
from io import StringIO
import pandas as pd
import logging
import psycopg2
from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from utils.postgres.adaptor import PostgresAdaptor

from bloomberg.per_security.parser import BloombergParser
from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.date_utils import get_today, get_now

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    pg_host = "apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com"
    pg_database = "fe_risk"
    pg_schema = "eqvol"
    pg_username = os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"]
    pg_password = os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"]

    pg_adaptor = PostgresAdaptor(
        host=pg_host,
        database=pg_database,
        schema=pg_schema,
        user=pg_username,
        password=pg_password,
    )

    df_etfs = pg_adaptor.execute_query(
        """
            select distinct TRIM(std.bbgcompositeticker) || ' Equity' as bbg_full_ticker
            from eqvol.ticker_request_config trc join 
            eqvol.sr_ticker_definition std on trc.ticker = std.ticker
            where std.symboltype in ('Equity', 'ETF') and 
            trc.is_bbg_div_enabled = true and std.bbgcompositeticker is not null;
        """,
    )

    securities = df_etfs["bbg_full_ticker"].tolist()
    per_sec_req_type = PerSecurityRequestType.getdata
    batch_name = "dp_divh"
    request_dict = {
        "firm_name": "dl47544",
        "program_flag": "adhoc",
        "sec_id": "TICKER",
        "output_format": "bulklist",
        "fields": ["DVD_HIST_ALL"],
        "securities": securities,
    }
    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/eqvol/dvd_hist/"
    dvd_hist_file_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)
    print(dvd_hist_file_path)

    as_of_date = get_today()
    parser = BloombergParser(
        dvd_hist_file_path, sep="|", skipinitialspace=True, on_bad_lines="error"
    )
    df_data = parser.parse_data()
    df_data["BC_EQY_DVD_HIST_ALL_ANN_DT"] = pd.to_datetime(
        df_data["BC_EQY_DVD_HIST_ALL_ANN_DT"], format="%m/%d/%Y", errors="coerce"
    )
    df_data["BC_EQY_DVD_HIST_ALL_EX_DT"] = pd.to_datetime(
        df_data["BC_EQY_DVD_HIST_ALL_EX_DT"], format="%m/%d/%Y", errors="coerce"
    )
    df_data["BC_EQY_DVD_HIST_ALL_REC_DT"] = pd.to_datetime(
        df_data["BC_EQY_DVD_HIST_ALL_REC_DT"], format="%m/%d/%Y", errors="coerce"
    )
    df_data["BC_EQY_DVD_HIST_ALL_PAY_DT"] = pd.to_datetime(
        df_data["BC_EQY_DVD_HIST_ALL_PAY_DT"], format="%m/%d/%Y", errors="coerce"
    )
    df_data["BC_EQY_DVD_HIST_ALL_AMT"] = pd.to_numeric(
        df_data["BC_EQY_DVD_HIST_ALL_AMT"], errors="coerce"
    )
    df_data["SECURITY"] = df_data["SECURITY"].str.strip()
    df_data["AS_OF_DATE"] = as_of_date
    df_data.rename(columns={"SECURITY": "BBG_TICKER"}, inplace=True)

    for col in df_data.columns:
        if pd.api.types.is_datetime64_any_dtype(df_data[col]):
            df_data[col] = df_data[col].apply(
                lambda x: x.strftime("%Y-%m-%d") if pd.notna(x) else None
            )

    target_schema = "BBGH_ONDEMAND"
    target_table = "BBG_EQVOL_DVD_HIST_EXP"
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )

    as_of_date = as_of_date.strftime("%Y-%m-%d")
    sf_adaptor.execute_query(
        "BBGH_ONDEMAND",
        f"delete from {target_schema}.{target_table} where AS_OF_DATE = '{as_of_date}'",
    )
    sf_adaptor.write_pandas_dataframe(target_schema, df_data, target_table)

    df_data.rename(
        columns={
            "BBG_TICKER": "bbg_ticker",
            "AS_OF_DATE": "as_of_date",
            "BC_EQY_DVD_HIST_ALL_ANN_DT": "bc_eqy_dvd_hist_all_ann_dt",
            "BC_EQY_DVD_HIST_ALL_EX_DT": "bc_eqy_dvd_hist_all_ex_dt",
            "BC_EQY_DVD_HIST_ALL_REC_DT": "bc_eqy_dvd_hist_all_rec_dt",
            "BC_EQY_DVD_HIST_ALL_PAY_DT": "bc_eqy_dvd_hist_all_pay_dt",
            "BC_EQY_DVD_HIST_ALL_AMT": "bc_eqy_dvd_hist_all_amt",
            "CP_DVD_TYP": "cp_dvd_typ",
            "DVD_FREQ": "dvd_freq",
        },
        inplace=True,
    )

    df_data["when_updated"] = get_now("UTC")

    start_time = time.time()

    buffer = StringIO()
    df_data.to_csv(buffer, index=False, header=True)
    end_time = time.time()
    logger.info(f"Time taken for buffer stream: {end_time - start_time:.4f} secs")
    
    start_time = time.time()
    buffer.seek(0)
    column_list = ", ".join(list(df_data.columns))
    conn_string = f"host={pg_host} dbname={pg_database} user={pg_username} password={pg_password} port=5432"
    with psycopg2.connect(conn_string) as conn:
        with conn.cursor() as cursor:
            cursor.execute("SET work_mem TO '1GB'")
            cursor.execute("TRUNCATE TABLE eqvol.bbg_eqvol_dvd_hist_exp")
            copy_sql = f"""COPY eqvol.bbg_eqvol_dvd_hist_exp ({column_list}) FROM STDIN WITH (FORMAT CSV, HEADER)"""
            cursor.copy_expert(copy_sql, buffer)

        conn.commit()
    end_time = time.time()
    logger.info(f"Time taken for saving bbg_eqvol_dvd_hist_exp: {end_time - start_time:.4f} secs")