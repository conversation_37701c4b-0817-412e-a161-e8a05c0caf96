{{
    config(
        tags=['gtt_trade_details'],
        materialized='table',
    )
}}

WITH parsed AS (
  SELECT
    *,
    CASE
      WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
      THEN TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD')
      ELSE NULL
    END AS file_dt,
    CASE WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
         THEN EXTRACT(year  FROM TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD'))
    END AS file_year,
    CASE WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
         THEN EXTRACT(month FROM TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD'))
    END AS file_month,
    CASE WHEN REGEXP_LIKE(filename, '\\d{8}\\.csv$')
         THEN EXTRACT(day   FROM TO_DATE(REGEXP_SUBSTR(filename, '\\d{8}(?=\\.csv$)'), 'YYYYMMDD'))
    END AS file_day
  FROM {{ ref("stg_gtt_trade_details_export") }}
),
dedup AS (
  SELECT
    parsed.*,
    CASE
      WHEN file_dt IS NULL
      THEN 1
      ELSE ROW_NUMBER() OVER (
             PARTITION BY file_year, file_month
             ORDER BY file_day DESC
           )
    END AS rn
  FROM parsed
)
SELECT
  tradeflow,
  ismirror,
  periodtype,
  year_start,
  month_start,
  year_end,
  month_end,
  year,
  month,
  reportercountryno,
  reportercode,
  reporterisoalpha3code,
  reporterisonumeric3code,
  reportername,
  reporterdescription,
  firstavailableperiod,
  lastavailableperiod,
  reportersource,
  incoterm,
  partnercountryno,
  partnercode,
  partnerisoalpha3code,
  partnerisonumeric3code,
  partnername,
  hscode,
  commoditydescriptionoriginal,
  commoditydescriptiontranslation,
  hs2code,
  hs2descriptionoriginal,
  hs2descriptiontranslation,
  hs4code,
  hs4descriptionoriginal,
  hs4descriptiontranslation,
  hs6code,
  hs6descriptionoriginal,
  hs6descriptiontranslation,
  commodityexplodedhscode,
  commodityexplodeddescriptionoriginal,
  commodityexplodeddescriptiontranslation,
  hseditionfrom,
  hseditionto,
  currency,
  value,
  unit1,
  quantity1,
  q1estimation,
  unit2,
  quantity2,
  q2estimation,
  priceunit1,
  price1,
  transport,
  subdivision,
  port,
  priceunit2,
  price2,
  customsregime,
  foreignport,
  suppression,
  usstate,
  to_date(year || '-' || month || '-' || 1) AS trade_date
FROM dedup
WHERE rn = 1