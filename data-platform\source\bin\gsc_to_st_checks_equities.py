import logging
import pandas as pd
import os, sys, argparse
from glob import glob
import snowflake.connector
from datetime import datetime, timedelta
import tempfile
import time

logger = logging.getLogger()
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root_temp = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.append(project_root_temp)
logging.basicConfig(encoding='utf-8', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',datefmt='%Y-%m-%d %H:%M:%S')

from strunner import *
setupEnvironment()
config_root= os.environ.get('JGDATA_PATH')
sys.path.append(config_root)
import jgdata
import jglib.infra.python.fileio as fio
from jgdata.datasets.bloomberg.util.parser import parse
from stcommon.email_util_k8s import EmailUtility
from stcommon.infra.python.fileio import read_toml

def sf_query(conn,query):
    logging.getLogger('snowflake.connector').setLevel(logging.WARNING)
    cs = conn.cursor()
    cs.execute(query)
    cs.close()

def loadSTData(conn,DATABASE,SCHEMA,filename,region,nowait=False):
    stage_name = f'@{DATABASE}.{SCHEMA}.ST_LISTINGS'
    target_table = f'{DATABASE}.{SCHEMA}.ST_LISTINGS'
    logger.info(f"Uploading: {filename}")

    # Extract base name and add region suffix
    base_name = os.path.basename(filename)
    dest_file = base_name+"_"+region
    
    put_command = f"PUT file://{filename} {stage_name}/{dest_file} AUTO_COMPRESS=TRUE"

    max_wait_minutes = 20
    check_interval_seconds = 30  # Check every 30 seconds
    start_time = time.time()
    while time.time() - start_time < max_wait_minutes * 60:
        if os.path.exists(filename):
            break
        else:
            logger.info(f"? Waiting for file: {filename}")
            if nowait:
                logger.info(f"Will not fait for file: {filename}, since nowait is passed")
                return
        time.sleep(check_interval_seconds)
        
    ## Snowflake put will fail if file not available.
    sf_query(conn,put_command)
    # Step 2: Copy from stage into table
    copy_command = f"""
            COPY INTO {target_table}_RAW
            FROM (
            SELECT 
                $1,  -- the entire row as VARIANT
                METADATA$FILENAME
                FROM {stage_name} t
            )
            FILE_FORMAT = (TYPE = 'PARQUET');
    """
    logger.info(copy_command)
    sf_query(conn,copy_command)
    logger.info("Data loaded successfully.")
    final_insert = f"""
        INSERT INTO {target_table} (
            date,                         availdate,                        uid,                       sedol,
            isin,                         cusip,                            cins,                      tradingitemid,
            securityid,                   companyid,                        gvkey,                     bbcmpid,
            barraid,                      rootid,                           primaryfigi,               tickerandexchcode,
            compositefigi,                exchcode,                         tradingfigi,               tradingtickerandexchcode,
            idbbunique,                   quotepermid,                      instrpermid,               estpermid,
            wscode,                       wsid,                             ric,                       ibesticker,
            seccode,                      timestampsaved,                   source_file
        )
        SELECT
            data:"date"::STRING,          data:"availDate"::STRING,         data:"uid"::STRING,         data:"sedol"::STRING,
            data:"isin"::STRING,          data:"cusip"::STRING,             data:"cins"::STRING,        data:"tradingItemId"::STRING,
            data:"securityId"::STRING,    data:"companyId"::STRING,         data:"gvkey"::STRING,       data:"bbCmpId"::STRING,
            data:"barraId"::STRING,       data:"rootId"::STRING,            data:"primaryFigi"::STRING, data:"tickerAndExchCode"::STRING,
            data:"compositeFigi"::STRING, data:"exchCode"::STRING,          data:"tradingFigi"::STRING, data:"tradingTickerAndExchCode"::STRING,
            data:"idBbUnique"::STRING,    data:"quotepermid"::STRING,       data:"instrpermid"::STRING, data:"estpermid"::STRING,
            data:"wscode"::STRING,        data:"wsid"::STRING,              data:"ric"::STRING,         data:"ibesticker"::STRING,
            data:"seccode"::STRING,       data:"timestampSaved"::NUMBER,    source_file
        FROM {target_table}_RAW
        WHERE SOURCE_FILE NOT IN (SELECT SOURCE_FILE FROM {target_table});
    """
    logger.info(final_insert)
    sf_query(conn,final_insert)
    logger.info("Data transferred to main table successfully.")

def loadSTCoverage(conn,DATABASE,SCHEMA,filename,region,nowait=False):
    stage_name = f'@{DATABASE}.{SCHEMA}.ST_COVERAGE'
    target_table = f'{DATABASE}.{SCHEMA}.ST_COVERAGE'
    logger.info(f"Uploading: {filename}")

    # Extract base name and add region suffix
    base_name = os.path.basename(filename)
    dest_file = base_name+"_"+region
    
    put_command = f"PUT file://{filename} {stage_name}/{dest_file} AUTO_COMPRESS=TRUE"

    max_wait_minutes = 20
    check_interval_seconds = 30  # Check every 30 seconds
    start_time = time.time()
    while time.time() - start_time < max_wait_minutes * 60:
        if os.path.exists(filename):
            break
        else:
            logger.info(f"? Waiting for file: {filename}")
            if nowait:
                logger.info(f"Will not fait for file: {filename}, since nowait is passed")
                return
        time.sleep(check_interval_seconds)
        
    ## Snowflake put will fail if file not available.
    sf_query(conn,put_command)
    # Step 2: Copy from stage into table
    copy_command = f"""
            COPY INTO {target_table}_RAW
            FROM (
            SELECT 
                $1,  -- the entire row as VARIANT
                METADATA$FILENAME
                FROM {stage_name} t
            )
            FILE_FORMAT = (TYPE = 'PARQUET');
    """
    logger.info(copy_command)
    sf_query(conn,copy_command)
    logger.info("Data loaded successfully.")
    final_insert = f"""
        INSERT INTO {target_table} (
            date,                           availdate,                        uid,
            sid,                            cid,                              tid,
            symbol,                         country,                          currency,
            issueType,                      secTyp,                           mic,
            exch,                           countryOfExposure,                countryOfDomicile,
            countryOfIncorp,                companyName,                      timestampsaved,
            source_file
        )
        SELECT
            data:"date"::STRING,            data:"availdate"::STRING,         data:"uid"::STRING,
            data:"sid"::STRING,             data:"cid"::STRING,               data:"tid"::STRING,
            data:"symbol"::STRING,          data:"country"::STRING,           data:"currency"::STRING,
            data:"issueType"::STRING,       data:"secTyp"::STRING,            data:"mic"::STRING,
            data:"exch"::STRING,            data:"countryOfExposure"::STRING, data:"countryOfDomicile"::STRING,
            data:"countryOfIncorp"::STRING, data:"companyName"::STRING,       data:"timestampsaved"::STRING,
            source_file
        FROM {target_table}_RAW
        WHERE SOURCE_FILE NOT IN (SELECT SOURCE_FILE FROM {target_table});
    """
    logger.info(final_insert)
    sf_query(conn,final_insert)
    logger.info("Data transferred to main table successfully.")

def generate_mapping_data(conn,DATABASE,SCHEMA):
    sql = f"CALL {DATABASE}.{SCHEMA}.SP_POPULATE_TRADING_FIGIS()"
    logger.info(sql)
    sf_query(conn,sql)
    sql = f"CALL {DATABASE}.{SCHEMA}.SP_POPULATE_JGIDS()"
    logger.info(sql)
    sf_query(conn,sql)

def generate_exceptions(conn,DATABASE,SCHEMA,date):
    sql = f"CALL {DATABASE}.{SCHEMA}.SP_GENERATE_DIFFS('{date}')"
    logger.info(sql)
    sf_query(conn,sql)


TABLE_STYLE = """
                <style>
                    table {
                        border-collapse: collapse;
                        width: auto; /* Auto width based on content */
                        max-width: 100%; /* Ensures it doesn't overflow */
                        font-size: 12px; /* Small font */
                    }
                    th {
                        background-color: #4CAF50; /* Green header */
                        color: white;
                        padding: 5px;
                        text-align: left;
                        border: 1px solid #ddd;
                    }
                    td {
                        padding: 5px;
                        text-align: left;
                        # border: 1px solid #ddd;
                        border: 1px solid black; 
                    }
                    tr:nth-child(odd) {
                        background-color: #3bdbed; 
                    }
                    tr:nth-child(even) {
                        background-color: #7ae8f5; 
                    }
                </style>
                """
                

def format_dataframe_html(df, title):
    return f"<strong>{title}:</strong><br>" + TABLE_STYLE + df.to_html(index=False, escape=False) + "<br><br>"

def raise_exceptions_alerts(conn,DATABASE,SCHEMA,date,email,EXCLUDE_LIST,region_desc):
    sql = f"""
            SELECT DISTINCT
                '{DATABASE}' ENVIROMENT,
                prod.EXCEPTION_DETAILS,
                ea.ANALYSIS EXCEPTION_ANALYSIS,
                PROD.TRADING_FIGI,
                PROD.ST_VALUE EXPECTED_VALUE,
                prod.BBG_EXCHANGE_TICKER_ID,
                prod.GSC_FIELDNAME,
                prod.GSC_VALUE GSC_VALUE,
                --By Default and JG ID CHecks are to be investigated by JG unless explicity flagged in the Exception Analysis table.
                CASE WHEN prod.EXCEPTION_DETAILS IN ('JG ID is associated to more than one Trading Figis','JGID is not unique') AND ea.JG_ACTION IS NULL
                     THEN TRUE 
                     WHEN prod.EXCEPTION_DETAILS IN ('DUPLICATE RELATIONS in GSC for PARNTCOF') 
                     THEN FALSE
                     ELSE COALESCE(ea.JG_ACTION,TRUE)
                END JG_ACTION,
                CASE WHEN prod.EXCEPTION_DETAILS IN ('JG ID is associated to more than one Trading Figis','JGID is not unique') AND ea.GSC_ACTION IS NULL
                     THEN FALSE
                     WHEN prod.EXCEPTION_DETAILS IN ('DUPLICATE RELATIONS in GSC for PARNTCOF') 
                     THEN TRUE
                     ELSE COALESCE(ea.GSC_ACTION,TRUE)
                END GSC_ACTION
            FROM  {DATABASE}.{SCHEMA}.GSC_EXCEPTIONS prod
            LEFT  JOIN {DATABASE}.{SCHEMA}.EXCEPTION_ANALYSIS ea
            ON    prod.EXCEPTION_DETAILS = ea.EXCEPTION_DETAILS
            AND   prod.BBG_EXCHANGE_TICKER_ID = ea.BBG_EXCHANGE_TICKER_ID
            WHERE PROD.RUN_DATE = '{date}'
            AND   prod.EXCEPTION_DETAILS NOT IN ({EXCLUDE_LIST})
            ORDER BY 1,2,3;
    """
    logger.info(sql)
    df = pd.read_sql(sql, conn)
    df["EXCEPTION_ANALYSIS"] = df["EXCEPTION_ANALYSIS"].str.replace(r"(\r\n|\n|\r)", "<br>", regex=True)

    email_util = EmailUtility()
    body = (
        f"Hi team,<br><br>These are the ST To GSC Comparison Results for {region_desc}.<br>"
    )

    if df is not None and not df.empty:
        body += format_dataframe_html(df, "Exception Details")

        email_util.send_email(
            to_recipient=email.split(','),
            subject=f"[ST_TO_GSC_CHECKS][ERROR] Exception Report for {DATABASE} for {region_desc}, for {date}",
            body=body,
            df=None
        )

def upload_all_files(conn, DATABASE, SCHEMA, base_path, region):
    for filename in os.listdir(base_path):
        file_path = os.path.join(base_path, filename)

        # Ensure it's a regular file
        if os.path.isfile(file_path):
            logger.info(f"Preparing to upload {file_path}")
            loadSTData(conn, DATABASE, SCHEMA, file_path, region)

def upload_all_files_coverage(conn, DATABASE, SCHEMA, base_path, region):
    for filename in os.listdir(base_path):
        file_path = os.path.join(base_path, filename)

        # Ensure it's a regular file
        if os.path.isfile(file_path):
            logger.info(f"Preparing to upload {file_path}")
            loadSTCoverage(conn, DATABASE, SCHEMA, file_path, region)


# Replace region dynamically
def get_region_path(base_path, region):
    return base_path.replace("/eu/", f"/{region.lower()}/")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog=__name__, description='ST Checks')
    parser.add_argument('-d', '--date', required=False, help="date in YYYYMMDD format")
    parser.add_argument('-c', '--config', required=True, help="config should uat or prod")
    parser.add_argument('--history', required=False, action='store_true', help="Do a one-time historical load.")
    parser.add_argument('--historycov', required=False, action='store_true', help="Do a one-time historical load of coverage data.")
    parser.add_argument('--nowait', required=False, action='store_true', help="Don't wait for NA files to be available")
    args = parser.parse_args()

    objconfig = {}
    objconfig = fio.read_config_secrets()
    config_path = config_root + "/conf/sources/st_check.toml"
    config = read_toml(config_path)

    # config = toml.load(config_path)
    config_curr = config['config'][args.config]

    email=config_curr['email']

    DATABASE_ST=config_curr['database_st']
    DATABASE_GSC=config_curr['database_gsc']
    DATABASE_SM=config_curr['database_sec_mast']
    SCHEMA_ST="ST_CHECK"
    SCHEMA_GSC_ENRICHMENT="GSC_ENRICHMENT"
    current_year_str = str(datetime.now().year)
    
    conn = snowflake.connector.connect(
        user = objconfig['sf_user'],
        password = objconfig['sf_password'],
        account = objconfig['sf_account'],
        warehouse = objconfig['sf_data_platform_wh'],
        database = DATABASE_ST,
        schema = SCHEMA_ST,
        role = objconfig['sf_data_platform_role']
    )
    base_path = config_curr['base_path']
    base_path_coverage = config_curr['base_path_coverage']

    if args.historycov:
        logger.info("Doing one-time historical load...")
        eu_path = get_region_path(base_path_coverage, 'EU') + current_year_str + "/"
        na_path = get_region_path(base_path_coverage, 'NA') + current_year_str + "/"
        upload_all_files_coverage(conn, DATABASE_ST, SCHEMA_ST, eu_path, region='EU')
        upload_all_files_coverage(conn, DATABASE_ST, SCHEMA_ST, na_path, region='NA')
    elif args.history:
        logger.info("Doing one-time historical load...")
        eu_path = get_region_path(base_path, 'EU') + current_year_str + "/"
        na_path = get_region_path(base_path, 'NA') + current_year_str + "/"
        upload_all_files(conn, DATABASE_ST, SCHEMA_ST, eu_path, region='EU')
        upload_all_files(conn, DATABASE_ST, SCHEMA_ST, na_path, region='NA')
    elif args.date:
        date_obj = datetime.strptime(args.date, "%Y%m%d")
        prev_day = date_obj - timedelta(days=1)
        while prev_day.weekday() >= 5:  # 5 = Saturday, 6 = Sunday
            prev_day -= timedelta(days=1)
        prev_day_str = prev_day.strftime("%Y%m%d")
        date_str=date_obj.strftime("%Y-%m-%d")

        ## This piece of code is required only for including in the email i.e. what is the coverage of the exception
        ## Since we pass --nowait only in the EU Dag (so that it doesn't wait for the NA file to be available), decided to leverage this flag.
        region_desc = None
        if args.nowait:
            region_desc='EU'
        else:
            region_desc='NA + EU'

        logger.info(f"Loading ST Identifiers Data for {args.date}")
        eu_file_path = get_region_path(base_path, 'EU') + current_year_str + "/" + args.date
        na_file_path = get_region_path(base_path, 'NA') + current_year_str + "/" + args.date
        loadSTData(conn, DATABASE_ST, SCHEMA_ST, na_file_path, region='NA',nowait=args.nowait)
        loadSTData(conn, DATABASE_ST, SCHEMA_ST, eu_file_path, region='EU')

        logger.info(f"Loading ST Coverage Data for {args.date}")
        eu_file_path = get_region_path(base_path_coverage, 'EU') + current_year_str + "/" + args.date
        na_file_path = get_region_path(base_path_coverage, 'NA') + current_year_str + "/" + args.date
        loadSTCoverage(conn, DATABASE_ST, SCHEMA_ST, na_file_path, region='NA',nowait=args.nowait)
        loadSTCoverage(conn, DATABASE_ST, SCHEMA_ST, eu_file_path, region='EU')

        logger.info("Generation of JG_ID and Mapping Table")
        generate_mapping_data(conn,DATABASE_SM,SCHEMA_GSC_ENRICHMENT)
        logger.info("Calling the SP to generate Exceptions")
        generate_exceptions(conn,DATABASE_SM,SCHEMA_ST,date_str)
        logger.info("Pulling the exceptions, send email and raising an error")
        EXCLUDE_LIST=config_curr['exclude_list']
        raise_exceptions_alerts(conn,DATABASE_SM,SCHEMA_ST,date_str,email,EXCLUDE_LIST,region_desc)
    else:
        logger.error("You must provide either --date or --history.")
        exit(1)


    conn.close()
