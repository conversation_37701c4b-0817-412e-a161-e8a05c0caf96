raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/ice/flash/1.0/Settlement_Reports_CSV/"  ## Location of Raw Files
  s3_bucket: "jg-data-dp-vendor-data" ## S3 with Snowflake Access
  s3_prefix: "ice/flash" ## Internal S3 path to files

  # Replace <YEAR> placeholder with the actual year when processing files for example: 2023 Gas/icecleared_gas_2023*.dat 

  structure: '[
    "Gas/icecleared_gas_<YEAR>*.dat",
    "Gas/icecleared_gasoptions_<YEAR>*.dat",
    "Gas/ngxcleared_gas_<YEAR>*.dat",
    "Oil/icecleared_oil_<YEAR>*.dat",
    "Oil/icecleared_oiloptions_<YEAR>*.dat",
    "Power/icecleared_power_<YEAR>*.dat",
    "Power/icecleared_poweroptions_<YEAR>*.dat",
    "Power/ngxcleared_power_<YEAR>*.dat"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "ICE_MFT"
  
  table_map:
    FUTURES_FLASH_RAW:
      pattern: ".*(icecleared|ngxcleared)_(gas|oil|power)_<YEAR>.*.dat" ## Matches files without "options"
      col_num: 11
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/flash/" ##<stage name>/<stage path>
      file_format: "FF_ICE_MFT_FLASH"

    OPTIONS_FLASH_RAW:
      pattern: ".*icecleared_(gas|oil|power)options_<YEAR>.*.dat" ## Matches files with "options"
      col_num: 13
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/flash/" ##<stage name>/<stage path>
      file_format: "FF_ICE_MFT_FLASH"
