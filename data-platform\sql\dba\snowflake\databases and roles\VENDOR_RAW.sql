

CREATE ROLE IF NOT EXISTS DR_VENDOR_RAW_OWNER;

CREATE ROLE IF NOT EXISTS DR_VENDOR_RAW_READER;

CREATE ROLE IF NOT EXISTS DR_VENDOR_RAW_WRITER;

GRANT ROLE DR_VENDOR_RAW_OWNER,DR_VENDOR_RAW_READER, DR_VENDOR_RAW_WRITER to ROLE FR_DATA_PLATFORM;


create DATABASE IF NOT EXISTS VENDOR_RAW;

 

GRANT OWNERSHIP ON DATABASE VENDOR_RAW TO ROLE DR_VENDOR_RAW_OWNER;

--added
GRANT ROLE DR_VENDOR_RAW_OWNER to ROLE ACCOUNTADMIN;

--added
USE ROLE DR_VENDOR_RAW_OWNER;


GRANT USAGE ON DATABASE VENDOR_RAW TO ROLE DR_VENDOR_RAW_READER;

GRANT USAGE ON DATABASE VENDOR_RAW TO ROLE DR_VENDOR_RAW_WRITER;

--added 
USE DATABASE VENDOR_RAW;

-- Stage schema for

--    Temporary work while loading

--    Create External S3 Stage in Stage schema

--    Reader's should not see this schema

CREATE SCHEMA IF NOT EXISTS STAGE_RAW;

GRANT OWNERSHIP ON SCHEMA STAGE_RAW TO ROLE DR_VENDOR_RAW_OWNER;

GRANT USAGE ON SCHEMA STAGE_RAW TO ROLE DR_VENDOR_RAW_WRITER;

 

CREATE SCHEMA IF NOT EXISTS COPPCLARK_HDS;

 

GRANT USAGE ON SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_OWNER;

GRANT USAGE ON SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_READER;

GRANT USAGE ON SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_WRITER;

 

GRANT OWNERSHIP ON SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_OWNER;

GRANT USAGE ON SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_READER;

GRANT USAGE ON SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_WRITER;

 

GRANT SELECT ON ALL TABLES IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_READER;

--GRANT SELECT ON FUTURE TABLES IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_READER;

GRANT SELECT ON ALL VIEWS IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_READER;

--GRANT SELECT ON FUTURE VIEWS IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_READER;

 

--DBT NEEDS CREATE TABLE/VIEW

GRANT CREATE TABLE, CREATE VIEW ON SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_WRITER;

 

GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_WRITER;

--GRANT INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_WRITER;

GRANT SELECT ON ALL VIEWS IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_WRITER;

--GRANT SELECT ON FUTURE VIEWS IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_WRITER;

 

 

CREATE SCHEMA IF NOT EXISTS SWAPSMON_HOURS;

 

GRANT USAGE ON SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_OWNER;

GRANT USAGE ON SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_READER;

GRANT USAGE ON SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_WRITER;

 

GRANT OWNERSHIP ON SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_OWNER;

GRANT USAGE ON SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_READER;

GRANT USAGE ON SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_WRITER;

 

GRANT SELECT ON ALL TABLES IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_READER;

--GRANT SELECT ON FUTURE TABLES IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_READER;

GRANT SELECT ON ALL VIEWS IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_READER;

--GRANT SELECT ON FUTURE VIEWS IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_READER;

 

--DBT NEEDS CREATE TABLE/VIEW

GRANT CREATE TABLE, CREATE VIEW ON SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_WRITER;

 

GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_WRITER;

--GRANT INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_WRITER;

GRANT SELECT ON ALL VIEWS IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_WRITER;

--GRANT SELECT ON FUTURE VIEWS IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_WRITER;

 

 

CREATE SCHEMA IF NOT EXISTS ICE_EOD_REPORTS;

 

GRANT USAGE ON SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_OWNER;

GRANT USAGE ON SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_READER;

GRANT USAGE ON SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_WRITER;

 

GRANT OWNERSHIP ON SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_OWNER;

GRANT USAGE ON SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_READER;

GRANT USAGE ON SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_WRITER;

 

GRANT SELECT ON ALL TABLES IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_READER;

--GRANT SELECT ON FUTURE TABLES IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_READER;

GRANT SELECT ON ALL VIEWS IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_READER;

--GRANT SELECT ON FUTURE VIEWS IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_READER;

 

--DBT NEEDS CREATE TABLE/VIEW

GRANT CREATE TABLE, CREATE VIEW ON SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_WRITER;

 

GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_WRITER;

--GRANT INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_WRITER;

GRANT SELECT ON ALL VIEWS IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_WRITER;

--GRANT SELECT ON FUTURE VIEWS IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_WRITER;


----------------------
--- Need Account Admin back to do Future Statements 
----------------------
USE ROLE ACCOUNTADMIN;


GRANT SELECT ON FUTURE TABLES IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_READER;


GRANT SELECT ON FUTURE VIEWS IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_READER;


GRANT INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_WRITER;

GRANT SELECT ON FUTURE VIEWS IN SCHEMA COPPCLARK_HDS TO ROLE DR_VENDOR_RAW_WRITER;

GRANT SELECT ON FUTURE TABLES IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_READER;


GRANT SELECT ON FUTURE VIEWS IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_READER;


GRANT INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_WRITER;


GRANT SELECT ON FUTURE VIEWS IN SCHEMA SWAPSMON_HOURS TO ROLE DR_VENDOR_RAW_WRITER;


GRANT SELECT ON FUTURE TABLES IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_READER;


GRANT SELECT ON FUTURE VIEWS IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_READER;



GRANT INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_WRITER;


GRANT SELECT ON FUTURE VIEWS IN SCHEMA ICE_EOD_REPORTS TO ROLE DR_VENDOR_RAW_WRITER;

GRANT USAGE ON DATABASE VENDOR_RAW TO ROLE DR_COMMOD_READER;

GRANT USAGE ON DATABASE VENDOR_RAW TO ROLE DR_COMMOD_WRITER;

GRANT USAGE ON SCHEMA VENDOR_RAW.USDA_COMMOD TO ROLE DR_COMMOD_READER;

GRANT USAGE ON SCHEMA VENDOR_RAW.USDA_REFERENCE TO ROLE DR_COMMOD_READER;

GRANT USAGE ON SCHEMA VENDOR_RAW.USDA_COMMOD TO ROLE DR_COMMOD_WRITER;

GRANT USAGE ON SCHEMA VENDOR_RAW.USDA_REFERENCE TO ROLE DR_COMMOD_WRITER;
 
GRANT SELECT ON ALL TABLES IN SCHEMA VENDOR_RAW.USDA_COMMOD TO ROLE DR_COMMOD_READER;

GRANT SELECT ON ALL TABLES IN SCHEMA VENDOR_RAW.USDA_REFERENCE TO ROLE DR_COMMOD_READER;
 
GRANT SELECT ON ALL TABLES IN SCHEMA VENDOR_RAW.USDA_COMMOD TO ROLE DR_COMMOD_WRITER;

GRANT SELECT ON ALL TABLES IN SCHEMA VENDOR_RAW.USDA_REFERENCE TO ROLE DR_COMMOD_WRITER;
 