from stcommon import *
from stcommon.infra.datalake.config import *

def initRawStore(dataset):
  log.info(f"Running init for {dataset}")
  loader = loadRawStore(dataset)
  loader.init()

def syncRawStore(dataset, **kwargs):
  log.info(f"Running sync for {dataset}")
  loader = loadRawStore(dataset)
  loader.run(**kwargs)

def loadRawStore(dataset):
  cfg_file = f"{getDataLakePath()}/conf/sources/{dataset}.toml"
  cfg_toml = read_toml(cfg_file)
  return instance_from_config(cfg_toml)

def getDataLakePath():
  fpath = os.environ.get('STDATA_PATH')  # TODO maybe need a separate one
  if fpath is None:
    raise ValueError(f"Missing STDATA_PATH Environment Variable")
  return fpath

from stcommon.infra.rawstore.sftp import SftpLoader
from stcommon.infra.rawstore.sftpzip import SftpZipLoader
from stcommon.infra.rawstore.database import <PERSON>Loader
from stcommon.infra.rawstore.bloomberg import BloombergLoader
from stcommon.infra.rawstore.snowflake import SnowflakeLoader
from stcommon.infra.rawstore.ftp import FtpLoader
from stcommon.infra.rawstore.awsbl import AwsBucketLoader
from stcommon.infra.rawstore.bbtickhistory import BloombergTickHistoryLoader