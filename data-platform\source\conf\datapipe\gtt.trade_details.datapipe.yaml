raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/gtt/trade_details"  ## Location of Raw Files
  s3_prefix: "gtt" ## Internal S3path to files

  structure: '[
    "gtt_report_*_$DATE$.csv"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "GTT"

  table_map:
    GTT_IMPORT_USD_RAW:
      pattern: "gtt_report_.*_I_$DATE$.csv" ## Need to be a regex format
      parse_header: true
      col_num: 60 #TODO: check the number of columns in the file
      metadata_columns: ["filename", "start_scan_time"]
      metadata_alias: ['filename', 'start_scan_time']
      stage_path: "gtt/" ##<stage name>/<stage path>
      file_format: "FF_TRADE_DETAILS"

    GTT_EXPORT_USD_RAW:
      pattern: "gtt_report_.*_E_$DATE$.csv" ## Need to be a regex format
      parse_header: true
      col_num: 60 #TODO: check the number of columns in the file
      metadata_columns: ["filename", "start_scan_time"]
      metadata_alias: ['filename', 'start_scan_time']
      stage_path: "gtt/" ##<stage name>/<stage path>
      file_format: "FF_TRADE_DETAILS"