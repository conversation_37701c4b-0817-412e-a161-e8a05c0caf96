USE ROLE SYSADMIN ;


CREATE DATABASE SPACINSIDER_UAT;

USE DATABASE SPACINSIDER_UAT;

----------------------
-- ROLES
----------------------
USE ROLE SECURITYADMIN;

DROP ROLE IF EXISTS DR_SPACINSIDER_UAT_READ_ONLY;
DROP ROLE IF EXISTS DR_SPACINSIDER_UAT_READ_WRITE;
DROP ROLE IF EXISTS DR_SPACINSIDER_UAT_DB_OWNER;


CREATE ROLE IF NOT EXISTS DR_SPACINSIDER_UAT_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_SPACINSIDER_UAT_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_SPACINSIDER_UAT_DB_OWNER;

GRANT ROLE DR_SPACINSIDER_UAT_DB_OWNER TO ROLE FR_DATA_PLATFORM_LEADS;
GRANT ROLE DR_SPACINSIDER_UAT_READ_WRITE TO ROLE FR_DATA_PLATFORM_UAT;

USE ROLE SYSADMIN ;

----------------------
--DB OWNERSHIP
----------------------
GRANT OWNERSHIP ON DATABASE SPACINSIDER_UAT TO ROLE DR_SPACINSIDER_UAT_DB_OWNER;

----------------------
--- FUTURE STATEMENTS 
----------------------
USE ROLE SECURITYADMIN;

----
--SCHEMA  FUTURE STATEMENTS
----
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE SPACINSIDER_UAT TO ROLE DR_SPACINSIDER_UAT_READ_WRITE;
GRANT CREATE TABLE, CREATE VIEW ON FUTURE SCHEMAS IN DATABASE SPACINSIDER_UAT TO ROLE DR_SPACINSIDER_UAT_READ_WRITE;

----
--FUTURE GRANTS READ WRITE ROLE
----
GRANT SELECT,INSERT, UPDATE, DELETE, TRUNCATE ON FUTURE TABLES IN DATABASE SPACINSIDER_UAT TO ROLE DR_SPACINSIDER_UAT_READ_WRITE;
GRANT SELECT ON FUTURE VIEWS IN DATABASE SPACINSIDER_UAT TO ROLE DR_SPACINSIDER_UAT_READ_WRITE;

----
--FUTURE GRANTS READ ROLE
----
GRANT SELECT ON FUTURE TABLES IN DATABASE SPACINSIDER_UAT TO ROLE DR_SPACINSIDER_UAT_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN DATABASE SPACINSIDER_UAT TO ROLE DR_SPACINSIDER_UAT_READ_ONLY;


-- FUTURE OWNERSHIP TO OWNER ROLE(ALWAYS)
--Ensure that the owner role is always the owner of schemas
GRANT OWNERSHIP ON FUTURE SCHEMAS IN DATABASE SPACINSIDER_UAT TO ROLE DR_SPACINSIDER_UAT_DB_OWNER;


USE ROLE DR_SPACINSIDER_UAT_DB_OWNER;

----------------------
--DB USAGE
----------------------
GRANT USAGE ON DATABASE SPACINSIDER_UAT TO ROLE DR_SPACINSIDER_UAT_READ_WRITE;
GRANT USAGE ON DATABASE SPACINSIDER_UAT TO ROLE DR_SPACINSIDER_UAT_READ_ONLY;

----------------------
--CREATE DBT SCHEMAS
----------------------
CREATE SCHEMA SPACINSIDER_UAT.STAGE;
CREATE SCHEMA SPACINSIDER_UAT.INTEGRATION;
CREATE SCHEMA SPACINSIDER_UAT.SPACANALYTICS;

-----
--READ ROLE ONLY USAGE ON FINAL SCHEMA
-----
GRANT USAGE ON SCHEMA SPACINSIDER_UAT.SPACANALYTICS TO ROLE DR_SPACINSIDER_UAT_READ_ONLY;


----------------------
--FR_RISK Grants
----------------------
GRANT ROLE DR_SPACINSIDER_UAT_READ_ONLY TO ROLE FR_RISK;