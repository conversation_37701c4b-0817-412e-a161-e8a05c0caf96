with weekend_flag as (
 select 
 case
 when date_part('DOW', convert_timezone('UTC', 'America/New_York', current_timestamp())::DATE) IN (0,6) THEN 1
 else 0
 end as is_weekend
),
adjusted_date as (
 select 
 case
 when date_part('DOW', convert_timezone('UTC', 'America/New_York', current_timestamp())::DATE) = 1 THEN convert_timezone('UTC', 'America/New_York', current_timestamp())::DATE - 3
 else convert_timezone('UTC', 'America/New_York', current_timestamp())::DATE - 1
 end as check_date
),
yesterday_data as (
 select
 file_flag,
 count(*) as cnt
 from {{ ref('pub_ice_mft_futures') }}
 where cast(trade_date as date) = (select check_date from adjusted_date)
 group by file_flag
),
all_flags as (
 select distinct file_flag
 from {{ ref('pub_ice_mft_futures') }}
)
select 
 af.file_flag,
 yd.cnt,
 wf.is_weekend
from 
all_flags af 
left join yesterday_data yd 
on af.file_flag = yd.file_flag
join weekend_flag wf 
where wf.is_weekend = 0
and coalesce(yd.cnt, 0) = 0 and af.file_flag = 'Final'