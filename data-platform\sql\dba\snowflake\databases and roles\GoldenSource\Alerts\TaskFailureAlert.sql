--Create alert
CREATE OR <PERSON><PERSON>LACE ALERT GOLDENSOURCE.METADATA.GOLDENSOURCE_OMNI_TASK_FAILURE_ALERT
WAREHOUSE = 'GS_DAILY_LOAD_WH'
IF (
  EXISTS (
    select
      stat_id
    from GOLDENSOURCE.metadata.ingestion_statistics_base
    where task_name <> root_task
    and status in ('Failed','Partial','Cancelled')
  )
)
THEN
  DECLARE
    alert_payload varchar;
  BEGIN
    select
      TO_JSON(object_construct('type','omni_pipeline_alerts','payload',ARRAY_AGG(object_construct(*)) WITHIN GROUP (ORDER BY pipeline_name,task_start_time DESC))) into alert_payload
    from(
      select
        pipeline_name,
        task_start_time,
        task_end_time,
        task_run_id,
        schema_name,
        table_name,
        status,
        first_error
      from (
        select
          *,
          FILTER(errors, a -> TRY_CAST(a:"errors_seen"::varchar AS NUMBER) > 0) filtered_errors,
          CASE
            WHEN NOT IS_NULL_VALUE(GET(filtered_errors,0):"errors_seen") THEN CONCAT(GET(filtered_errors,0):"file"::varchar,' => ',GET(filtered_errors,0):"first_error"::varchar)
            ELSE GET(errors,0):"message"::varchar
          END first_error
        from (
          select
            pcfg.pipeline_id pipeline_name,
            ins.task_run_id,
            ins.schema_name,
            ins.table_name,
            ins.job_name,
            max(ins.status) status,
            min(ins.task_start_time) task_start_time,
            max(ins.task_end_time) task_end_time,
            CASE
              WHEN max(ins.fl_id) is null then array_union_agg(ins.errors)
              ELSE array_union_agg(hist.file_load_details)
            END errors,
            array_union_agg(ins.warnings) warnings
          from (
            select
              fl.value::varchar fl_id,
              *
            from metadata.ingestion_statistics_base,
            lateral flatten(input => file_load_id, outer => True) fl
            where stat_id in (
              select
                stat_id
              from TABLE(RESULT_SCAN(SNOWFLAKE.ALERT.GET_CONDITION_QUERY_UUID()))
            )
          ) ins
          left join metadata.file_load_history hist
          on ins.fl_id = hist.load_id
          inner join metadata.pipeline_configuration pcfg
          on ins.root_task = pcfg.root_task_name
          group by ins.task_run_id, ins.schema_name, ins.table_name, ins.job_name, pcfg.pipeline_id
        )
      )
    );
    CALL SYSTEM$SEND_SNOWFLAKE_NOTIFICATION(
      SNOWFLAKE.NOTIFICATION.APPLICATION_JSON(:alert_payload),
      SNOWFLAKE.NOTIFICATION.INTEGRATION('OMNI_PROD_NOTIFICATION_INTEGRATION')
    );
  END;

GRANT EXECUTE ALERT ON ACCOUNT TO ROLE GS_DATA_ADMIN_PROD;
GRANT EXECUTE MANAGED ALERT ON ACCOUNT TO ROLE GS_DATA_ADMIN_PROD;

--Resume alert
ALTER ALERT GOLDENSOURCE.METADATA.GOLDENSOURCE_OMNI_TASK_FAILURE_ALERT RESUME;
