use role ACCOUNTADMIN;

CREATE DATABASE SECURITY_MASTER;

-- Create schemas
USE DATABASE SECURITY_MASTER;

CREATE SCHEMA IF NOT EXISTS POC;

-- Create warehouse
CREATE WAREHOUSE IF NOT EXISTS SECURITY_MASTER_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 300         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

-- Create roles
CREATE ROLE IF NOT EXISTS DR_SECURITY_MASTER_READER;
CREATE ROLE IF NOT EXISTS DR_SECURITY_MASTER_WRITER;
CREATE ROLE IF NOT EXISTS DR_SECURITY_MASTER_OWNER;

-- Grant usage on warehouse to all roles
GRANT USAGE ON WAREHOUSE SECURITY_MASTER_WH TO ROLE DR_SECURITY_MASTER_WRITER;
GRANT USAGE ON WAREHOUSE SECURITY_MASTER_WH TO ROLE DR_SECURITY_MASTER_OWNER;

-- Grant usage on database to all roles
GRANT USAGE ON DATABASE SECURITY_MASTER TO ROLE DR_SECURITY_MASTER_WRITER;
GRANT USAGE ON DATABASE SECURITY_MASTER TO ROLE DR_SECURITY_MASTER_OWNER;
GRANT USAGE ON DATABASE SECURITY_MASTER TO ROLE DR_SECURITY_MASTER_READER;

-- Grant schema-specific permissions
GRANT USAGE ON SCHEMA SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_WRITER;
GRANT USAGE ON SCHEMA SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_OWNER;
GRANT USAGE ON SCHEMA SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_READER;


CREATE SCHEMA IF NOT EXISTS SECURITY_MASTER.EQUITIES;
GRANT OWNERSHIP ON SCHEMA SECURITY_MASTER.EQUITIES TO ROLE DR_SECURITY_MASTER_OWNER REVOKE CURRENT GRANTS;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA SECURITY_MASTER.EQUITIES TO DR_SECURITY_MASTER_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA SECURITY_MASTER.EQUITIES TO DR_SECURITY_MASTER_OWNER;
GRANT ALL ON SCHEMA SECURITY_MASTER.EQUITIES TO DR_SECURITY_MASTER_OWNER;
GRANT USAGE ON SCHEMA SECURITY_MASTER.EQUITIES TO DR_SECURITY_MASTER_READER;

CREATE SCHEMA IF NOT EXISTS SECURITY_MASTER.FLEX;
GRANT OWNERSHIP ON SCHEMA SECURITY_MASTER.FLEX TO ROLE DR_SECURITY_MASTER_OWNER REVOKE CURRENT GRANTS;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA SECURITY_MASTER.FLEX TO DR_SECURITY_MASTER_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA SECURITY_MASTER.FLEX TO DR_SECURITY_MASTER_OWNER;
GRANT ALL ON SCHEMA SECURITY_MASTER.FLEX TO DR_SECURITY_MASTER_OWNER;
GRANT USAGE ON SCHEMA SECURITY_MASTER.FLEX TO DR_SECURITY_MASTER_READER;

CREATE SCHEMA IF NOT EXISTS SECURITY_MASTER.GSC_ENRICHMENT;
GRANT OWNERSHIP ON SCHEMA SECURITY_MASTER.GSC_ENRICHMENT TO ROLE DR_SECURITY_MASTER_OWNER REVOKE CURRENT GRANTS;
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA SECURITY_MASTER.GSC_ENRICHMENT TO DR_SECURITY_MASTER_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA SECURITY_MASTER.GSC_ENRICHMENT TO DR_SECURITY_MASTER_OWNER;
GRANT ALL ON SCHEMA SECURITY_MASTER.GSC_ENRICHMENT TO DR_SECURITY_MASTER_OWNER;
GRANT USAGE ON SCHEMA SECURITY_MASTER.GSC_ENRICHMENT TO DR_SECURITY_MASTER_READER;





GRANT ROLE DR_SECURITY_MASTER_WRITER TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_SECURITY_MASTER_READER TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_SECURITY_MASTER_READER TO ROLE DR_SECURITY_MASTER_OWNER;
GRANT ROLE DR_SECURITY_MASTER_READER TO ROLE JG_GENERAL;

-- Manage Functional Role privileges here
CREATE ROLE FR_SECURITY_MASTER_USER;
GRANT ROLE DR_SECURITY_MASTER_READER TO ROLE FR_SECURITY_MASTER_USER;

-- NONPROD users also have access to PROD data for testing (but not vice versa)
GRANT ROLE FR_SECURITY_MASTER_USER TO ROLE FR_SECURITY_MASTER_NONPROD_USER;

GRANT ROLE DR_SECURITY_MASTER_READER TO ROLE FR_SECURITY_MASTER_USER;
-- Some views in SECURITY_MASTER require SELECT on the GOLDENSOURCE database, so we grant GS_DATA_ANALYST_PROD to FR_SECURITY_MASTER_USER
GRANT ROLE GS_DATA_ANALYST_PROD TO ROLE FR_SECURITY_MASTER_USER;

GRANT ROLE FR_SECURITY_MASTER_USER TO ROLE FR_DATA_PLATFORM;
GRANT ROLE FR_SECURITY_MASTER_USER TO ROLE FR_EXECUTION_TECH;
GRANT ROLE FR_SECURITY_MASTER_USER TO ROLE FR_TRADING_PROD;
GRANT ROLE FR_SECURITY_MASTER_USER TO ROLE FR_FETECH;
GRANT ROLE FR_SECURITY_MASTER_USER TO ROLE FR_COMPLIANCE_IT;
GRANT ROLE FR_SECURITY_MASTER_USER TO ROLE FR_JG_INTERCONNECT_PROD;





-- OWNER role (full access)
GRANT ALL PRIVILEGES ON SCHEMA SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_OWNER;

-- Grant future grants to OWNER role
GRANT ALL PRIVILEGES ON FUTURE TABLES IN SCHEMA SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_OWNER;

GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_WRITER;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_OWNER;

GRANT SELECT ON ALL TABLES IN SCHEMA  SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_WRITER;
GRANT SELECT ON ALL TABLES IN SCHEMA  SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_OWNER;
GRANT SELECT ON ALL TABLES IN SCHEMA  SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_READER;

GRANT SELECT ON FUTURE TABLES IN SCHEMA  SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_WRITER;
GRANT SELECT ON FUTURE TABLES IN SCHEMA  SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_OWNER;
GRANT SELECT ON FUTURE TABLES IN SCHEMA  SECURITY_MASTER.POC TO ROLE DR_SECURITY_MASTER_READER;

GRANT ROLE DR_SECURITY_MASTER_OWNER TO ROLE FR_DATA_PLATFORM_LEADS;
GRANT ROLE DR_SECURITY_MASTER_OWNER TO USER OONTONGTAN;
GRANT ROLE DR_SECURITY_MASTER_OWNER TO USER PULKITVORA;
GRANT ROLE DR_SECURITY_MASTER_OWNER TO USER TRISTANFABER;


GRANT USAGE ON SCHEMA SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_WRITER;
GRANT USAGE ON SCHEMA SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_OWNER;
GRANT USAGE ON SCHEMA SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_READER;

GRANT ALL PRIVILEGES ON SCHEMA SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_OWNER;

GRANT ALL PRIVILEGES ON FUTURE TABLES IN SCHEMA SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_OWNER;

GRANT SELECT ON ALL TABLES IN SCHEMA  SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_WRITER;
GRANT SELECT ON ALL TABLES IN SCHEMA  SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_OWNER;
GRANT SELECT ON ALL TABLES IN SCHEMA  SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_READER;

GRANT SELECT ON FUTURE TABLES IN SCHEMA  SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_WRITER;
GRANT SELECT ON FUTURE TABLES IN SCHEMA  SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_OWNER;
GRANT SELECT ON FUTURE TABLES IN SCHEMA  SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_READER;

GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_WRITER;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA SECURITY_MASTER.EQUITY_OPTIONS_VOL TO ROLE DR_SECURITY_MASTER_OWNER;

GRANT ROLE DR_SECURITY_MASTER_READER TO ROLE FR_EQVOL;
GRANT ROLE DR_SECURITY_MASTER_READER TO ROLE FR_QUANTMODELLING;