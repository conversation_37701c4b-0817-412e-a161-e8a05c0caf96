
{{
    config(
        materialized='incremental',
        unique_key=['COUNTRY', 'hour','DATE','granularity','timestamp_utc'],
        tags=['eex_spot','eex_spot_hh'],
        alias='int_eex_spot_price_volumes_hh'
    )
}}

with stg_price as (
    select 
        *, 
        ts_utc as timestamp 
    from 
        {{ ref('stg_eex_spot_unpivoted_price_hh')}}
    qualify Rank() over (partition by country,hour,timestamp order by start_scan_time desc) =1
),

stg_volume as (
    select 
        *, 
        ts_utc as timestamp 
    from 
        {{ ref('stg_eex_spot_unpivoted_volumes_hh')}}
    qualify Rank() over (partition by country,hour,timestamp order by start_scan_time desc) =1
),

stg_price_volumes as(
    select 
        stg_price.country           as country,
        stg_price.hour              as hour, 
        stg_price.DELIVERY_DAY      as delivery_day,
        stg_price.ts_utc            as timestamp_utc,
        stg_price.price             as price,
        stg_volume.market           as market,
        stg_volume.granularity      as granularity,
        stg_volume.volume           as volume,
        stg_volume.start_scan_time  as volume_start_scan_time,
        stg_price.start_scan_time   as price_start_scan_time
    from 
        stg_price 
    inner join 
        stg_volume
    on  stg_price.country            = stg_volume.country
    and stg_price.hour              = stg_volume.hour
    and stg_price.timestamp         = stg_volume.timestamp
    order by 
        stg_price.ts_utc
),

price_volumes as(
    select 
        country                     as country,
        hour                        as hour, 
        DELIVERY_DAY                as delivery_day,
        timestamp_utc               as timestamp_utc,
        price                       as price,
        volume                      as volume,
        market                      as market, 
        granularity                 as granularity ,
        case
            when volume_start_scan_time <= price_start_scan_time then volume_start_scan_time
            else price_start_scan_time 
        end as start_scan_time 
    from 
        stg_price_volumes
)

select
    distinct
        country                     as country,
        hour                        as hour, 
        DELIVERY_DAY                as date,
        timestamp_utc               as timestamp_utc,
        price                       as price,
        volume                      as volume,
        market                      as market, 
        granularity                 as granularity ,
        start_scan_time             as start_scan_time    
from 
    price_volumes int_price_volumes
where   price   is not null 
    and volume  is not null

{% if is_incremental() %}
    and start_scan_time >= (current_date - interval '1 days')::timestamp
{% endif %}
qualify Rank() over (partition by country,hour,date,granularity,timestamp_utc order by start_scan_time desc) =1
