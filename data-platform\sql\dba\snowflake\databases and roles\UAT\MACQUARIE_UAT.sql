-- DB & role creation
-- This database will contain Pricing curves 
-- and FX  rate from Vendor/broker MACUARIE
-- It will be populated from airflow job

CREATE DATABASE MACQUARIE_UAT;
CREATE ROLE DR_MACQUARIE_UAT_DB_OWNER;
GRANT OWNERSHIP ON DATABASE MACQUARIE_UAT TO ROLE DR_MACQUARIE_UAT_DB_OWNER;

GRANT ROLE DR_MACQUARIE_UAT_DB_OWNER TO ROLE ACCOUNTADMIN;

CREATE ROLE DR_MACQUARIE_UAT_DB_READ_ONLY;
CREATE ROLE DR_MACQUARIE_UAT_DB_READ_WRITE;
GRANT ROLE DR_MACQUARIE_UAT_DB_READ_ONLY TO ROLE DR_MACQUARIE_UAT_DB_READ_WRITE;
GRANT ROLE DR_MACQUARIE_UAT_DB_READ_WRITE TO ROLE DR_MACQUARIE_UAT_DB_OWNER;

GRANT ALL ON DATABASE MACQUARIE_UAT TO ROLE DR_MACQUARIE_UAT_DB_OWNER;
GRANT USAGE ON DATABASE MACQUARIE_UAT TO DR_MACQUARIE_UAT_DB_READ_ONLY;

-- Schema & role creation
USE ROLE DR_MACQUARIE_UAT_DB_OWNER;
CREATE SCHEMA MACQUARIE_UAT.PRICE;

GRANT USAGE ON SCHEMA MACQUARIE_UAT.PRICE TO DR_MACQUARIE_UAT_DB_READ_WRITE;
GRANT USAGE ON SCHEMA MACQUARIE_UAT.PRICE TO DR_MACQUARIE_UAT_DB_READ_ONLY;

GRANT OWNERSHIP ON SCHEMA MACQUARIE_UAT.PRICE TO ROLE DR_MACQUARIE_UAT_DB_OWNER;
USE ROLE AR_GRANT_ADMIN; -- needed for FUTURE grants
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA MACQUARIE_UAT.PRICE TO DR_MACQUARIE_UAT_DB_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA MACQUARIE_UAT.PRICE TO DR_MACQUARIE_UAT_DB_OWNER;
GRANT ALL ON SCHEMA MACQUARIE_UAT.PRICE TO DR_MACQUARIE_UAT_DB_OWNER;

-- Manage Functional Role privileges here
GRANT ROLE DR_MACQUARIE_UAT_DB_OWNER TO USER HARRYCHEN;
GRANT ROLE DR_MACQUARIE_UAT_DB_OWNER TO ROLE FR_DATA_PLATFORM;