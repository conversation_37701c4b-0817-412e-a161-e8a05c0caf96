
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.timetables.trigger import CronTriggerTimetable
from airflow.hooks.base import BaseHook
from datetime import timed<PERSON><PERSON>
import pendulum
from util.dataset import to_s3
from airflow.providers.http.operators.http import HttpOperator
from util.validate import check_for_anomalies
import os,logging,requests,subprocess

dag_id = os.path.basename(__file__).replace(".py","")


def download_and_validate():

    run_download_gl_bb_script()

    # Fetch the Airflow connection by conn_id
    conn = BaseHook.get_connection("http_default")
    
    # Build full URL using connection details + endpoint
    endpoint = f"getJFSFeedAvailabilitySatusDailyDownload/{dag_id}"
    base_url = f"{conn.host}"
    if conn.port:
        base_url += f":{conn.port}"
    full_url = f"{base_url}/{endpoint}"
    
    # Set headers (you can also pull these from conn.extra if stored there)
    headers = {"Content-Type": "application/json"}

    # Make the request
    response = requests.get(full_url, headers=headers)

    # Validate response
    try:
        success, message = check_for_anomalies(response)
        if success:
            logging.info(message) 
    except Exception as e:
        logging.error("Validation failed: %s", str(e))
        raise e
    
    return "success" 

def run_download_gl_bb_script():

    JGDATA_PATH = os.environ.get("JGDATA_PATH")

    command = (
        f'python3 {JGDATA_PATH}/bin/download.py --dataset eex.spot.gl.bb.agg'
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.warning("Command failed with exit status %d", e.returncode)
        logging.warning("STDOUT: %s", e.stdout)
        logging.warning("STDERR: %s", e.stderr)
        raise

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 11, 4, tz="Europe/Berlin"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 4,
    "retry_delay": timedelta(minutes=10),
}


doc_md_DAG=f'This DAG downloads the data from EEX Spot from SFTP to JSF and to S3'
dag = DAG(
    dag_id=dag_id,
    default_args=default_args,
    description='This DAG downloads the data from EEX Spot from SFTP to JSF and to S3',
    schedule=CronTriggerTimetable('10 13 * * *',timezone="Europe/Berlin"),
    tags=["phdata","eex","spot"],
    catchup=False,
    doc_md=doc_md_DAG,
)

download_files = PythonOperator(
    task_id="download_and_validate",
    python_callable=download_and_validate,
    dag=dag
)

download_files 