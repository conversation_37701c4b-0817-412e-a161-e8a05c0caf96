import os, sys, argparse
import pandas as pd
from strunner import *
setupEnvironment()
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '..')))
from jglib import *
import jglib.datasets.snp.feeds
from datetime import datetime

parser = argparse.ArgumentParser(description="Process previous date")
parser.add_argument("--procdate", required=True, help="Specify the processing date in format YYYY-MM-DD", type=validate_date)
args = parser.parse_args()
date_pattern = args.procdate
year = date_pattern.split('-')[0]

summary = jglib.infra.iceberg.IcebergReader('snp/feeds/Summary',jg_datalake_path(),'cache')
df = summary.readTable('snp.feeds.Summary')
df['fileDate'] = pd.to_datetime(pd.to_numeric(df['fileDate'], errors='coerce').dropna().astype(int).astype(str),format='%Y%m%d').dt.date
date_patterns = dates.get_last_14_days_from_prior_day()
filtered_df = df[df['fileDate'].astype(str).isin(date_patterns)]
summary_files = filtered_df[['fileName']].drop_duplicates()

data_path = f"{jg_data_path()}/snp_regen/{date_pattern}"
create_directory_if_not_exists(data_path)
config_secret = read_config_secrets()
host = config_secret['snp_sftp_host']
username = config_secret['snp_sftp_username']
password = config_secret['snp_sftp_password']
# date_folder_patterns = [f"{d[:4]}-{d[4:6]}-{d[6:]}" for d in date_patterns]
formatted_dates = [datetime.strptime(date, "%Y-%m-%d").strftime("%Y%m%d") for date in date_patterns]

print(f"Processing Date: {date_pattern}")

folders = [f"/EtFHistory/{datetime.now().year}/{date_pattern}" for date_pattern in date_patterns]
folders.append("/JainGlobalLLC13250466")
all_files = []
for folder in folders:
    all_files.extend(paramiko.get_filenames_in_folder(host, 22, username, password, folder, formatted_dates))

sftp_df = pd.DataFrame(all_files, columns=["fileName", "filePath", "fileDate"])
filtered_sftp_files = sftp_df[['fileName', 'filePath']].drop_duplicates()
summary_sftp_files = filtered_sftp_files[filtered_sftp_files["fileName"].str.startswith("ETF")]
missing_files = summary_sftp_files.merge(summary_files, on="fileName", how="left", indicator=True)\
                                  .query('_merge == "left_only"')\
                                  .drop(columns=['_merge'])

paramiko.download_files_by_filename_parallel(host, 22, username, password, missing_files['filePath'], data_path)
