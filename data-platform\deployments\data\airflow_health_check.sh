#!/bin/bash
export PATH=$PATH:/opt/jsvc-datait/.local/bin/
export RAWSTORE_ROOT='/jfs/tech1/apps/rawdata/'
export JGDATA_PATH="/jfs/tech1/apps/datait/jg-code/prod//JG-DATA-PLATFORM/source/"
#export PYTHONPATH="/jfs/tech1/apps/datait/jg-code/prod//JG-DATA-PLATFORM/source/"
export STCOMMON_PATH="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/stcommon"

if ! pgrep -f "airflow scheduler" > /dev/null
then
  echo "starting scheduler" 
#  echo -e "Date -- `date`" >> /opt/jsvc-datait/airflow/logs/airflow_scheduler.log
  airflow scheduler >> /opt/data/process_logs/airflow/airflow_scheduler.log &  
fi

if ! pgrep -f "airflow webserver" > /dev/null
then
  echo "starting webserver"
#  echo -e "Date -- `date`" >> /opt/jsvc-datait/airflow/logs/airflow_webserver.log
  airflow webserver >> /opt/data/process_logs/airflow/airflow_webserver.log &
fi

