----------------------
-- ROLES
----------------------
USE ROLE SECURITYADMIN;


CREATE ROLE IF NOT EXISTS DR_MDS_DEV_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_MDS_DEV_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_MDS_DEV_DB_OWNER;

GRANT ROLE DR_MDS_DEV_DB_OWNER TO ROLE FR_DATA_PLATFORM ;
GRANT ROLE DR_MDS_DEV_READ_WRITE TO ROLE DR_MDS_DEV_DB_OWNER;
GRANT ROLE DR_MDS_DEV_READ_ONLY TO ROLE DR_MDS_DEV_READ_WRITE;

GRANT ROLE DR_MDS_DEV_READ_WRITE TO USER SVC_DATAIT_MDS;


USE ROLE SYSADMIN ;

CREATE DATABASE MDS_DEV;

USE DATABASE MDS_DEV;

-- Warehouse
CREATE WAREHOUSE IF NOT EXISTS MDS_DEV_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60           -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE MDS_DEV_WH TO ROLE DR_MDS_DEV_READ_WRITE;
GRANT USAGE ON WAREHOUSE MDS_DEV_WH TO ROLE DR_MDS_DEV_DB_OWNER;

USE WAREHOUSE MDS_DEV_WH ;


----------------------
--DB OWNERSHIP
----------------------
GRANT OWNERSHIP ON DATABASE MDS_DEV TO ROLE DR_MDS_DEV_DB_OWNER;
GRANT USAGE ON DATABASE MDS_DEV TO ROLE DR_MDS_DEV_READ_ONLY;
GRANT USAGE ON DATABASE MDS_DEV TO ROLE DR_MDS_DEV_READ_WRITE;

----------------------
--- FUTURE STATEMENTS 
----------------------
USE ROLE SECURITYADMIN;

-- FUTURE OWNERSHIP TO OWNER ROLE(ALWAYS)
--Ensure that the owner role is always the owner of schemas
GRANT OWNERSHIP ON FUTURE SCHEMAS IN DATABASE MDS_DEV TO ROLE DR_MDS_DEV_DB_OWNER;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE MDS_DEV TO ROLE DR_MDS_DEV_READ_ONLY;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE MDS_DEV TO ROLE DR_MDS_DEV_READ_WRITE;

----
--FUTURE GRANTS READ WRITE ROLE
----
GRANT SELECT,INSERT, UPDATE, DELETE, TRUNCATE ON FUTURE TABLES IN DATABASE MDS_DEV TO ROLE DR_MDS_DEV_READ_WRITE;
GRANT SELECT ON FUTURE VIEWS IN DATABASE MDS_DEV TO ROLE DR_MDS_DEV_READ_WRITE;

----
--FUTURE GRANTS READ ROLE
----
GRANT SELECT ON FUTURE TABLES IN DATABASE MDS_DEV TO ROLE DR_MDS_DEV_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN DATABASE MDS_DEV TO ROLE DR_MDS_DEV_READ_ONLY;

USE ROLE DR_MDS_DEV_DB_OWNER;

CREATE SCHEMA MDS_DEV.MDS;

GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA MDS_DEV.MDS TO ROLE DR_MDS_DEV_READ_WRITE;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA MDS_DEV.MDS TO ROLE DR_MDS_DEV_READ_WRITE;


GRANT SELECT ON FUTURE TABLES IN SCHEMA MDS_DEV.MDS TO ROLE DR_MDS_DEV_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA MDS_DEV.MDS TO ROLE DR_MDS_DEV_READ_ONLY;
