-- For query log ingestion
-- We can't grant access to custom roles or users in snowflake.accunt_usage schema
-- work around to create database and schema and then create view 


USE ROLE SYSADMIN;

CREATE SCHEMA DATA_PLATFORM_CORE.ACCOUNT_USAGE ;

GRA<PERSON> USAGE ON DATABASE DATA_PLATFORM_CORE TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON SCHEMA DATA_PLATFORM_CORE.ACCOUNT_USAGE TO ROLE FR_DATACATALOG_ALATION;

GRANT SELECT ON FUTURE VIEWS IN SCHEMA DATA_PLATFORM_CORE.ACCOUNT_USAGE TO ROLE FR_DATACATALOG_ALATION;


CREATE VIEW DATA_PLATFORM_CORE.ACCOUNT_USAGE.alation_QLI_view AS
SELECT
     user_name as "userName",
     CASE
        WHEN SCHEMA_NAME IS NULL
          THEN DATABASE_NAME ||'.'|| ''
     ELSE DATABASE_NAME ||'.'|| SCHEMA_NAME
     END AS "defaultDatabases",
     TRIM(QUERY_TEXT) AS "queryString",
     TRIM(SESSION_ID) AS "sessionID",
     ROUND(TOTAL_ELAPSED_TIME/1000,0) AS "seconds",
     false AS "cancelled",
     TO_CHAR(start_time,'YYYY-MM-DD HH:MI:SS.US') AS "startTime",
     TO_CHAR(start_time,'YYYY-MM-DD HH:MI:SS.US') AS "sessionStartTime"
FROM SNOWFLAKE.ACCOUNT_USAGE.QUERY_HISTORY
WHERE EXECUTION_STATUS = 'SUCCESS' AND QUERY_TEXT IS NOT NULL AND TRIM(QUERY_TEXT) != ''
      AND QUERY_TYPE NOT IN (
          'ALTER_SESSION',
          'ALTER_SET_TAG',
          'ALTER_UNSET_TAG',
          'BEGIN_TRANSACTION',
          'CALL',
          'COMMIT',
          'CREATE_STREAM',
          'DESCRIBE',
          'DROP_STREAM',
          'GET_FILES',
          'GRANT',
          'LIST_FILES',
          'PUT_FILES',
          'REFRESH_DYNAMIC_TABLE_AT_REFRESH_VERSION',
          'REMOVE_FILES',
          'SHOW',
          'USE'
      )
      AND NOT (
          QUERY_TEXT ILIKE 'REVOKE%'
          OR QUERY_TEXT ILIKE 'EXPLAIN%'
          OR QUERY_TEXT ILIKE 'SELECT SYSTEM$%'
          OR QUERY_TEXT ILIKE 'SELECT 1%'
          OR QUERY_TEXT ILIKE 'INSERT INTO % VALUES%'
          )
      AND (DATABASE_NAME NOT IN ('SNOWFLAKE') OR DATABASE_NAME IS NULL)
      -- AND DATABASE_NAME = '<database_name>'
      ;


use role FR_DATACATALOG_ALATION;

--validate
select * from DATA_PLATFORM_CORE.ACCOUNT_USAGE.alation_QLI_view limit 10;
