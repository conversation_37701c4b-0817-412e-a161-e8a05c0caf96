{"id": 5077931, "name": "require-pull-requests-to-main-release", "target": "branch", "source_type": "Repository", "source": "Jain-Global/data-platform", "enforcement": "active", "conditions": {"ref_name": {"exclude": [], "include": ["~DEFAULT_BRANCH", "refs/heads/release/*"]}}, "rules": [{"type": "required_status_checks", "parameters": {"strict_required_status_checks_policy": true, "do_not_enforce_on_create": true, "required_status_checks": [{"context": "check-commit-messages", "integration_id": 15368}]}}, {"type": "pull_request", "parameters": {"required_approving_review_count": 1, "dismiss_stale_reviews_on_push": true, "require_code_owner_review": true, "require_last_push_approval": false, "required_review_thread_resolution": true, "automatic_copilot_code_review_enabled": false, "allowed_merge_methods": ["merge", "squash", "rebase"]}}], "bypass_actors": [{"actor_id": 12900467, "actor_type": "Team", "bypass_mode": "always"}]}