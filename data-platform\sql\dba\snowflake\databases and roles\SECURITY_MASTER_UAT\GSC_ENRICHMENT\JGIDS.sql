CREATE TABLE SECURITY_MASTER_UAT.GSC_ENRICHMENT.JGIDS(
	MARKET_INSTRUMENT_CHARACTERISTICS_SOK VARCHAR,
    JGI<PERSON> VARCHAR(14),
    <PERSON><PERSON>SO<PERSON> VARCHAR
)
COMMENT = 'Temporary table to maintain JGI<PERSON> per GoldenSource Equity listing in the REFINED.GSC_MARKET_INSTRUMENT_CHARACTERISTICS table with primary key MARKET_INSTRUMENT_CHARACTERISTICS_SOK. Once these ids are maintained in the GoldenSource ODS DB, this table will be dropped.';


-- 1) Find listings that still should be assigned a JGID
CREATE OR REPLACE TEMPORARY TABLE JGID_CANDIDATE_LISTINGS AS
SELECT 
    -- debug columns
    mkch.LAST_CHANGE_DATETIME,
    mkch.version_start_timestamp,
    mkch.source_effective_start_date,
    mkch.version_end_timestamp,
    inst.ISIN_ID,
    mkch.BB_EXCHANGE_TICKER_ID,
    mkch.BB_COMPOSITE_TICKER_ID,
    mkch.ADDITIONAL_LISTING_QUALIFICATION_TYPE,
    mkch.BB_GLOBAL_ID,
    mkch.BB_COMPOSITE_GLOBAL_ID,
    mkch.GEOGRAPHIC_UNIT_ID,
    mkch.TRADING_CURRENCY_CODE,
    -- Ignore minor vs major currency unit differences when assigning JGID: a) GSC has some data quality issues and is not consistent with BBG, and b) for fungibility, the quoting convention shouldn't really matter
    --CASE WHEN mstat.MINORCUR_CHARACTERVALUE IS NULL THEN mkch.TRADING_CURRENCY_CODE ELSE mstat.MINORCUR_CHARACTERVALUE END TRADING_CURRENCY_CODE,
    mkch.PRIMARY_TRADING_MARKET_IND,
    mkch.MARKET_INSTRUMENT_CHARACTERISTICS_SOK,
    mkch.TRADING_STATUS_TYPE
    
from GOLDENSOURCE_UAT.refined.gsc_market_instrument_characteristics mkch

left join GOLDENSOURCE_UAT.refined.gsc_financial_instrument inst
on inst.instrument_key_sok = mkch.instrument_key_sok
and inst.data_warehouse_status_num = 1

left join GOLDENSOURCE_UAT.structured.gsc_market_financial_instrument_characteristics_statistics mstat
on  mkch.MARKET_INSTRUMENT_CHARACTERISTICS_SOK = mstat.MARKET_INSTRUMENT_CHARACTERISTICS_SOK
and mstat.data_warehouse_status_num = 1

    -- choose latest version of GoldenSource listing record
    where mkch.data_warehouse_status_num = 1 
    -- only pick equity-like instruments - non-equity securities probably can have a simpler logic
    and inst.instrument_type in ('EQSHR', 'RECEIPTS', 'PFD', 'LTDPART', 'REALESTA', 'FUND', 'UNIT')
    -- only pick euro markets for initial scope - extend as necessary to eventually cover all equities
    and mkch.GEOGRAPHIC_UNIT_ID in ('AT', 'BE', 'CH', 'DE', 'DK', 'ES', 'FI', 'FR', 'GB', 'IE', 'IT', 'NL', 'NO', 'PT', 'SE', 'US', 'ZA')
    -- only pick listings with exchange ticker & compo figi
    and mkch.BB_EXCHANGE_TICKER_ID is not null and mkch.BB_COMPOSITE_TICKER_ID is not null
    -- only pick records whose MARKET_INSTRUMENT_CHARACTERISTICS_SOK has not yet been added to the JGIDS table (manually review & update those if needed)
    and mkch.MARKET_INSTRUMENT_CHARACTERISTICS_SOK not in (SELECT MARKET_INSTRUMENT_CHARACTERISTICS_SOK FROM SECURITY_MASTER_UAT.GSC_ENRICHMENT.JGIDS);
    ;


-- 2) Find all ISIN/TRADING_CURRENCY pairs with PRIMARY_TRADING_MARKET_IND = 'Y' and use its composite FIGI as JGID
CREATE OR REPLACE TEMPORARY TABLE NEW_JGIDS_FROM_PRIMARY_TRADING_MARKET_IND AS
WITH 
unique_primary_market_isin_trading_ccy AS (
    select ISIN_ID, TRADING_CURRENCY_CODE
    from JGID_CANDIDATE_LISTINGS
    where PRIMARY_TRADING_MARKET_IND = 'Y'
    group by ISIN_ID, TRADING_CURRENCY_CODE
    having COUNT(PRIMARY_TRADING_MARKET_IND) = 1
    ),
unique_primary_market_isin_trading_ccy_jgids AS (
    select a.ISIN_ID, a.TRADING_CURRENCY_CODE, CONCAT('JG', b.BB_COMPOSITE_GLOBAL_ID) as JGID
    from unique_primary_market_isin_trading_ccy a
    left join JGID_CANDIDATE_LISTINGS b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE)
    where PRIMARY_TRADING_MARKET_IND = 'Y'
    )
select a.MARKET_INSTRUMENT_CHARACTERISTICS_SOK, b.JGID, a.ISIN_ID, a.TRADING_CURRENCY_CODE, 
    'PRIMARY_TRADING_MARKET_IND' as REASON, a.source_effective_start_date as ADDED_ON
    from JGID_CANDIDATE_LISTINGS a
    join unique_primary_market_isin_trading_ccy_jgids b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE)
;


-- 3) Rank all remaining ISIN/TRADING_CURRENCY pairs by number of listings per BB_COMPOSITE_GLOBAL_ID and sort by BB_COMPOSITE_GLOBAL_ID (to break ties)
CREATE OR REPLACE TEMPORARY TABLE NEW_JGIDS_FROM_EXCHANGE_LISTING_COUNT AS
WITH
non_primary_market_isin_trading_ccy AS (
    select ISIN_ID, TRADING_CURRENCY_CODE
    from JGID_CANDIDATE_LISTINGS
    where PRIMARY_TRADING_MARKET_IND = 'N'
    group by ISIN_ID, TRADING_CURRENCY_CODE
    ),
non_primary_market_composite_figi_listing_count AS (
    select a.ISIN_ID, a.TRADING_CURRENCY_CODE, b.BB_COMPOSITE_GLOBAL_ID, COUNT(*) as EXCHANGE_LISTING_COUNT,
    from non_primary_market_isin_trading_ccy a
    join JGID_CANDIDATE_LISTINGS b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE)
    where b.BB_COMPOSITE_GLOBAL_ID is not null
    group by a.ISIN_ID, a.TRADING_CURRENCY_CODE, b.BB_COMPOSITE_GLOBAL_ID
    ),
ranked_non_primary_market_composite_figi_listing_count AS (
    select ISIN_ID, TRADING_CURRENCY_CODE, CONCAT('JG', BB_COMPOSITE_GLOBAL_ID) as JGID, EXCHANGE_LISTING_COUNT,
    ROW_NUMBER() OVER (
      PARTITION BY ISIN_ID, TRADING_CURRENCY_CODE
      -- rank 1 for the BB_COMPOSITE_GLOBAL_ID with the highest EXCHANGE_LISTING_COUNT, and lowest alphnumeric order in case of a draw
      ORDER BY EXCHANGE_LISTING_COUNT DESC, BB_COMPOSITE_GLOBAL_ID ASC
    ) AS rnk
    from non_primary_market_composite_figi_listing_count
    )
select a.MARKET_INSTRUMENT_CHARACTERISTICS_SOK, b.JGID, a.ISIN_ID, a.TRADING_CURRENCY_CODE, 
    'EXCHANGE_LISTING_COUNT' as REASON, a.source_effective_start_date as ADDED_ON
    from JGID_CANDIDATE_LISTINGS a
    join ranked_non_primary_market_composite_figi_listing_count b on (a.ISIN_ID = b.ISIN_ID and a.TRADING_CURRENCY_CODE = b.TRADING_CURRENCY_CODE and b.rnk = 1)
;


-- Sanity check: Should return 0 records, which it does
select * from JGID_CANDIDATE_LISTINGS a
where a.MARKET_INSTRUMENT_CHARACTERISTICS_SOK not in (select MARKET_INSTRUMENT_CHARACTERISTICS_SOK from NEW_JGIDS_FROM_PRIMARY_TRADING_MARKET_IND)
and a.MARKET_INSTRUMENT_CHARACTERISTICS_SOK not in (select MARKET_INSTRUMENT_CHARACTERISTICS_SOK from NEW_JGIDS_FROM_EXCHANGE_LISTING_COUNT)
and a.ISIN_ID is not null
and a.BB_COMPOSITE_GLOBAL_ID is not null;


-- 4) Insert all newly identified JGIDs into JGID table
INSERT INTO SECURITY_MASTER_UAT.GSC_ENRICHMENT.JGIDS
select * from NEW_JGIDS_FROM_PRIMARY_TRADING_MARKET_IND 
union select * from NEW_JGIDS_FROM_EXCHANGE_LISTING_COUNT
;
