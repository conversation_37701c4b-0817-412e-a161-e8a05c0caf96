import pytest
from unittest.mock import MagicMock, patch
from datetime import datetime
#from strunner import *

import argparse 
import sys
import os
import boto3
from urllib.parse import urlparse
import re
import glob

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root_temp = os.path.abspath(os.path.join(current_dir, '../..'))
# project_root =  os.path.abspath(os.path.join(project_root_temp, '../..'))
sys.path.append(project_root_temp)
print(project_root_temp)


from jglib.tools.paramiko import *
import jglib.infra.python.fileio as fio
import jglib.paths as path
from jglib.tools.unzip_file import unzip_all_zip_files
from jglib.tools.s3Operations import S3_Operations

@patch("paramiko.Transport")
@patch("paramiko.SFTPClient.from_transport")
def test_download_files_by_date(mock_sftp_client_from_transport, mock_transport):
    mock_sftp = MagicMock()
    mock_sftp.listdir_attr.return_value = [
        MagicMock(filename="file_20240101.txt", st_mtime=datetime(2024, 1, 1).timestamp())
    ]
    mock_sftp.get.return_value = None

    mock_sftp_client_from_transport.return_value = mock_sftp
    mock_transport.return_value = MagicMock()

    download_files_by_date(
        hostname="example.com",
        username="user",
        password="pass",
        remote_dir="/remote",
        local_dir="/local",
        date_criteria=datetime(2024, 1, 1).date()
    )

    mock_sftp.get.assert_called_once_with("/remote/file_20240101.txt", "/local/file_20240101.txt")
