from jglib.infra.iceberg import *
import pyspark
from pyspark.sql import SparkSession
from pyspark.sql.types import *
from pandas.core.dtypes.dtypes import DatetimeTZDtype
from filelock import FileLock

class IcebergWriter:

  def __init__(self, name, path, type):
    log.debug(os.path.abspath(__file__) + "==>" + sys._getframe().f_code.co_name)
    log.debug(f"Initialize Iceberg at: {path}")
    self.name = name # name of the catalog
    self.path = path # root path for data lake
    self.type = type # type of data lake
    self.lock_path = ensure_dir_exists(f"/var/tmp/iceberg/")
    if jg_datalake_path().startswith("s3a://"):
      self.spark = SparkSession.builder.config(conf=(pyspark.SparkConf()
        .setAppName('jglib')
        .setMaster("local")
        .set('spark.executor.memory','10g')
        .set('spark.driver.memory','10g')
        .set('spark.jars',f"{jglib_path()}/jars/jg-iceberg-extensions-1.0-SNAPSHOT.jar,{jglib_path()}/jars/iceberg-spark-runtime.jar,{jglib_path()}/jars/iceberg-spark-extensions.jar")
        .set('spark.sql.extensions','org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions')
        .set('spark.sql.execution.arrow.pyspark.enabled','true')
        .set("fs.s3a.access.key", config_secret["aws_access_key"]) 
        .set("fs.s3a.secret.key", config_secret["aws_secret_key"]) 
        .set("spark.hadoop.fs.s3a.aws.credentials.provider", "org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider") 
        .set("spark.hadoop.fs.s3a.endpoint", f"s3-{config_secret['aws_region']}.amazonaws.com") 
        .set(f"spark.sql.catalog.{name}",'org.apache.iceberg.spark.SparkCatalog')
        .set(f"spark.sql.catalog.{name}.name",name) # set name so we can access in java
        .set(f"spark.sql.catalog.{name}.config_path",f"{jglib_path()}/jglib/")
        .set(f"spark.sql.catalog.{name}.config_type",type)
        .set(f"spark.sql.catalog.{name}.catalog-impl",'com.jainglobal.iceberg.DataLakeCatalog')
        .set(f"spark.sql.catalog.{name}.warehouse",f"{self.path}/{self.type}/")
      )).getOrCreate()
    else:
      self.spark = SparkSession.builder.config(conf=(pyspark.SparkConf()
        .setAppName('jglib')
        .setMaster("local")
        .set('spark.executor.memory','10g')
        .set('spark.driver.memory','10g')
        .set('spark.jars',f"{jglib_path()}/jars/jg-iceberg-extensions-1.0-SNAPSHOT.jar,{jglib_path()}/jars/iceberg-spark-runtime.jar,{jglib_path()}/jars/iceberg-spark-extensions.jar")
        .set('spark.sql.extensions','org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions')
        .set('spark.sql.execution.arrow.pyspark.enabled','true')
        .set(f"spark.sql.catalog.{name}",'org.apache.iceberg.spark.SparkCatalog')
        .set(f"spark.sql.catalog.{name}.name",name) # set name so we can access in java
        .set(f"spark.sql.catalog.{name}.config_path",f"{jglib_path()}/jglib/")
        .set(f"spark.sql.catalog.{name}.config_type",type)
        .set(f"spark.sql.catalog.{name}.catalog-impl",'com.jainglobal.iceberg.DataLakeCatalog')
        .set(f"spark.sql.catalog.{name}.warehouse",f"{self.path}/{self.type}/")
      )).getOrCreate()
    self.spark.catalog.setCurrentCatalog(self.name)

  def __repr__(self):
    return f"Iceberg: {self.path}"

  def updateTable(self, table: str, df: pd.DataFrame, writemode, joinkey):
    log.debug(os.path.abspath(__file__) + "==>" + sys._getframe().f_code.co_name)
    self.spark.catalog.setCurrentCatalog(self.name)
    if not self.spark.catalog.tableExists(table):
      raise ValueError(f"Table Missing: {table}")
    if is_empty(df):
      raise ValueError(f"Cannot update with empty df {table}")
    log.info(df.columns)
    df_pd = df
    df = self.spark.createDataFrame(df,spark_schema(get_schema(df)))
    log.info(f"updating table: {self.name} {table}")
    log.info("writemode: " + writemode)
    # we prevent simultaneous writes from different joblib threads
    lock = FileLock(self.lock_path + f"{table}")
    #with lock.acquire(timeout=60):
    if writemode == "append":
      #df.writeTo(table).append()
      df.writeTo(table).using("iceberg").tableProperty("commit.retry.num-retries", "5000").tableProperty("write.merge.isolation-level", "snapshot").tableProperty("commit.retry.min-wait-ms", "1000").append()
    elif writemode == "overwrite_all":
      #df.writeTo(table).using("iceberg").tableProperty("commit.retry.num-retries", "5000").tableProperty("write.merge.isolation-level", "snapshot").tableProperty("commit.retry.min-wait-ms", "1000").overwritePartitions()
      df.writeTo(table).overwritePartitions()
    elif writemode == "overwrite_file":
      fileNames = "'"+"','".join(df_pd['fileName'].unique().tolist())+"'"
      sql = f"DELETE FROM {table} WHERE fileName in ({fileNames})"
      self.spark.sql(f"{sql}") 
      df.writeTo(table).append()
    elif writemode == "upsert":
      log.info(joinkey)
      if joinkey is None:
        raise ValueError(f"Join Key cannot be None")
      df.createOrReplaceTempView("new_data")
      sql_query = generate_sql_query(table, joinkey)
      log.info("Upsert Query: " + sql_query)
      self.spark.sql(sql_query)
      log.info("Upsert completed")

  def run_query(self, table: str, query):
    log.info(os.path.abspath(__file__) + "==>" + sys._getframe().f_code.co_name)
    self.spark.catalog.setCurrentCatalog(self.name)
    if not self.spark.catalog.tableExists(table):
      raise ValueError(f"Table Missing: {table}")

    log.info("Query: " + query)
    self.spark.sql(query)
    log.info("Query completed")

  # Table Creation
  # ------------------------------------------------------------------------------------------------

  def createTable(self, table: str, schema: dict, partitioned_by=None, comment=None):
    log.debug(os.path.abspath(__file__) + "==>" + sys._getframe().f_code.co_name)
    self.spark.catalog.setCurrentCatalog(self.name)
    if self.spark.catalog.tableExists(f"{table}"):
      log.info(f"Skipping. Already exists: {table}")
      return

    schema = spark_schema(schema) # convert pandas schema to spark format
    sql_schema = ', '.join([f"{x.name} {x.dataType.simpleString()}" for x in schema])
    sql = f"""
            CREATE TABLE {table} ({sql_schema})
            COMMENT '{comment}'
          """ 
    # optional partition spec
    if partitioned_by == 'year':
       sql += f" PARTITIONED BY (year(date))"
    elif partitioned_by == 'month':
       sql += f" PARTITIONED BY (month(date))"
    #elif not partitioned_by is None:
    #  sql += f" PARTITIONED BY (year({partitioned_by}), {partitioned_by})"
    else:
      sql += f" PARTITIONED BY ({partitioned_by})"

    log.info(sql)

    opt  = f"OPTIONS("
    #opt += f"'commit.retry.num-retries'=0,"                             # dont retry commit errors
    opt += f"'write.metadata.delete-after-commit.enabled'='true',"      # delete metadata after commitq
    opt += f"'write.metadata.previous-versions-max'=10"                 # ...
    opt += f")"

    self.spark.sql(f"{sql} {opt}")
    log.info(f"Created table {table}")
    log.info(f"{sql} {opt}")

  def deleteFromTable(self, table: str, condition):
    log.debug(os.path.abspath(__file__) + "==>" + sys._getframe().f_code.co_name)
    self.spark.catalog.setCurrentCatalog(self.name)
    if not self.spark.catalog.tableExists(table):
      raise ValueError(f"Table Missing: {table}")
    sql = f"DELETE FROM {table} WHERE {condition}"
    self.spark.sql(f"{sql}") 
    log.info("Delete completed")

  # Maintenance
  # ------------------------------------------------------------------------------------------------

  def cleanup(self, table: str=None):
    log.info(f"Running {self.name} Cleanup: {table}")
    self.spark.sql(f"CALL {self.name}.system.rewrite_data_files(table=>'{table}',strategy=>'binpack')")
    self.spark.sql(f"CALL {self.name}.system.rewrite_manifests('{table}', false)")
    self.spark.sql(f"CALL {self.name}.system.expire_snapshots(table=>'{table}',retain_last=>2)")
    self.spark.sql(f"CALL {self.name}.system.remove_orphan_files(table=>'{table}')")

# Helpers
# --------------------------------------------------------------------------------------------------

config_secret = read_config_secrets()

def spark_schema(schema):
  spark_schema = []
  for item, dtype in schema.items():
    if is_string_dtype(dtype):
      spark_schema.append(StructField(item,StringType(),True))
    elif is_float_dtype(dtype):
      spark_schema.append(StructField(item,DoubleType(),True))
    elif is_int64_dtype(dtype):
      spark_schema.append(StructField(item,LongType(),True))
    elif is_integer_dtype(dtype):
      spark_schema.append(StructField(item,IntegerType(),True))
    elif dtype == DatetimeTZDtype:
      spark_schema.append(StructField(item,TimestampType(),True))
    elif is_datetime64_dtype(dtype):
      spark_schema.append(StructField(item,TimestampNTZType(),True))
    elif is_bool_dtype(dtype):
      spark_schema.append(StructField(item,BooleanType(),False))
    else:
      raise ValueError(f"Unsupported dtype: {dtype}")
  return StructType(spark_schema)

def generate_sql_query(table, joinkey):
    on_clause = " AND ".join([f"t.{source_key} = s.{target_key}" for source_key, target_key in joinkey.items()])
    sql_query = f"""
          MERGE INTO {table} t
          USING new_data s
          ON {on_clause}
          WHEN MATCHED THEN UPDATE SET *
          WHEN NOT MATCHED THEN INSERT *
        """
    return sql_query
