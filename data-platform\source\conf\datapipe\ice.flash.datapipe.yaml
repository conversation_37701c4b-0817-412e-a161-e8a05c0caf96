raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/ice/flash/1.0/Settlement_Reports_CSV/"  ## Location of Raw Files
  s3_bucket: "jg-data-dp-vendor-data" ## S3 with Snowflake Acess
  s3_prefix: "ice/flash" ## Internal S3path to files

  structure: '[
    "Gas/icecleared_gas_*$DATE$*.dat",
    "Gas/icecleared_gasoptions_*$DATE$*.dat",
    "Gas/ngxcleared_gas_*$DATE$*.dat",
    "Oil/icecleared_oil_*$DATE$*.dat",
    "Oil/icecleared_oiloptions_*$DATE$*.dat",
    "Power/icecleared_power_*$DATE$*.dat",
    "Power/icecleared_poweroptions_*$DATE$*.dat",
    "Power/ngxcleared_power_*$DATE$*.dat"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "ICE_MFT"
  
  table_map:
    FUTURES_FLASH_RAW:
      pattern: ".*(icecleared|ngxcleared)_(gas|oil|power)_*$DATE$*.dat" ## Matches files without "options"
      col_num: 11
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/flash/" ##<stage name>/<stage path>
      file_format: "FF_ICE_MFT_FLASH"

    OPTIONS_FLASH_RAW:
      pattern: ".*icecleared_(gas|oil|power)options_*$DATE$*.dat" ## Matches files with "options"
      col_num: 13
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/flash/" ##<stage name>/<stage path>
      file_format: "FF_ICE_MFT_FLASH"
