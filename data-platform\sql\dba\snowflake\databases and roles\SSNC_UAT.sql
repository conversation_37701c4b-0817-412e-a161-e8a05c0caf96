CREATE DATABASE IF NOT EXISTS SSNC_UAT;
CREATE SCHEMA IF NOT EXISTS SSNC_UAT.GLOBEOP;
CREATE SCHEMA IF NOT EXISTS SSNC_UAT.DERIVATIVES;
CREATE SCHEMA IF NOT EXISTS SSNC_UAT.STAGING;

CREATE ROLE IF NOT EXISTS DR_SSNC_UAT_DB_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_SSNC_UAT_DB_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_SSNC_UAT_OWNER;
CREATE ROLE IF NOT EXISTS DR_SSNC_UAT_RISK_READER;

GRANT ROLE DR_SSNC_UAT_DB_READ_ONLY TO ROLE FR_PRODUCT_CONTROL;
GRANT ROLE DR_SSNC_UAT_RISK_READER TO ROLE FR_RISK;

GRANT USAGE ON DATABASE SSNC_UAT TO ROLE DR_SSNC_UAT_DB_READ_WRITE;
GRANT USAGE ON DATABASE SSNC_UAT TO ROLE DR_SSNC_UAT_DB_READ_ONLY;
GRANT USAGE ON DATABASE SSNC_UAT TO ROLE DR_SSNC_UAT_OWNER;
GRANT USAGE ON DATABASE SSNC_UAT TO ROLE DR_SSNC_UAT_RISK_READER;

-- schema GLOBEOP
GRANT USAGE ON SCHEMA SSNC_UAT.GLOBEOP TO ROLE DR_SSNC_UAT_DB_READ_WRITE;
GRANT USAGE ON SCHEMA SSNC_UAT.GLOBEOP TO ROLE DR_SSNC_UAT_DB_READ_ONLY;

GRANT ROLE DR_SSNC_UAT_OWNER TO ROLE FR_DATA_PLATFORM;
GRANT ALL ON SCHEMA SSNC_UAT.GLOBEOP TO ROLE DR_SSNC_UAT_OWNER;
  
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA SSNC_UAT.GLOBEOP TO DR_SSNC_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA SSNC_UAT.GLOBEOP TO DR_SSNC_UAT_OWNER;
 
GRANT SELECT ON ALL TABLES IN SCHEMA SSNC_UAT.GLOBEOP TO DR_SSNC_UAT_OWNER;
GRANT SELECT ON ALL TABLES IN SCHEMA SSNC_UAT.GLOBEOP TO DR_SSNC_UAT_DB_READ_ONLY;
GRANT SELECT ON ALL TABLES IN SCHEMA SSNC_UAT.GLOBEOP TO DR_SSNC_UAT_DB_READ_WRITE;

GRANT SELECT ON FUTURE TABLES IN SCHEMA SSNC_UAT.GLOBEOP TO DR_SSNC_UAT_DB_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA SSNC_UAT.GLOBEOP TO DR_SSNC_UAT_DB_READ_ONLY;
GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA SSNC_UAT.GLOBEOP TO DR_SSNC_UAT_DB_READ_WRITE;
GRANT SELECT  ON FUTURE VIEWS  IN SCHEMA SSNC_UAT.GLOBEOP TO DR_SSNC_UAT_DB_READ_WRITE;

--schema STAGING
GRANT USAGE ON SCHEMA SSNC_UAT.STAGING TO ROLE DR_SSNC_UAT_DB_READ_WRITE;
GRANT USAGE ON SCHEMA SSNC_UAT.STAGING TO ROLE DR_SSNC_UAT_DB_READ_ONLY;
 
GRANT ROLE DR_SSNC_UAT_OWNER TO ROLE FR_DATA_PLATFORM;
GRANT ALL ON SCHEMA SSNC_UAT.STAGING TO ROLE DR_SSNC_UAT_OWNER;
  
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA SSNC_UAT.STAGING TO DR_SSNC_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA SSNC_UAT.STAGING TO DR_SSNC_UAT_OWNER;
 
GRANT SELECT ON ALL TABLES IN SCHEMA SSNC_UAT.STAGING TO DR_SSNC_UAT_OWNER;
GRANT SELECT ON ALL TABLES IN SCHEMA SSNC_UAT.STAGING TO DR_SSNC_UAT_DB_READ_ONLY;
GRANT SELECT ON ALL TABLES IN SCHEMA SSNC_UAT.STAGING TO DR_SSNC_UAT_DB_READ_WRITE;

GRANT SELECT ON FUTURE TABLES IN SCHEMA SSNC_UAT.STAGING TO DR_SSNC_UAT_DB_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA SSNC_UAT.STAGING TO DR_SSNC_UAT_DB_READ_ONLY;
GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA SSNC_UAT.STAGING TO DR_SSNC_UAT_DB_READ_WRITE;
GRANT SELECT  ON FUTURE VIEWS  IN SCHEMA SSNC_UAT.STAGING TO DR_SSNC_UAT_DB_READ_WRITE;

--schema DERIVATIVES
GRANT USAGE ON SCHEMA SSNC_UAT.DERIVATIVES TO ROLE DR_SSNC_UAT_DB_READ_WRITE;
GRANT USAGE ON SCHEMA SSNC_UAT.DERIVATIVES TO ROLE DR_SSNC_UAT_DB_READ_ONLY;
GRANT USAGE ON SCHEMA SSNC_UAT.DERIVATIVES TO ROLE DR_SSNC_UAT_RISK_READER;

GRANT ALL ON SCHEMA SSNC_UAT.DERIVATIVES TO ROLE DR_SSNC_UAT_OWNER;
 
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_OWNER;
 
GRANT SELECT ON ALL TABLES IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_DB_READ_ONLY;
GRANT SELECT ON ALL TABLES IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_DB_READ_WRITE;
GRANT SELECT ON ALL TABLES IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_OWNER;

GRANT SELECT ON FUTURE TABLES IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_DB_READ_ONLY;

GRANT SELECT ON FUTURE VIEWS  IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_DB_READ_ONLY;
GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_DB_READ_WRITE;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_DB_READ_WRITE;

GRANT SELECT ON ALL TABLES IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_RISK_READER;
GRANT SELECT ON FUTURE TABLES IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_RISK_READER;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA SSNC_UAT.DERIVATIVES TO DR_SSNC_UAT_RISK_READER;