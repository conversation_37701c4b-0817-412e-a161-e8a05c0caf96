
--- Permission for DB : GOLDENSOURCE

USE ROLE SYSADMIN;

GRANT USAGE ON DATABASE GOLDENSOURCE TO FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL SCHEMAS IN DAT<PERSON>ASE GOLDENSOURCE TO ROLE FR_DATACATALOG_ALATION;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE GOLDENSOURCE TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL TABLES IN DATABASE GOLDENSOURCE TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON <PERSON><PERSON>URE TABLES IN DATABASE GOLDENSOURCE TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON ALL VIEWS IN DATABASE GOLDENSOURCE TO ROLE FR_DATACATALOG_ALATION;
GRANT REFERENCES ON FUTURE VIEWS IN DATABASE GOLDENSOURCE TO ROLE FR_DATACATALOG_ALATION;

GRANT REFERENCES ON ALL EXTERNAL TABLES in DATABASE GOLDENSOURCE TO ROL<PERSON> FR_DATACATALOG_ALATION;
GRANT USAGE ON ALL STAGES IN DATABASE GOLDENSOURCE TO ROLE FR_DATACATALOG_ALATION;


GRANT REFERENCES ON FUTURE EXTERNAL TABLES in DATABASE GOLDENSOURCE TO ROLE FR_DATACATALOG_ALATION;

GRANT USAGE ON ALL FUNCTIONS IN DATABASE GOLDENSOURCE TO ROLE FR_DATACATALOG_ALATION;

-- End for Database : GOLDENSOURCE
