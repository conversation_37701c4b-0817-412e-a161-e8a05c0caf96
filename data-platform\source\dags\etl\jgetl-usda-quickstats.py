from airflow import DAG
from airflow.providers.ssh.operators.ssh import SSHOperator
from airflow.providers.ssh.hooks.ssh import SSHHook
from datetime import datetime, timedelta
from airflow.timetables.trigger import CronTriggerTimetable
from util.jg_ssh_dbt_operator import JainGlobalSSHDBTOperator
import os, sys, pendulum, subprocess, logging, pytz
from airflow.hooks.base import BaseHook
from airflow.operators.python import PythonOperator



dag_id = os.path.basename(__file__).replace(".py", "")
ny_time = pendulum.now("America/New_York")
CURRENT_DATE = ny_time.strftime("%Y%m%d")
JGDATA_PATH = os.environ.get("JGDATA_PATH")

def get_jg_env():
    env_value = os.getenv('jg_env', 'prod').lower()
    if env_value not in ['prod', 'uat']:
        raise ValueError(f"Invalid jg_env value: {env_value}. Allowed values are 'prod' or 'uat'.")
    return env_value

env = get_jg_env()

source='usda' 
dataset1 = 'quickstats'

jsvc_path = '/opt/jsvc-datait'
source_path = f'{jsvc_path}/prod/JG-DATA-PLATFORM/source'
python_file_path = f'{source_path}/bin'
command_pipeline_rawdata1 = f'--dataset {source}.{dataset1} --date {CURRENT_DATE}'

command_copy1 = f'--dataset {source}.{dataset1} --date {CURRENT_DATE} --keypair'

conn = BaseHook.get_connection("dbt_conn_id")
key_file = conn.extra_dejson.get("path")


default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.yesterday(tz="America/New_York"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

def download_quickstats():
    command = (
        f"python3 {JGDATA_PATH}/bin/quick_stat_download.py --date {CURRENT_DATE}"
    )
    try:
        logging.info(command)
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logging.info("---LOGS---")
        logging.info("Command output: %s", result.stdout)
        logging.info("Command error output: %s", result.stderr)
    except subprocess.CalledProcessError as e:
        logging.error("Command failed with exit status %d", e.returncode)
        logging.error("STDOUT: %s", e.stdout)
        logging.error("STDERR: %s", e.stderr)
        raise

doc_md_DAG=f'Create  run command for {source}.{dataset1}'
with DAG(
    dag_id=dag_id,
    default_args=default_args,
    description=f'This DAG runs ETL in DBT for {source} {dataset1}',
    schedule=CronTriggerTimetable('30 3 * * *',timezone="America/New_York"),
    tags=[ dataset1, source, dataset1],
    catchup=False,
    doc_md=doc_md_DAG
) as dag:


    ssh_hook = SSHHook(
        remote_host='*************',
        username='jsvc-datait',
        key_file=key_file,
        port=22,
        cmd_timeout=None
    )

    download_quickstats_data =  PythonOperator(
        task_id="jg-raw-usda_agri-qs",
        python_callable=download_quickstats,
        dag=dag
    )


    run_python_command1 = SSHOperator(
        task_id='run_raw_data_command',
        ssh_hook=ssh_hook,
        command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_rawdata.py {command_pipeline_rawdata1}',
        do_xcom_push=True
    )


    run_python_command2 = SSHOperator(
        task_id='run_pipeline_sf_copy_command_for_QS',
        ssh_hook=ssh_hook,
        command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_sf_copy.py {command_copy1}',
        do_xcom_push=True
    )


    

    download_quickstats_data >> run_python_command1 >> run_python_command2  



