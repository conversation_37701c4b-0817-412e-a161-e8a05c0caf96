CREATE DATABASE COMMOD_UAT;
CREATE SCHEMA COMMOD_UAT.KPLER;
CREATE ROLE DR_COMMOD_UAT_DB_READ_WRITE;
CREATE ROLE DR_COMMOD_UAT_DB_READ_ONLY;
CREATE ROLE DR_COMMOD_UAT_OWNER;

GRANT ROLE DR_COMMOD_UAT_DB_READ_ONLY TO ROLE FR_COMMOD_QUANT_US;
 
GRANT USAGE ON DATABASE COMMOD_UAT TO ROLE DR_COMMOD_UAT_DB_READ_WRITE;
GRANT USAGE ON DATABASE COMMOD_UAT TO ROLE DR_COMMOD_UAT_DB_READ_ONLY;
GRANT USAGE ON DATABASE COMMOD_UAT TO ROLE DR_COMMOD_UAT_OWNER;
 
GRANT USAGE ON SCHEMA COMMOD_UAT.KPLER TO ROLE DR_COMMOD_UAT_DB_READ_WRITE;
GRANT USAGE ON SCHEMA COMMOD_UAT.KPLER TO ROLE DR_COMMOD_UAT_DB_READ_ONLY;
 
 
GRANT ROLE DR_COMMOD_UAT_OWNER TO ROLE FR_DATA_PLATFORM;
GRANT ALL ON SCHEMA COMMOD_UAT.KPLER TO ROLE DR_COMMOD_UAT_OWNER;
  
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD_UAT.KPLER TO DR_COMMOD_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA COMMOD_UAT.KPLER TO DR_COMMOD_UAT_OWNER;
 
GRANT SELECT ON FUTURE TABLES IN SCHEMA COMMOD_UAT.KPLER TO DR_COMMOD_UAT_DB_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD_UAT.KPLER TO DR_COMMOD_UAT_DB_READ_ONLY;
GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA COMMOD_UAT.KPLER TO DR_COMMOD_UAT_DB_READ_WRITE;
GRANT SELECT  ON FUTURE VIEWS  IN SCHEMA COMMOD_UAT.KPLER TO DR_COMMOD_UAT_DB_READ_WRITE;
 

CREATE SCHEMA COMMOD_UAT.IIR;

GRANT USAGE ON SCHEMA COMMOD_UAT.IIR TO ROLE DR_COMMOD_UAT_DB_READ_WRITE;
GRANT USAGE ON SCHEMA COMMOD_UAT.IIR TO ROLE DR_COMMOD_UAT_DB_READ_ONLY;

GRANT ALL ON SCHEMA COMMOD_UAT.IIR TO ROLE DR_COMMOD_UAT_OWNER;

 
GRANT OWNERSHIP ON FUTURE TABLES IN SCHEMA COMMOD_UAT.IIR TO DR_COMMOD_UAT_OWNER;
GRANT OWNERSHIP ON FUTURE VIEWS IN SCHEMA COMMOD_UAT.IIR TO DR_COMMOD_UAT_OWNER;
 
GRANT SELECT ON FUTURE TABLES IN SCHEMA COMMOD_UAT.IIR TO DR_COMMOD_UAT_DB_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD_UAT.IIR TO DR_COMMOD_UAT_DB_READ_ONLY;
GRANT SELECT,INSERT,UPDATE,DELETE,TRUNCATE ON FUTURE TABLES IN SCHEMA COMMOD_UAT.IIR TO DR_COMMOD_UAT_DB_READ_WRITE;
GRANT SELECT  ON FUTURE VIEWS  IN SCHEMA COMMOD_UAT.IIR TO DR_COMMOD_UAT_DB_READ_WRITE;


CREATE ROLE IF NOT EXISTS DR_COMMOD_IIR_READONLY;

GRANT ROLE DR_COMMOD_IIR_READONLY TO ROLE FR_YVESMINDREN;

-- Grant usage on database to roles
GRANT USAGE ON DATABASE COMMOD_UAT TO ROLE DR_COMMOD_IIR_READONLY;
GRANT USAGE ON SCHEMA COMMOD_UAT.IIR TO DR_COMMOD_IIR_READONLY;
GRANT SELECT ON ALL VIEWS  IN SCHEMA COMMOD_UAT.IIR TO DR_COMMOD_IIR_READONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA COMMOD_UAT.IIR TO DR_COMMOD_IIR_READONLY;
GRANT SELECT ON ALL TABLES  IN SCHEMA COMMOD_UAT.IIR TO DR_COMMOD_IIR_READONLY;
GRANT SELECT ON FUTURE TABLES  IN SCHEMA COMMOD_UAT.IIR TO DR_COMMOD_IIR_READONLY;
