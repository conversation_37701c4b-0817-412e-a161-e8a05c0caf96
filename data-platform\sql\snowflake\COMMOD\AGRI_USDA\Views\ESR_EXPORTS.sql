CREATE OR <PERSON><PERSON><PERSON>CE VIEW COMMOD.AGRI_USDA.ESR_EXPORTS(
	COMMODITY_CODE,
	COMMODITY_NAME,
	COUNTRY_CODE,
	COUNTRY_NAME,
	COUNTRY_DESCRIPTION,
	REGION_ID,
	REGION_NAME,
	WEEKLY_EXPORTS,
	ACCUMULATED_EXPORTS,
	OUTSTANDING_SALES,
	GROSS_NEW_SALES,
	CURRENT_MY_NET_SALES,
	CURRENT_MY_TOTAL_COMMITMENT,
	NEXT_MY_OUTSTANDING_SALES,
	NEXT_MY_NET_SALES,
	UNIT_ID,
	UNIT_NAME,
	WEEK_ENDING_DATE,
	WEEK,
	MARKET_YEAR,
	MARKET_YEAR_START,
	MARKET_YEAR_END,
	FILE_NAME,
	TIME_STAMP
) AS
SELECT
	EER.COMMODITY_CODE,
	ECMR.COMMODITY_NAME,
	EER.COUNTRY_CODE,
	TRIM(ECNR.COUNTRY_NAME) COUNTRY_NAME,
	TRIM(ECNR.COUNTRY_DESCRIPTION) COUNTRY_DESCRIPTION,
	ECNR.REGION_ID,
	ERR.REGION_NAME,
	EER.WEEKLY_EXPORTS,
	EER.ACCUMULATED_EXPORTS,
	EER.OUTSTANDING_SALES,
	EER.GROSS_NEW_SALES,
	EER.CURRENT_MY_NET_SALES,
	EER.CURRENT_MY_TOTAL_COMMITMENT,
	EER.NEXT_MY_OUTSTANDING_SALES,
	EER.NEXT_MY_NET_SALES,
	EER.UNIT_ID,
	EUR.UNIT_NAME,
	EER.WEEK_ENDING_DATE,
	GREATEST(1,CEIL(datediff(DAY, EDRD.MARKET_YEAR_START,EER.WEEK_ENDING_DATE)/7)),
	EER.MARKET_YEAR,
	EDRD.MARKET_YEAR_START,
	EDRD.MARKET_YEAR_END,
	EER.FILE_NAME,
	EER.TIME_STAMP
FROM
	COMMOD.AGRI_USDA.ESR_EXPORTS_LATEST EER
LEFT JOIN VENDOR_RAW.USDA_REFERENCE.ESR_COMMODITY_RAW ECMR
ON
	EER.COMMODITY_CODE = ECMR.COMMODITY_CODE
LEFT JOIN VENDOR_RAW.USDA_REFERENCE.ESR_COUNTRY_RAW ECNR
ON
	EER.COUNTRY_CODE = ECNR.COUNTRY_CODE
LEFT JOIN VENDOR_RAW.USDA_REFERENCE.ESR_REGION_RAW ERR
ON
	ECNR.REGION_ID = ERR.REGION_CODE
LEFT JOIN VENDOR_RAW.USDA_REFERENCE.ESR_UOM_RAW EUR
ON
	EER.UNIT_ID = EUR.UNIT_ID
LEFT JOIN (
	SELECT
		COMMODITY_CODE,
		MARKET_YEAR_START,
		MARKET_YEAR_END,
		MARKET_YEAR,
		MAX(RELEASE_TIME_STAMP) RELEASE_TIME_STAMP,
		MAX(FILE_NAME) FILE_NAME,
		MAX(TIME_STAMP) TIME_STAMP
	FROM
		VENDOR_RAW.USDA_REFERENCE.ESR_DATA_RELEASE_DATE_RAW
	GROUP BY
		1,
		2,
		3,
		4) EDRD
ON
	EDRD.COMMODITY_CODE = EER.COMMODITY_CODE
	AND EER.MARKET_YEAR = EDRD.MARKET_YEAR;