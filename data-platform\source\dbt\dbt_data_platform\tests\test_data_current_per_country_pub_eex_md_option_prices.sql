with weekend_flag as (
    select  
        case
            when date_part('DOW', CONVERT_TIMEZONE('UTC','America/New_York',CURRENT_TIMESTAMP())::DATE) IN (0,6) THEN 1
            else 0
        end as is_weekend
),

current_data as (

    select
        country_code  as country,
        count(*) as cnt
    from {{ ref("pub_eex_md_option_prices") }}
    where cast(trading_day as date) =
    case
      -- if today is Monday (dow = 1), look back to last Friday
      when date_part('dow', CONVERT_TIMEZONE('UTC','America/New_York',CURRENT_TIMESTAMP())::DATE) = 1 then CONVERT_TIMEZONE('UTC','America/New_York',CURRENT_TIMESTAMP())::DATE - 3
      -- otherwise look back exactly one day
      else CONVERT_TIMEZONE('UTC','America/New_York',CURRENT_TIMESTAMP())::DATE - 1
    end
    group by country_code 
),

all_countries as (
    select distinct country_code as country
    from {{ ref("pub_eex_md_option_prices") }}
)

select * 
from 
all_countries ac 
left join current_data cd 
on ac.country = cd.country
join weekend_flag wf 
where wf.is_weekend = 0
and coalesce(cd.cnt, 0) = 0