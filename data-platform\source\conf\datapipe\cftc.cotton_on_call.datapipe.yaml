raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/cftc/cotton_on_call"  ## Location of Raw Files
  s3_prefix: "cftc/cotton_on_call" ## Internal S3path to files

  structure: '[
    "cotton_on_call_report_$DATE$.csv"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "CFTC"

  table_map:
    CFTC_COTTON_ON_CALL_RAW:
      pattern: "cotton_on_call_report_$DATE$.csv"
      col_num: 7
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "cftc/cotton_on_call/" 
      file_format: "FF_CFTC_COTTON_ON_CALL"