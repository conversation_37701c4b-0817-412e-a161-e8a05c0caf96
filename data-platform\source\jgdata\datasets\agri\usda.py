import requests
import os
import json

key_index=0


def jg_config_path():
    configPath = os.environ.get('CONFIG_PATH', os.getcwd())
    with open(f'{configPath}/config.json', 'r') as f:
        config = json.load(f)
    return config["JG_CONFIG_PATH"]

def read_config_secrets():
    config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
    with open(config_secret_path, 'r') as f:
        config_secret = json.load(f)
    return config_secret

def get_response(url):
    global key_index
    config=read_config_secrets()
    api_keys=config["api_key"]

    url_res=requests.get(url,headers={'x-api-key':api_keys[key_index]})
    if url_res.status_code==429:
        while url_res.status_code!=200:
            if key_index<len(api_keys)-1:
                key_index=key_index+1
            else:
                key_index=0
            url_res=requests.get(url,headers={'x-api-key':api_keys[key_index]})
    if "esr/exports" in url:
            data=[{**item,'market_year': url[-4:]} for  item in  url_res.json()]
    else:
            data=url_res.json()

    return data
