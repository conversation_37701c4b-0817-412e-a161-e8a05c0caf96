CREATE OR REPLACE PROCEDURE eqvol.manage_sr_option_quote_partitions_hourly()
LANGUAGE plpgsql
AS $$
DECLARE
    base_date      DATE := current_date;
    part_date      DATE;
    part_start_ts  TIMESTAMP;
    part_end_ts    TIMESTAMP;
    part_name      TEXT;
    drop_date      DATE := base_date - INTERVAL '3 days'; 
    sql_stmt       TEXT;
    i              INT;
BEGIN
    FOR i IN 0..47 LOOP 
        part_date := base_date + (i / 24); 
        part_start_ts := part_date + ((i % 24) * INTERVAL '1 hour');  
        part_end_ts := part_start_ts + INTERVAL '1 hour';
        part_name := format('sr_option_quotes_exp_%s_%02s', to_char(part_start_ts, 'YYYYMMDD'), lpad(extract(hour FROM part_start_ts)::text, 2, '0'));
        
        -- Create the partition if it does not exist
        IF NOT EXISTS (
            SELECT 1 FROM pg_tables WHERE schemaname = 'eqvol' AND tablename = part_name
        ) THEN
            sql_stmt := format(
                'CREATE TABLE eqvol.%I PARTITION OF eqvol.sr_option_quotes_exp
                 FOR VALUES FROM (%L) TO (%L);',
                part_name,
                part_start_ts,
                part_end_ts
            );
            EXECUTE sql_stmt;
            RAISE NOTICE 'Created partition: %', part_name;

            -- Create a composite index on the new partition
            sql_stmt := format(
                'CREATE INDEX idx_%I ON eqvol.%I (ticker, expiration_date, strike_price, call_put, jg_api_req_timestamp);',
                part_name,
                part_name
            );
            EXECUTE sql_stmt;
            RAISE NOTICE 'Created composite index on partition: %', part_name;
        END IF;
    END LOOP;

    -- Drop old partitions that are 2 days old
    FOR h IN 0..23 LOOP
        part_name := format('sr_option_quotes_exp_%s_%02s', to_char(drop_date, 'YYYYMMDD'), lpad(h::TEXT, 2, '0'));
        IF EXISTS (
            SELECT 1 FROM pg_tables WHERE schemaname = 'eqvol' AND tablename = part_name
        ) THEN
            sql_stmt := format('DROP TABLE IF EXISTS eqvol.%I CASCADE;', part_name);
            EXECUTE sql_stmt;
            RAISE NOTICE 'Dropped old partition: %', part_name;
        END IF;
    END LOOP;
END;
$$;

---

CREATE OR REPLACE PROCEDURE eqvol.manage_bbg_stock_quote_partitions_hourly()
LANGUAGE plpgsql
AS $$
DECLARE
    base_date      DATE := current_date;
    part_date      DATE;
    part_start_ts  TIMESTAMP;
    part_end_ts    TIMESTAMP;
    part_name      TEXT;
    drop_date      DATE := base_date - INTERVAL '3 days'; 
    sql_stmt       TEXT;
    i              INT;
BEGIN
    FOR i IN 0..47 LOOP 
        part_date := base_date + (i / 24); 
        part_start_ts := part_date + ((i % 24) * INTERVAL '1 hour');  
        part_end_ts := part_start_ts + INTERVAL '1 hour';
        part_name := format('bbg_stock_quotes_exp_%s_%02s', to_char(part_start_ts, 'YYYYMMDD'), lpad(extract(hour FROM part_start_ts)::text, 2, '0'));
        
        -- Create the partition if it does not exist
        IF NOT EXISTS (
            SELECT 1 FROM pg_tables WHERE schemaname = 'eqvol' AND tablename = part_name
        ) THEN
            sql_stmt := format(
                'CREATE TABLE eqvol.%I PARTITION OF eqvol.bbg_stock_quotes_exp
                 FOR VALUES FROM (%L) TO (%L);',
                part_name,
                part_start_ts,
                part_end_ts
            );
            EXECUTE sql_stmt;
            RAISE NOTICE 'Created partition: %', part_name;

            -- Create a composite index on the new partition
            sql_stmt := format(
                'CREATE INDEX idx_%I ON eqvol.%I (bbg_ticker, jg_api_req_timestamp);',
                part_name,
                part_name
            );
            EXECUTE sql_stmt;
            RAISE NOTICE 'Created composite index on partition: %', part_name;
        END IF;
    END LOOP;

    -- Drop old partitions that are 2 days old
    FOR h IN 0..23 LOOP
        part_name := format('bbg_stock_quotes_exp_%s_%02s', to_char(drop_date, 'YYYYMMDD'), lpad(h::TEXT, 2, '0'));
        IF EXISTS (
            SELECT 1 FROM pg_tables WHERE schemaname = 'eqvol' AND tablename = part_name
        ) THEN
            sql_stmt := format('DROP TABLE IF EXISTS eqvol.%I CASCADE;', part_name);
            EXECUTE sql_stmt;
            RAISE NOTICE 'Dropped old partition: %', part_name;
        END IF;
    END LOOP;
END;
$$;

---

CALL eqvol.manage_sr_option_quote_partitions_hourly();


CREATE TABLE eqvol.sr_option_quotes_exp (
	ticker varchar(50) not NULL,
	call_put varchar(1) not NULL,
	strike_price int8 not NULL,
	expiration_date date not NULL,
	update_type varchar(50) null,
	bid_price float4 NULL,
	bid_size int4 NULL,
	cum_bid_size int4 NULL,
	bid_time int4 NULL,
	bid_mkt_type varchar(50) null,
	ask_price float4 NULL,
	ask_size int4 NULL,
	cum_ask_size int4 NULL,
	ask_time int4 NULL,
	ask_mkt_type varchar(50) null,
	sr_srctimestamp timestamp NULL,
	sr_nettimestamp timestamp NULL,
	jg_api_req_timestamp timestamp(6) NOT NULL,
	jg_api_recvd_timestamp timestamp(6) NOT NULL
)
PARTITION BY RANGE (jg_api_req_timestamp);


CREATE TABLE eqvol.bbg_stock_quotes_exp (
	bbg_ticker varchar(50) not NULL,
	last_price float4 NULL,
	bid float4 NULL,
	bid_size int4 NULL,
	ask float4 NULL,
	ask_size int4 NULL,
	jg_api_req_timestamp timestamp(6) NOT NULL
	api_recvd_timestamp timestamp(6) NOT NULL
)
PARTITION BY RANGE (jg_api_req_timestamp);


CREATE TABLE eqvol.sr_ticker_definition (
	ticker varchar(50) NOT NULL,
	assettype varchar(20) NOT NULL,
	tickersource varchar(20) NOT NULL,
	bbgcompositeticker varchar(50) NULL,
	symboltype varchar(50) NULL,
	"name" varchar(500) NULL,
	primaryexch varchar(20) NULL,
	country varchar(10) NULL,
	mic varchar(10) NULL,
	symbol varchar(50) NULL,
	bbgexchangeticker varchar(50) NULL,
	isin varchar(50) NULL,
	figi varchar(50) NULL,
	gics varchar(50) NULL,
	naics varchar(50) NULL,
	numoptions int4 NULL,
	bbgcompositeglobalid varchar(50) NULL,
	bbgglobalid varchar(50) NULL,
	bbgcurrency varchar(10) NULL,
	sr_timestamp timestamp(6) NULL,
	when_updated_utc timestamp(6) NOT NULL,
	CONSTRAINT ticker_definition_unique UNIQUE (ticker, assettype, tickersource)
);

CREATE TABLE eqvol.sr_option_quotes_exp_latest (
	ticker varchar(50) not NULL,
	call_put varchar(1) not NULL,
	strike_price int8 not NULL,
	expiration_date date not NULL,
	update_type character varying(50) null,
	bid_price float4 NULL,
	bid_size int4 NULL,
	cum_bid_size int4 NULL,
	bid_time int4 NULL,
	bid_mkt_type varchar(50) null,
	ask_price float4 NULL,
	ask_size int4 NULL,
	cum_ask_size int4 NULL,
	ask_time int4 NULL,
	ask_mkt_type varchar(50) null,
	sr_srctimestamp timestamp NULL,
	sr_nettimestamp timestamp NULL,
	jg_api_req_timestamp timestamp(6) NOT NULL,
	jg_api_recvd_timestamp timestamp(6) NOT NULL,
	PRIMARY KEY (ticker, expiration_date, strike_price, call_put)
);


CREATE TABLE eqvol.ticker_request_config (
	ticker varchar(50) NOT NULL,
	stripe varchar(2) NULL,
	is_active bool NOT NULL,
	when_updated_utc timestamp(6) DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'::text) NOT NULL,
	is_sr_opt_quote_enabled bool DEFAULT true NOT NULL,
	is_bbg_stock_quote_enabled bool DEFAULT true NOT NULL,
	is_bbg_div_enabled bool DEFAULT false NOT NULL,
	CONSTRAINT ticker_request_config_pkey PRIMARY KEY (ticker)
);

CREATE TABLE eqvol.sr_stripe_config(
	stripe varchar(2) not NULL,
	compound_stripe varchar(2) not NULL,
	when_updated_utc timestamp(6) not NULL DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
	PRIMARY KEY (stripe)
);

CREATE TABLE eqvol.sr_option_quote_exp_timestamps(
	snap_timestamp timestamp NOT NULL,
	snap_date date NOT NULL,
	is_cold_snap boolean NOT NULL,
	cold_parquet_path varchar(500) null,
	CONSTRAINT sr_option_quote_timestamps_pkey PRIMARY KEY (snap_timestamp)
);

CREATE TABLE eqvol.bbg_eqvol_dvd_hist_exp (
	as_of_date date NOT NULL,
	bbg_ticker varchar NOT NULL,
	bc_eqy_dvd_hist_all_ann_dt date NULL,
	bc_eqy_dvd_hist_all_ex_dt date NULL,
	bc_eqy_dvd_hist_all_rec_dt date NULL,
	bc_eqy_dvd_hist_all_pay_dt date NULL,
	dvd_freq varchar NOT NULL,
	cp_dvd_typ varchar NOT NULL,
	bc_eqy_dvd_hist_all_amt float8 NULL,
	when_updated timestamp DEFAULT (now() AT TIME ZONE 'utc'::text) NULL
);

CREATE INDEX idx_bbg_eqvol_dvd_hist_exp ON eqvol.bbg_eqvol_dvd_hist_exp USING btree (bbg_ticker, as_of_date);

CREATE TABLE eqvol.bbg_eqvol_dvd_proj_exp (
	as_of_date date NOT NULL,
	bbg_ticker varchar NOT NULL,
	dvd_ex_dt date NULL,
	dvd_declared_dt date NULL,
	bc_bdvd_per_share_amt float8 NULL,
	bc_bdvd_trend float8 NULL,
	bc_bdvd_opt_implied_range_low float8 NULL,
	bc_bdvd_opt_implied_range_high float8 NULL,
	when_updated timestamp DEFAULT (now() AT TIME ZONE 'utc'::text) NULL
);
CREATE INDEX idx_bbg_eqvol_dvd_proj_exp ON eqvol.bbg_eqvol_dvd_proj_exp USING btree (bbg_ticker, as_of_date);

CREATE TABLE eqvol.sr_option_quotes_exp_complete (
	ticker varchar(50) NOT NULL,
	call_put varchar(1) NOT NULL,
	strike_price int8 NOT NULL,
	expiration_date date NOT NULL,
	update_type varchar(50) NULL,
	bid_price float4 NULL,
	bid_size int4 NULL,
	cum_bid_size int4 NULL,
	bid_time int4 NULL,
	bid_mkt_type varchar(50) NULL,
	ask_price float4 NULL,
	ask_size int4 NULL,
	cum_ask_size int4 NULL,
	ask_time int4 NULL,
	ask_mkt_type varchar(50) NULL,
	sr_srctimestamp timestamp NULL,
	sr_nettimestamp timestamp NULL,
	jg_api_req_timestamp timestamp(6) NOT NULL,
	jg_api_recvd_timestamp timestamp(6) NOT NULL,
	CONSTRAINT sr_option_quotes_exp_complete_pkey PRIMARY KEY (ticker, expiration_date, strike_price, call_put)
);

-- Permissions

ALTER TABLE eqvol.sr_option_quotes_exp_complete OWNER TO external_write;
GRANT ALL ON TABLE eqvol.sr_option_quotes_exp_complete TO external_write;
GRANT SELECT ON TABLE eqvol.sr_option_quotes_exp_complete TO fe_risk;
GRANT SELECT ON TABLE eqvol.sr_option_quotes_exp_complete TO fe_risk_ro;
GRANT SELECT ON TABLE eqvol.sr_option_quotes_exp_complete TO mds_read;

---

CREATE OR REPLACE PROCEDURE eqvol.update_option_quotes_exp_complete()
LANGUAGE plpgsql
AS $$
BEGIN
    -- Create temporary table with filtered data (only complete quotes)
    CREATE TEMP TABLE temp_option_quotes AS
    SELECT 
        ticker, call_put, strike_price, expiration_date, update_type,
        bid_price, bid_size, cum_bid_size, bid_time, bid_mkt_type,
        ask_price, ask_size, cum_ask_size, ask_time, ask_mkt_type,
        sr_srctimestamp, sr_nettimestamp, jg_api_req_timestamp, jg_api_recvd_timestamp
    FROM eqvol.sr_option_quotes_exp_latest
    WHERE bid_price IS NOT NULL AND ask_price IS NOT NULL;
    
    -- Create index on temp table for better join performance
    CREATE INDEX idx_temp_option_quotes_pk ON temp_option_quotes (ticker, expiration_date, strike_price, call_put);
    
    -- Update existing rows
    UPDATE eqvol.sr_option_quotes_exp_complete AS target
    SET 
        update_type = source.update_type,
        bid_price = source.bid_price,
        bid_size = source.bid_size,
        cum_bid_size = source.cum_bid_size,
        bid_time = source.bid_time,
        bid_mkt_type = source.bid_mkt_type,
        ask_price = source.ask_price,
        ask_size = source.ask_size,
        cum_ask_size = source.cum_ask_size,
        ask_time = source.ask_time,
        ask_mkt_type = source.ask_mkt_type,
        sr_srctimestamp = source.sr_srctimestamp,
        sr_nettimestamp = source.sr_nettimestamp,
        jg_api_req_timestamp = source.jg_api_req_timestamp,
        jg_api_recvd_timestamp = source.jg_api_recvd_timestamp
    FROM temp_option_quotes AS source
    WHERE target.ticker = source.ticker
        AND target.expiration_date = source.expiration_date
        AND target.strike_price = source.strike_price
        AND target.call_put = source.call_put;
    
    -- Insert new rows only (rows that don't exist in target table)
    INSERT INTO eqvol.sr_option_quotes_exp_complete (
        ticker, call_put, strike_price, expiration_date, update_type,
        bid_price, bid_size, cum_bid_size, bid_time, bid_mkt_type,
        ask_price, ask_size, cum_ask_size, ask_time, ask_mkt_type,
        sr_srctimestamp, sr_nettimestamp, jg_api_req_timestamp, jg_api_recvd_timestamp
    )
    SELECT 
        ticker, call_put, strike_price, expiration_date, update_type,
        bid_price, bid_size, cum_bid_size, bid_time, bid_mkt_type,
        ask_price, ask_size, cum_ask_size, ask_time, ask_mkt_type,
        sr_srctimestamp, sr_nettimestamp, jg_api_req_timestamp, jg_api_recvd_timestamp
    FROM temp_option_quotes
    WHERE NOT EXISTS (
        SELECT 1 FROM eqvol.sr_option_quotes_exp_complete
        WHERE ticker = temp_option_quotes.ticker
            AND expiration_date = temp_option_quotes.expiration_date
            AND strike_price = temp_option_quotes.strike_price
            AND call_put = temp_option_quotes.call_put
    );
    
    -- Clean up temporary table
    DROP TABLE temp_option_quotes;
END;
$$;

GRANT EXECUTE ON PROCEDURE eqvol.update_option_quotes_exp_complete() TO external_write;


CREATE OR REPLACE FUNCTION eqvol.get_opt_quotes_for_opt_attr_sandbox(opt_attrs_json jsonb, quote_timestamp timestamp without time zone DEFAULT NULL::timestamp without time zone, lookback_hours int default 10)
 RETURNS TABLE(ticker character varying, call_put character varying, strike_price bigint, expiration_date date, is_sr_quote_avail boolean, update_type character varying, bid_price real, bid_size integer, cum_bid_size integer, bid_time integer, bid_mkt_type character varying, ask_price real, ask_size integer, cum_ask_size integer, ask_time integer, ask_mkt_type character varying, sr_srctimestamp timestamp without time zone, sr_nettimestamp timestamp without time zone, jg_api_req_timestamp timestamp without time zone, jg_api_recvd_timestamp timestamp without time zone)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
	quote_timestamp_to_use timestamp;
BEGIN
	
    IF quote_timestamp IS NULL THEN
		SELECT MAX(snap_timestamp) INTO quote_timestamp_to_use
		FROM eqvol.sr_option_quote_exp_timestamps;
	ELSE
		SELECT MAX(snap_timestamp) INTO quote_timestamp_to_use
		FROM eqvol.sr_option_quote_exp_timestamps
		WHERE snap_timestamp <= quote_timestamp
		AND snap_timestamp >= quote_timestamp - (lookback_hours * INTERVAL '1 hour');
	END IF;

    RETURN query
	WITH input_options AS (
		SELECT 
			opt_attrs.ticker,
			opt_attrs.strike_price,
			opt_attrs.expiration_date,
			opt_attrs.call_put
		FROM jsonb_to_recordset(opt_attrs_json) AS opt_attrs(
			ticker varchar,
			strike_price int8,
			expiration_date date,
			call_put varchar
	))
    select COALESCE(inp_opts.ticker, soql.ticker, complete_ts.ticker) as ticker,
	COALESCE(inp_opts.call_put, soql.call_put, complete_ts.call_put) as call_put,
	COALESCE(inp_opts.strike_price, soql.strike_price, complete_ts.strike_price) as strike_price,
	COALESCE(inp_opts.expiration_date, 	soql.expiration_date, complete_ts.expiration_date) as expiration_date,
	case when soql.ticker is null and complete_ts.ticker is null then false else true end as is_sr_quote_avail,
	COALESCE(soql.update_type, complete_ts.update_type) as update_type,
	COALESCE(soql.bid_price, complete_ts.bid_price) as bid_price,
	COALESCE(soql.bid_size, complete_ts.bid_size) as bid_size,
	COALESCE(soql.cum_bid_size, complete_ts.cum_bid_size) as cum_bid_size,
	COALESCE(soql.bid_time, complete_ts.bid_time) as bid_time,
	COALESCE(soql.bid_mkt_type, complete_ts.bid_mkt_type) as bid_mkt_type,
	COALESCE(soql.ask_price, complete_ts.ask_price) as ask_price,
	COALESCE(soql.ask_size, complete_ts.ask_size) as ask_size,
	COALESCE(soql.cum_ask_size, complete_ts.cum_ask_size) as cum_ask_size,
	COALESCE(soql.ask_time, complete_ts.ask_time) as ask_time,
	COALESCE(soql.ask_mkt_type, complete_ts.ask_mkt_type) as ask_mkt_type,
	COALESCE(soql.sr_srctimestamp, complete_ts.sr_srctimestamp) as sr_srctimestamp,
	COALESCE(soql.sr_nettimestamp, complete_ts.sr_nettimestamp) as sr_nettimestamp,
	COALESCE(soql.jg_api_req_timestamp, complete_ts.jg_api_req_timestamp) as jg_api_req_timestamp,
	COALESCE(soql.jg_api_recvd_timestamp, complete_ts.jg_api_recvd_timestamp) as jg_api_recvd_timestamp
	from input_options inp_opts left outer join 
	eqvol.sr_option_quotes_exp_complete complete_ts on inp_opts.ticker = complete_ts.ticker and inp_opts.expiration_date = complete_ts.expiration_date and inp_opts.strike_price = complete_ts.strike_price and inp_opts.call_put = complete_ts.call_put left outer join
	eqvol.sr_option_quotes_exp soql on (soql.jg_api_req_timestamp = quote_timestamp_to_use and inp_opts.ticker = soql.ticker and inp_opts.expiration_date = soql.expiration_date and inp_opts.strike_price = soql.strike_price and inp_opts.call_put = soql.call_put)
	where COALESCE(complete_ts.jg_api_req_timestamp, quote_timestamp_to_use) >= quote_timestamp_to_use - (lookback_hours * INTERVAL '1 hour');
    
    
END;
$function$
;

-- Permissions

ALTER FUNCTION eqvol.get_opt_quotes_for_opt_attr_sandbox(jsonb, timestamp, int) OWNER TO external_write;
GRANT ALL ON FUNCTION eqvol.get_opt_quotes_for_opt_attr_sandbox(jsonb, timestamp, int) TO external_write;

CREATE TABLE eqvol.sr_root_definition (
    root VARCHAR(20),
    assettype VARCHAR(20),
    tickersource VARCHAR(20),
    ticker VARCHAR(20),
    osi_root VARCHAR(20),
    option_type VARCHAR(20),
    multi_hedge VARCHAR(20),
    exercise_time VARCHAR(20),
    exercise_type VARCHAR(20),
    time_metric VARCHAR(20),
    trading_period VARCHAR(50),
    pricing_model VARCHAR(50),
    moneyness_type VARCHAR(50),
    price_quote_type VARCHAR(20),
    volume_tier VARCHAR(20),
    position_limit INT,
    exchanges VARCHAR(100),
    tick_value FLOAT4,
    point_value FLOAT4,
    point_currency VARCHAR(20),
    strike_scale FLOAT4,
    strike_ratio FLOAT4,
    cash_on_exercise FLOAT4,
    premium_mult FLOAT4,
    symbol_ratio FLOAT4,
    adj_convention VARCHAR(20),
    opt_price_inc VARCHAR(20),
    price_format VARCHAR(50),
    min_tick_size FLOAT4,
    trade_curr VARCHAR(20),
    settle_curr VARCHAR(20),
    strike_curr VARCHAR(20),
    ric_root VARCHAR(20),
    bbg_root VARCHAR(20),
    bbg_group VARCHAR(20),
    timestamp timestamp,
    when_updated_utc TIMESTAMP(6),
	CONSTRAINT root_definition_unique UNIQUE (root, assettype, tickersource)
);

ALTER TABLE eqvol.sr_root_definition OWNER TO external_write;
GRANT ALL ON TABLE eqvol.sr_root_definition TO external_write;
GRANT SELECT ON TABLE eqvol.sr_root_definition TO fe_risk;
GRANT SELECT ON TABLE eqvol.sr_root_definition TO fe_risk_ro;
GRANT SELECT ON TABLE eqvol.sr_root_definition TO mds_read;