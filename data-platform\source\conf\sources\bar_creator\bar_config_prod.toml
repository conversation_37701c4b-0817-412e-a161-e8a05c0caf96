# Main head containing different configuration sets
[config_heads]

# Configuration set for the first tick file
[config_heads.bpipe]
bar_size = 1
tick_file_prefix = 'bpipe'
identifier_query = "SELECT DISTINCT IDENTIFIER FROM BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG where SNAP_TYPE like 'BPIPE-BAR'"
subscribe_field = ['LAST_PRICE', 'ASK', 'BID', 'EID', 'MKTDATA_EVENT_TYPE', 'MKTDATA_EVENT_SUBTYPE', 'IS_DELAYED_STREAM']
tick_dir = "/opt/data/rawdata"
last_processed_dir = "/opt/data/rawdata/bpipe_bar_last_processed_config_prod"
#tick_dir = "/jfs/tech1/apps/datait/vikram/rawdata"
#last_processed_dir = "/jfs/tech1/apps/datait/vikram/rawdata/bpipe_bar_last_processed_config_prod"
db_name = "BLOOMBERG"
wait_time_seconds = 0.002
field_names = ["ASK", "BID", "LAST_PRICE", "VOLUME", "TIME"]
enable_logging = true
log_file = ""

[config_heads.bpipe_tick_asia]
bar_size = 1
tick_file_prefix = 'bpipe_tick_asia'
identifier_query = "SELECT DISTINCT IDENTIFIER FROM BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG where SNAP_TYPE like 'BPIPE-TICK'"
subscribe_field = ['LAST_PRICE', 'ASK', 'BID', 'EID', 'MKTDATA_EVENT_TYPE', 'MKTDATA_EVENT_SUBTYPE', 'IS_DELAYED_STREAM']
tick_dir = "/opt/data/rawdata_uat"
db_name = "BLOOMBERG"
wait_time_seconds = 0.002
field_names = ["ASK", "BID", "LAST_PRICE", "VOLUME", "TIME"]
enable_logging = true
log_file = ""
