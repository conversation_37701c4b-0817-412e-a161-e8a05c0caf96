raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/gtt/1.0/"  ## Location of Raw Files
  s3_prefix: "gtt" ## Internal S3path to files

structure: '[
  "gtt_report_$DATE$_I_USD_.csv",
  "gtt_report_$DATE$_E_USD_.csv",
]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "GTT"

  table_map:
    GTT_IMPORT_USD_RAW:
      pattern: "gtt_report_$DATE$_I_USD_.csv" ## Need to be a regex format
      col_num: 27 #TODO: check the number of columns in the file
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "gtt/" ##<stage name>/<stage path>
      file_format: "FF_TRADE_DETAILS"

    GTT_EXPORT_USD_RAW:
      pattern: "gtt_report_$DATE$_E_USD_.csv" ## Need to be a regex format
      col_num: 27 #TODO: check the number of columns in the file
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "gtt/" ##<stage name>/<stage path>
      file_format: "FF_TRADE_DETAILS"