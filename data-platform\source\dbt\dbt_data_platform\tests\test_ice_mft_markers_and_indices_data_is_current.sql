{{ 
    config(
        tags=["markersandindices"]
    ) 
}}

with weekend_flag as (
    select  
        case
            when date_part('DOW', convert_timezone('UTC', 'America/New_York', current_timestamp())::DATE) IN (0,6) THEN 1
            else 0
        end as is_weekend
),
today_data as (
    select
        count(*) as cnt
    from {{ ref('pub_ice_mft_markers_and_indices') }}
    where date = convert_timezone('UTC', 'America/New_York', current_timestamp())::DATE
)
select 
    td.cnt,
    wf.is_weekend
from 
today_data td
join weekend_flag wf 
where wf.is_weekend = 0
and coalesce(td.cnt, 0) = 0 