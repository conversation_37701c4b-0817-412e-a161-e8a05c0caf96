----------------------
-- ROLES
----------------------
USE ROLE SECURITYADMIN;


CREATE ROLE IF NOT EXISTS DR_MDS_READ_ONLY;
CREATE ROLE IF NOT EXISTS DR_MDS_READ_WRITE;
CREATE ROLE IF NOT EXISTS DR_MDS_DB_OWNER;

GRANT ROLE DR_MDS_DB_OWNER TO ROLE FR_DATA_PLATFORM_LEADS ;
GRANT ROLE DR_MDS_READ_WRITE TO ROLE DR_MDS_DB_OWNER;
GRANT ROLE DR_MDS_READ_ONLY TO ROLE DR_MDS_READ_WRITE;

GRANT ROLE DR_MDS_READ_WRITE TO USER SVC_DATAIT_MDS;
GRANT ROLE DR_MDS_READ_ONLY TO ROLE FR_DATA_PLATFORM;



USE ROLE SYSADMIN ;

CREATE DATABASE MDS;

USE DATABASE MDS;

-- Warehouse
CREATE WAREHOUSE IF NOT EXISTS MDS_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60           -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE MDS_WH TO ROLE DR_MDS_READ_WRITE;
GRANT USAGE ON WAREHOUSE MDS_WH TO ROLE DR_MDS_DB_OWNER;

USE WAREHOUSE MDS_WH ;


----------------------
--DB OWNERSHIP
----------------------
GRANT OWNERSHIP ON DATABASE MDS TO ROLE DR_MDS_DB_OWNER;
GRANT USAGE ON DATABASE MDS TO ROLE DR_MDS_READ_ONLY;
GRANT USAGE ON DATABASE MDS TO ROLE DR_MDS_READ_WRITE;

----------------------
--- FUTURE STATEMENTS 
----------------------
USE ROLE SECURITYADMIN;

-- FUTURE OWNERSHIP TO OWNER ROLE(ALWAYS)
--Ensure that the owner role is always the owner of schemas
GRANT OWNERSHIP ON FUTURE SCHEMAS IN DATABASE MDS TO ROLE DR_MDS_DB_OWNER;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE MDS TO ROLE DR_MDS_READ_ONLY;
GRANT USAGE ON FUTURE SCHEMAS IN DATABASE MDS TO ROLE DR_MDS_READ_WRITE;


----
--FUTURE GRANTS READ WRITE ROLE
----
GRANT SELECT,INSERT, UPDATE, DELETE, TRUNCATE ON FUTURE TABLES IN DATABASE MDS TO ROLE DR_MDS_READ_WRITE;
GRANT SELECT ON FUTURE VIEWS IN DATABASE MDS TO ROLE DR_MDS_READ_WRITE;

----
--FUTURE GRANTS READ ROLE
----
GRANT SELECT ON FUTURE TABLES IN DATABASE MDS TO ROLE DR_MDS_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN DATABASE MDS TO ROLE DR_MDS_READ_ONLY;



USE ROLE DR_MDS_DB_OWNER;


CREATE SCHEMA MDS.MDS;

GRANT SELECT, INSERT, UPDATE, DELETE ON FUTURE TABLES IN SCHEMA MDS.MDS TO ROLE DR_MDS_READ_WRITE;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA MDS.MDS TO ROLE DR_MDS_READ_WRITE;


GRANT SELECT ON FUTURE TABLES IN SCHEMA MDS.MDS TO ROLE DR_MDS_READ_ONLY;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA MDS.MDS TO ROLE DR_MDS_READ_ONLY;
