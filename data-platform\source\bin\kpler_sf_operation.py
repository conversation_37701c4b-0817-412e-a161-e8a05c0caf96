from datetime import datetime

import pandas as pd
import json,os
import logging
logging.getLogger().setLevel(logging.INFO)


output=''
from strunner import *
setupEnvironment()
from stcommon.infra.rds.snowflake_bulk_loader import SnowflakeBulkLoader
from stcommon.infra.python.fileio import read_toml

parser = argparse.ArgumentParser(description="Process ENV")

parser.add_argument("--env", required=True)

args = parser.parse_args()

env=args.env

configPath = os.environ.get('CONFIG_PATH', os.getcwd())
 
config=read_toml(f'{configPath}/conf/sources/kpler_liquids.toml')


commod_db=config["config"][env]["database"]
commod_schema=config["config"][env]["schema"]

loader= SnowflakeBulkLoader(warehouse="DATA_PLATFORM_WH",database=commod_db,schema=f"{commod_db}.{commod_schema}",role="FR_DATA_PLATFORM")


config_file_query = f'select * from {commod_db}.{commod_schema}.kpler_columns_metadata'
cur=loader.cursor
cur.execute(config_file_query)


# Load configuration
columns_mapping = cur.fetch_pandas_all()

# Create dictionaries for cols
columns_dict = dict(zip(columns_mapping['original'.upper()], columns_mapping['mapped'.upper()]))
data_types_dict = dict(zip(columns_mapping['mapped'.upper()], columns_mapping['sf_type'.upper()]))

set_clause=''

for key,value in columns_dict.items():
    if data_types_dict[value]=='DATETIME':
        set_clause+=f"""{value} = try_to_timestamp(nullif(COALESCE(JSON_EXTRACT_PATH_TEXT(jsonupdates,'{key}'),k.{value}::TEXT),'') ),"""

    elif 'NUMBER' in data_types_dict[value]:
        set_clause+=f"""{value} = try_to_number(nullif(COALESCE(JSON_EXTRACT_PATH_TEXT(jsonupdates,'{key}'),k.{value}::TEXT),'') ),"""


    elif key not in ["product_id","trade_id","update_id"]:
        set_clause+=f"""{value} = nullif(COALESCE(JSON_EXTRACT_PATH_TEXT(jsonupdates,'{key}'),k.{value}),'') ,"""

set_clause=set_clause[:-1]
    
# Get timestamp from filenames
def extract_timestamp(filename):
    try:
        timestamp_str = filename.split('_')[-1].replace('.csv', '')
        return datetime.strptime(timestamp_str, '%Y-%m-%dT%H%M%S')
    except Exception:
        return None

# Map json_updates to new cols
def map_json_updates(json_updates, columns_dict):
    mapped = {columns_dict.get(k, k): v for k, v in json_updates.items()}
    if 'trade_id' in columns_dict.values() and 'tradeId' in json_updates:
        mapped['trade_id'] = json_updates['tradeId']
    if 'product_id' in columns_dict.values() and 'productId' in json_updates:
        mapped['product_id'] = json_updates['productId']
    return mapped


main_query=f'select * from {commod_db}.{commod_schema}.KPLER_UPDATES where status is NULL '

def get_table_name(file_name):
    if 'Crude' in file_name:
        table_name='kpler_crude'
    elif 'Gasoline' in file_name:
        table_name='kpler_gasoline'
    elif 'Distillates' in file_name:
        table_name='kpler_distillates'
    elif 'DPP' in file_name:
        table_name='kpler_dpp'
    return table_name

query=f'select * from {commod_db}.{commod_schema}.KPLER_PRODUCTS'
cur=loader.cursor
cur.execute(query)

columns=[col[0] for col in cur.description]
source_results=[dict(zip(columns,row)) for row in cur.fetchall()]
        
for row in source_results:
    inserted=0


    source=row["SOURCE"]
    table_name=get_table_name(source)


    delete_query=f"""
    DELETE from {commod_db}.{commod_schema}.{table_name} where update_id in (select trade_id||'_'||product_id  from {commod_db}.{commod_schema}.KPLER_UPDATES where status is NULL and file_name like '%{source}%' and operation = 'DELETE')
    """
    logging.info(f"Deleted Records for {source}")
    cur.execute(delete_query)
    deleted=cur.rowcount


    
    update_query=f"UPDATE {commod_db}.{commod_schema}.KPLER_UPDATES  set status='DONE' where  operation= 'DELETE' and status is null and file_name like '%{source}%' "
    logging.info(update_query)
    cur.execute(update_query)



    query1=main_query+f" and file_name like '%{source}%' and operation = 'INSERT'"
    # logging.info(query1)

    cur=loader.cursor
    cur.execute(query1)
    columns=[col[0] for col in cur.description]
    results=[dict(zip(columns,row)) for row in cur.fetchall()]
    logging.info(table_name)

    for res in results:
        product_id=res["PRODUCT_ID"]
        trade_id=res["TRADE_ID"]
        operation=res['OPERATION']
        date_to_check=res["DATE"]
        update_id = f"{trade_id}_{product_id}"

        json_updates = json.loads(res['JSONUPDATES']) if pd.notna(res['JSONUPDATES']) else {}
        mapped_json_updates = map_json_updates(json_updates, columns_dict)
            

        if operation=='INSERT':
            
            columns = ['update_id','trade_id', 'product_id'] + [k for k in mapped_json_updates.keys() if k not in ['trade_id', 'product_id']]
            placeholders = [ '%s', '%s', '%s'] + ['%s' for k, _ in mapped_json_updates.items() if k not in ['trade_id', 'product_id']]

            params = [ update_id, str(trade_id), str(product_id)] + [v.replace("'","''") for k, v in mapped_json_updates.items() if k not in ['trade_id', 'product_id']]
            query = f"""INSERT INTO {commod_db}.{commod_schema}.{table_name} ({", ".join(columns)}) select {", ".join(f"'{param}'" if param else "null" for param in params)} where not exists( select 1 from {commod_db}.{commod_schema}.{table_name} where update_id='{update_id}');"""
            logging.info(query) 

            cur.execute(query)
            inserted=inserted+1


        update_query=f"UPDATE {commod_db}.{commod_schema}.KPLER_UPDATES  set status='DONE' where product_id = {product_id} and trade_id={trade_id} and operation= '{operation}' and status is null and date ='{date_to_check}'"
        logging.info(update_query)
        cur.execute(update_query)

    merge_update_query= f"""merge into {commod_db}.{commod_schema}.{table_name} k
    using(
    select * from {commod_db}.{commod_schema}.kpler_updates 
    where  file_name like '%{source}%'
    AND    status is null
    and operation='UPDATE' ) u
    on     k.product_id = u.product_id
    and    k.trade_id = u.trade_id
    when matched then update 
        SET     {set_clause}
    """
    
    logging.info(f"Executed Merge Query for {source}")
    cur.execute(merge_update_query)
    updated=cur.rowcount

    update_query=f"UPDATE {commod_db}.{commod_schema}.KPLER_UPDATES  set status='DONE' where  operation= 'UPDATE' and status is null and file_name like '%{source}%' "
    logging.info(update_query)
    cur.execute(update_query)



    output+=f"Performed operation for {source}:\n"
    output+=f"Inserted: {inserted}\n"
    output+=f"Updated: {updated}\n"
    output+=f"Deleted: {deleted}\n"

logging.info(output)
