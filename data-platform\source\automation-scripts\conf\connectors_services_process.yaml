connectors:
  - property_loc: "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/snowpipe-streaming/config/snowflakeconnector-tpicap-prod.properties"
    log_directory: "/jfs/tech1/apps/datait/jg-logs/connectors/tpicap"
    server: "************"

  - property_loc: "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/snowpipe-streaming/config/snowflakeconnector-bpipe-tick-prod.properties"
    log_directory: "/jfs/tech1/apps/datait/jg-logs/connectors/bpipe_tick"
    server: "************"

  - property_loc: "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/snowpipe-streaming/config/snowflakeconnector-refinitiv-prod.properties"
    log_directory: "/jfs/tech1/apps/datait/jg-logs/connectors/refinitiv"
    server: "************"

  - property_loc: "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/snowpipe-streaming/config/snowflakeconnector-bpipe-bar-prod.properties"
    log_directory: "/jfs/tech1/apps/datait/jg-logs/connectors/bpipe_bar"
    server: "************"


services:
  - systemd_svc_name: "tp_icap_prod.service"
    service_server: "jsvc-datait@*************,jsvc-datait@************"
    cron_expression: "* * * * *"

  - systemd_svc_name: "bpipe_tick_prod.service"
    service_server: "jsvc-datait@*************,jsvc-datait@************"
    cron_expression: "* * * * *"

  - systemd_svc_name: "refinitiv_prod.service"
    service_server: "jsvc-datait@*************,jsvc-datait@************"
    cron_expression: "* * * * *"

  - systemd_svc_name: "bpipe_bar_prod.service"
    service_server: "jsvc-datait@*************,jsvc-datait@************"  
    cron_expression: "* * * * *"

  - systemd_svc_name: "snowpipe-streaming-tpicap-prod.service"
    service_server: "jsvc-datait@************"  
    cron_expression: "* * * * *"

  - systemd_svc_name: "snowpipe-streaming-bpipe-tick-prod.service"
    service_server: "jsvc-datait@************"  
    cron_expression: "* * * * *"

  - systemd_svc_name: "snowpipe-streaming-refinitiv-prod.service "
    service_server: "jsvc-datait@************"  
    cron_expression: "* * * * *"

  - systemd_svc_name: "snowpipe-streaming-bpipe-bar-prod.service "
    service_server: "jsvc-datait@************"  
    cron_expression: "* * * * *"


process:
  - process_name: "Agent.Listener"
    process_server: "jsvc-datait@************"
    cron_expression: "* * * * *"

  - process_name: "run.sh"
    process_server: "jsvc-datait@************"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-foit/JG-FO-tools/services/../bin/main.py"
    process_server: "jsvc-foit@***********"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-foit/jg_fotools_ecm/services/../bin/main.py"
    process_server: "jsvc-foit@***********"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-foit/jg_fotools_ecm/services/../extras/realtime_cmg/push.py"
    process_server: "jsvc-foit@***********"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-foit/jg_fotools_ecm/services/../extras/realtime_cmg/recieve.py"
    process_server: "jsvc-foit@***********"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-foit/jg_fotools_restrictedlist/services/../bin/main.py"
    process_server: "jsvc-foit@***********"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-foit/jg_fotools_workingcapital/services/../bin/main.py"
    process_server: "jsvc-foit@***********"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-foit/jg_fotools_workingcapital/services/../extras/wcs_arc_upload/wcs_arc_upload_recieve.py"
    process_server: "jsvc-foit@***********"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-foit/JG-FO-tools/services/../extras/notifications/notification_recieve.py"
    process_server: "jsvc-foit@***********"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-treasit/treasury/locate/JG-LOCATE-HUB/ftp_monitor/locate_processes/treasury_to_pb.py"
    process_server: "jsvc-treasit@*************"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-treasit/treasury/locate/JG-LOCATE-HUB/ftp_monitor/locate_processes/send_to_flex.py"
    process_server: "jsvc-treasit@*************"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-treasit/treasury/locate/JG-LOCATE-HUB/ftp_monitor/locate_processes/get_pb_response.py"
    process_server: "jsvc-treasit@************"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-treasit/treasury/locate/JG-LOCATE-HUB/ftp_monitor/locate_processes/pb_file_transfer.py"
    process_server: "jsvc-treasit@************"
    cron_expression: "* * * * *"

  - process_name: "'main:app --host ************ --port 8000'"
    process_server: "jsvc-dataops@************"
    cron_expression: "* * * * *"

  - process_name: "main.py"
    process_server: "jsvc-dataops@************"
    cron_expression: "* * * * *"

  - process_name: "'main:app --host ************* --port 8001'"
    process_server: "jsvc-dataops@*************"
    cron_expression: "* * * * *"

  - process_name: "'main:app --host ************* --port 8000'"
    process_server: "jsvc-dataops@*************"
    cron_expression: "* * * * *"

  - process_name: "main.py"
    process_server: "jsvc-dataops@*************"
    cron_expression: "* * * * *"

  - process_name: "promtail-linux-amd64"
    process_server: "jsvc-treasit@************"
    cron_expression: "* * * * *"

  - process_name: "promtail-linux-amd64"
    process_server: "jsvc-treasit@*************"
    cron_expression: "* * * * *"

  - process_name: "/opt/jsvc-foit/jg_fotools_restrictedlist/services/../extras/tas_insert/tas_insert_recieve.py"
    process_server: "jsvc-foit@***********"
    cron_expression: "* * * * *"
