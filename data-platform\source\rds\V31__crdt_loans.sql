SET search_path TO var;

------------------------------------------------------------------------------------------------


CREATE TABLE IF NOT EXISTS crdt.spg_loanx_intraday_quotes (
	loanx_id varchar NOT NULL,
	mark_date date NOT NULL,
	price_timestamp timestamp NOT NULL,
	bid float8 NOT NULL,
	offer float8 NOT NULL,
	depth int4 NOT NULL,
	load_timestamp timestamp NOT NULL,
	CONSTRAINT spg_loanx_intraday_quotes_pkey PRIMARY KEY (loanx_id, mark_date, price_timestamp)
);

CREATE TABLE IF NOT EXISTS crdt.stg_spg_loanx_intraday_quotes (
	loanx_id varchar NOT NULL,
	mark_date date NOT NULL,
	price_timestamp timestamp NOT NULL,
	bid float8 NOT NULL,
	offer float8 NOT NULL,
	depth int4 NOT NULL,
	load_timestamp timestamp NOT NULL,
	CONSTRAINT stg_spg_loanx_intraday_quotes_pkey PRIMARY KEY (loanx_id, mark_date, price_timestamp)
);

------------------------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS crdt.spg_loanx_marks_nyc (
	loanx_id varchar NOT NULL,
	mark_date date NOT NULL,
	bid float8 NOT NULL,
	offer float8 NOT NULL,
	depth int NOT NULL,
	evaluated_price float8 NOT NULL,
	close_bid float8 NOT NULL,
	close_offer float8 NOT NULL,
	close_date date NOT NULL,
	contributed varchar NOT NULL,
	load_timestamp timestamp NOT NULL,
	CONSTRAINT spg_loanx_marks_nyc_pkey PRIMARY KEY (loanx_id, mark_date)
);

grant select on table crdt.spg_loanx_marks_nyc to fe_risk;
grant select on table crdt.spg_loanx_marks_nyc to fe_risk_ro;

grant select on table crdt.spg_loanx_intraday_quotes to fe_risk;
grant select on table crdt.spg_loanx_intraday_quotes to fe_risk_ro;