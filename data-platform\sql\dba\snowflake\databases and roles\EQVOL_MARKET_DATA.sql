use role ACCOUNTADMIN;

CREATE DATABASE EQVOL_MARKET_DATA IF NOT EXISTS;

CREATE ROLE IF NOT EXISTS DR_EQVOL_MARKET_DATA_OWNER;
CREATE ROLE IF NOT EXISTS DR_EQVOL_MARKET_DATA_READER;

GRANT OWNERSHIP ON DATABASE EQVOL_MARKET_DATA TO ROLE DR_EQVOL_MARKET_DATA_OWNER;

--added
GRANT ROLE DR_EQVOL_MARKET_DATA_OWNER to ROLE ACCOUNTADMIN;

--added
USE ROLE DR_EQVOL_MARKET_DATA_OWNER;

GRANT USAGE ON DATABASE EQVOL_MARKET_DATA TO ROLE DR_EQVOL_MARKET_DATA_OWNER;
GRANT USAGE ON DATABASE EQVOL_MARKET_DATA TO ROLE DR_EQVOL_MARKET_DATA_READER;

USE ROLE DR_EQVOL_MARKET_DATA_OWNER;

USE DATABASE EQVOL_MARKET_DATA;

CREATE SCHEMA IF NOT EXISTS SPIDERROCK;
CREATE SCHEMA IF NOT EXISTS BLOOMBERG;
CREATE SCHEMA IF NOT EXISTS REFERENCE;
CREATE SCHEMA IF NOT EXISTS CBOE;

GRANT OWNERSHIP ON SCHEMA SPIDERROCK TO ROLE DR_EQVOL_MARKET_DATA_OWNER;
GRANT OWNERSHIP ON SCHEMA BLOOMBERG TO ROLE DR_EQVOL_MARKET_DATA_OWNER;
GRANT OWNERSHIP ON SCHEMA REFERENCE TO ROLE DR_EQVOL_MARKET_DATA_OWNER;
GRANT OWNERSHIP ON SCHEMA CBOE      TO ROLE DR_EQVOL_MARKET_DATA_OWNER;


GRANT USAGE ON SCHEMA SPIDERROCK TO ROLE DR_EQVOL_MARKET_DATA_READER;
GRANT USAGE ON SCHEMA BLOOMBERG TO ROLE DR_EQVOL_MARKET_DATA_READER;
GRANT USAGE ON SCHEMA REFERENCE TO ROLE DR_EQVOL_MARKET_DATA_READER;
GRANT USAGE ON SCHEMA CBOE      TO ROLE DR_EQVOL_MARKET_DATA_READER;


GRANT SELECT ON ALL TABLES IN SCHEMA SPIDERROCK TO ROLE DR_EQVOL_MARKET_DATA_READER;
GRANT SELECT ON ALL VIEWS IN SCHEMA SPIDERROCK TO ROLE DR_EQVOL_MARKET_DATA_READER;

GRANT SELECT ON ALL TABLES IN SCHEMA BLOOMBERG TO ROLE DR_EQVOL_MARKET_DATA_READER;
GRANT SELECT ON ALL VIEWS IN SCHEMA BLOOMBERG TO ROLE DR_EQVOL_MARKET_DATA_READER;

GRANT SELECT ON ALL TABLES IN SCHEMA REFERENCE TO ROLE DR_EQVOL_MARKET_DATA_READER;
GRANT SELECT ON ALL VIEWS IN SCHEMA REFERENCE TO ROLE DR_EQVOL_MARKET_DATA_READER;

GRANT SELECT ON ALL TABLES IN SCHEMA CBOE TO ROLE DR_EQVOL_MARKET_DATA_READER;
GRANT SELECT ON ALL VIEWS  IN SCHEMA CBOE TO ROLE DR_EQVOL_MARKET_DATA_READER;


GRANT SELECT ON ALL TABLES IN SCHEMA SPIDERROCK TO ROLE DR_EQVOL_MARKET_DATA_OWNER;
GRANT SELECT ON ALL VIEWS IN SCHEMA SPIDERROCK TO ROLE DR_EQVOL_MARKET_DATA_OWNER;

GRANT SELECT ON ALL TABLES IN SCHEMA BLOOMBERG TO ROLE DR_EQVOL_MARKET_DATA_OWNER;
GRANT SELECT ON ALL VIEWS IN SCHEMA BLOOMBERG TO ROLE DR_EQVOL_MARKET_DATA_OWNER;

GRANT SELECT ON ALL TABLES IN SCHEMA REFERENCE TO ROLE DR_EQVOL_MARKET_DATA_OWNER;
GRANT SELECT ON ALL VIEWS IN SCHEMA REFERENCE TO ROLE DR_EQVOL_MARKET_DATA_OWNER;

USE ROLE ACCOUNTADMIN;


GRANT SELECT ON FUTURE TABLES IN SCHEMA SPIDERROCK TO ROLE DR_EQVOL_MARKET_DATA_READER;
GRANT SELECT ON FUTURE VIEWS IN SCHEMA SPIDERROCK TO ROLE DR_EQVOL_MARKET_DATA_READER;

GRANT SELECT ON FUTURE TABLES IN SCHEMA BLOOMBERG TO ROLE DR_EQVOL_MARKET_DATA_READER;
GRANT SELECT ON FUTURE VIEWS IN SCHEMA BLOOMBERG TO ROLE DR_EQVOL_MARKET_DATA_READER;

GRANT SELECT ON FUTURE TABLES IN SCHEMA REFERENCE TO ROLE DR_EQVOL_MARKET_DATA_READER;
GRANT SELECT ON FUTURE VIEWS IN SCHEMA REFERENCE TO ROLE DR_EQVOL_MARKET_DATA_READER;

GRANT SELECT ON FUTURE TABLES IN SCHEMA SPIDERROCK TO ROLE DR_EQVOL_MARKET_DATA_OWNER;
GRANT SELECT ON FUTURE VIEWS IN SCHEMA SPIDERROCK TO ROLE DR_EQVOL_MARKET_DATA_OWNER;

GRANT SELECT ON FUTURE TABLES IN SCHEMA BLOOMBERG TO ROLE DR_EQVOL_MARKET_DATA_OWNER;
GRANT SELECT ON FUTURE VIEWS IN SCHEMA BLOOMBERG TO ROLE DR_EQVOL_MARKET_DATA_OWNER;

GRANT SELECT ON FUTURE TABLES IN SCHEMA REFERENCE TO ROLE DR_EQVOL_MARKET_DATA_OWNER;
GRANT SELECT ON FUTURE VIEWS IN SCHEMA REFERENCE TO ROLE DR_EQVOL_MARKET_DATA_OWNER;

GRANT SELECT ON FUTURE TABLES IN SCHEMA CBOE TO ROLE DR_EQVOL_MARKET_DATA_OWNER;
GRANT SELECT ON FUTURE VIEWS  IN SCHEMA CBOE TO ROLE DR_EQVOL_MARKET_DATA_OWNER;


GRANT ROLE DR_EQVOL_MARKET_DATA_OWNER TO ROLE FR_DATA_PLATFORM;
GRANT ROLE DR_EQVOL_MARKET_DATA_READER TO ROLE FR_QUANTMODELING;
GRANT ROLE DR_EQVOL_MARKET_DATA_READER TO ROLE FR_EQVOL;


-- Warehouse
CREATE WAREHOUSE IF NOT EXISTS EQVOL_MD_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 60         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE EQVOL_MD_WH TO ROLE DR_EQVOL_MARKET_DATA_OWNER;

CREATE WAREHOUSE IF NOT EXISTS EQVOL_WH
    WAREHOUSE_SIZE = 'X-SMALL'  -- You can adjust the size based on your needs
    AUTO_SUSPEND = 300         -- Suspends after 5 minutes of inactivity
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE;

GRANT USAGE ON WAREHOUSE EQVOL_WH TO ROLE FR_EQVOL;

GRANT ROLE DR_EQVOL_MARKET_DATA_OWNER TO USER SVC_DATAIT;
GRANT ROLE DR_EQVOL_MARKET_DATA_OWNER TO USER DATA_PLATFORM_USER;