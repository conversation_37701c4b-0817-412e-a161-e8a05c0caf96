from airflow import DAG
from airflow.providers.ssh.operators.ssh import SSHOperator
from airflow.providers.ssh.hooks.ssh import SSHHook
from datetime import datetime, timedelta
from airflow.timetables.trigger import CronTriggerTimetable
from util.jg_ssh_dbt_operator import JainGlobalSSHDBTOperator
from airflow.sensors.time_delta import TimeDeltaSensor
import os
import pendulum
from airflow.hooks.base import BaseHook

dag_id = os.path.basename(__file__).replace(".py", "")
CURRENT_DATE = datetime.now().strftime("%Y%m%d")

def get_jg_env():
    env_value = os.getenv('jg_env', 'prod').lower()
    if env_value not in ['prod', 'uat']:
        raise ValueError(f"Invalid jg_env value: {env_value}. Allowed values are 'prod' or 'uat'.")
    return env_value

env = get_jg_env()

source='eex'
dataset = 'spot'
jsvc_path = '/opt/jsvc-datait'
source_path = f'{jsvc_path}/prod/JG-DATA-PLATFORM/source'
python_file_path = f'{source_path}/bin'
command_pipeline_rawdata = f'--dataset {source}.{dataset} --date {CURRENT_DATE}'
command_copy = f'--dataset {source}.{dataset} --date {CURRENT_DATE} --keypair'

conn = BaseHook.get_connection("dbt_conn_id")
key_file = conn.extra_dejson.get("path")

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": pendulum.yesterday(tz="Europe/Berlin"),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

doc_md_DAG=f'Create dbt run command for {source}.{dataset}'
with DAG(
    dag_id=dag_id,
    default_args=default_args,
    description=f'This DAG runs ETL in DBT for {source} {dataset}',
    schedule=CronTriggerTimetable('0,30 11,12,13,14,17 * * *',timezone="Europe/Berlin"),
    tags=["phdata", dataset, source, "dbt"],
    catchup=False,
    doc_md=doc_md_DAG
) as dag:


    ssh_hook = SSHHook(
        remote_host='*************',
        username='jsvc-datait',
        key_file=key_file,
        cmd_timeout=None, # Set to None to disable timeout
        port=22
    )


    run_python_command1 = SSHOperator(
        task_id='run_raw_data_command',
        ssh_hook=ssh_hook,
        command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_rawdata.py {command_pipeline_rawdata}',
        do_xcom_push=True,
        conn_timeout=None, # Set to None to disable timeout,
        cmd_timeout=None, # Set to None to disable timeout
    )

    wait_for_five_min = TimeDeltaSensor(
        task_id='wait_for_five_min',
        delta=timedelta(minutes=5)
        )
    
    run_python_command2 = SSHOperator(
        task_id='run_pipeline_sf_copy_command',
        ssh_hook=ssh_hook,
        command=f'source export_variables_{env};set -o pipefail; python {python_file_path}/pipeline_sf_copy.py {command_copy}',
        do_xcom_push=True,
        conn_timeout=None, # Set to None to disable timeout,
        cmd_timeout=None, # Set to None to disable timeout
    )


    run_dbt_command = JainGlobalSSHDBTOperator(
        task_id='run_dbt_command',
        ssh_hook=ssh_hook,
        base_command = 'run',
        profile = 'dbt_data_platform',
        select_args = [f"tag:{source}_{dataset}"],
        target = env,
        env = env,
        timeout=None  # Set to None to disable timeout
    )


    test_dbt_command = JainGlobalSSHDBTOperator(
        task_id='test_dbt_command',
        ssh_hook=ssh_hook,
        base_command = 'test',
        profile = 'dbt_data_platform',
        select_args = [f"tag:{source}_{dataset}"],
        target = env,
        env = env,
        timeout=None  # Set to None to disable timeout
    )

    run_python_command1 >> wait_for_five_min >> run_python_command2 >> run_dbt_command >> test_dbt_command



