# Main head containing different configuration sets
[config]

# Configuration set for the first tick file
[config.prod]
base_path = "/jfs/stshare/data/prod/0.1/eu/stdata/equities/mk2/identifiers/"
base_path_coverage = "/jfs/stshare/data/prod/0.1/eu/stdata/equities/mk2/coverage/"
database_st = "SECURITY_MASTER_UAT"
database_gsc = "GOLDENSOURCE"
database_sec_mast = "SECURITY_MASTER"
bbrg_dir = "equityEuroPricing"
bbrg_file_prefix = "equity_euro.px.gz."
email="<EMAIL>,<EMAIL>,<PERSON><PERSON>nde<PERSON>@thegoldensource.com,D<PERSON><EMAIL>,<EMAIL>,<PERSON><PERSON><PERSON><PERSON>@thegoldensource.com"
email_bbg="<EMAIL>,dns<PERSON><PERSON>@thegoldensource.com,j<PERSON><PERSON>@thegoldensource.com,<EMAIL>,su<PERSON><PERSON>@thegoldensource.com"
exclude_list="'BARRAID is Missing or Different','Cusip is Missing or Different','RIC is Missing or Different'"

[config.uat]
base_path = "/jfs/stshare/data/prod/0.1/eu/stdata/equities/mk2/identifiers/"
base_path_coverage = "/jfs/stshare/data/prod/0.1/eu/stdata/equities/mk2/coverage/"
database_st = "SECURITY_MASTER_UAT"
database_gsc = "GOLDENSOURCE_UAT"
database_sec_mast = "SECURITY_MASTER_UAT"
bbrg_dir = "equityEuroPricing"
bbrg_file_prefix = "equity_euro.px.gz."
email="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
email_bbg="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
exclude_list="'Cusip is Missing or Different','RIC is Missing or Different'"

[config.dev]
base_path = "/jfs/stshare/data/prod/0.1/eu/stdata/equities/mk2/identifiers/"
base_path_coverage = "/jfs/stshare/data/prod/0.1/eu/stdata/equities/mk2/coverage/"
database_st = "SECURITY_MASTER_UAT"
database_gsc = "GOLDENSOURCE_UAT"
database_sec_mast = "SECURITY_MASTER_UAT"
bbrg_dir = "equityEuroPricing"
bbrg_file_prefix = "equity_euro.px.gz."
email="<EMAIL>"
email_bbg="<EMAIL>"
exclude_list="'Cusip is Missing or Different','RIC is Missing or Different'"