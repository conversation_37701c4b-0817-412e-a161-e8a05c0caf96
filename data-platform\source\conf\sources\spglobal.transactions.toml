name = 'spglobal.transactions'
class = 'stcommon.infra.rawstore.SftpLoader'
schedule = '*/10 * * * *'
tz = 'UTC'

[kwargs]
host = 'sftp2.spglobal.com'
source = 'Products'
target = 's3://jg-data-dp-vendor-data/transactions/1.0'
s3target_local_dir = '/jfs/tech1/apps/rawdata/spglobal/transactions/1.0'


files = ['TransactionsV2/.*\.zip',
        'TransactionsMAV2/.*\.zip',
        'TransactionsMAConsiderationV2/.*\.zip',
        'TransactionsMAMktDataV2/.*\.zip',
        'TransactionsOfferingV2/.*\.zip',
        'TransactionsOfferingMarketV2/.*\.zip',
        'TransactionsRefDataV2/.*\.zip',
        'DataItemMaster/.*\.zip'
         ]