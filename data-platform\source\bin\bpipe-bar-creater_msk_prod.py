import toml
import os
import time
from collections import defaultdict
from datetime import datetime
import sys
import pytz
# current_dir = os.path.dirname(os.path.abspath(__file__))
# project_root_temp = os.path.abspath(os.path.join(current_dir, '..'))
# sys.path.append(project_root_temp)
# print(project_root_temp)
from strunner import *
setupEnvironment()

import jglib.paths as pth
from stcommon.infra.rds.snowflake_operation import *
import logging
import sys
import os
import json
import time
import os
import json
from datetime import datetime, timedelta
import socket
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
import pytz
import re
from confluent_kafka import Producer, KafkaException
from stcommon.infra.rds.kafka_operation import KafkaPublisher
kafka_conf = {}

HOSTNAME = str(socket.gethostname())
PROJECT_ROOT_TEMP= os.environ.get('JGDATA_PATH')
instance_id = ""

TOML_CONFIG_FILE = PROJECT_ROOT_TEMP + "/conf/sources/bar_creator/bar_config_prod.toml"
with open(TOML_CONFIG_FILE, 'r') as file:
    toml_config_handler = toml.load(file)

config_head_obj = toml_config_handler.get("config_heads", {}).get('bpipe', {})
bpipe_tick_dir = config_head_obj.get("tick_dir")
bpipe_last_processed_dir = config_head_obj.get("last_processed_dir")

logger = logging.getLogger("Bar Creator")
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

BPIPE_RAW_PATH = os.path.join(bpipe_tick_dir, f"bpipe_streaming_{HOSTNAME}_PROD")
LAST_PROCESSED_JSON_FILE = os.path.join(bpipe_last_processed_dir, f"last_processed_{HOSTNAME}.json")

console_handler = logging.StreamHandler(sys.stdout)
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
console_handler.setFormatter(formatter)
    
logger.addHandler(console_handler)
logger.info("Bar creator job started.")
    
class FileProcessor:
    def __init__(self, config_path, config_head, tick_dir = BPIPE_RAW_PATH, json_file = LAST_PROCESSED_JSON_FILE, instance_id=''):
        # Load configurations from the TOML file
        self.config = toml.load(config_path)

        # Load the specific configuration set based on the head
        self.config_head = self.config['config_heads'][config_head]

        # self.tick_dir = os.path.join(pth.jg_data_path(), f"bpipe_streaming_{HOSTNAME}")
        # self.tick_dir = BPIPE_RAW_PATH
        self.tick_dir = tick_dir
        

        #"bpipe_streaming"
        self.db_name = self.config_head['db_name']
        self.wait_time_seconds = self.config_head['wait_time_seconds']
        self.field_names = self.config_head['field_names']

        #TODO: Change it in prod
        # self.obj_sf = SnowflakeDML("APACST_PROD_DB")
        # self.obj_sf = SnowflakeDML("DATA_PLATFORM_UAT")
        # self.json_file = LAST_PROCESSED_JSON_FILE
        self.json_file = json_file


    def get_most_recent_file(self):
        """Finds the second most recent file in the directory, excluding the current minute."""
        
        directory = self.tick_dir
        files = os.listdir(directory)

        # Get current timestamp minus 1 minute
        one_min_ago = datetime.now() - timedelta(minutes=1)

        # Extract valid files with timestamps
        dated_files = []

        for file in files:
            try:
                # Extract timestamp from filename (assuming "prefix_YYYYMMDD_HHMMSS.txt" format)
                timestamp_str = file.split("_to_")[-1].replace(".txt", "")
                file_time = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")

                # Exclude files from the last minute
                if file_time < one_min_ago:
                    dated_files.append((file_time, file))

            except ValueError:
                continue  # Skip files with incorrect format

        # Sort files by timestamp in descending order (most recent first)
        dated_files.sort(reverse=True, key=lambda x: x[0])

        # Return the second most recent file if available
        return dated_files[1][1] if len(dated_files) > 1 else None


    def read_last_processed(self):
        """Reads the last processed file from JSON. If missing, returns the most recent file (current - 1 min)."""
        
        # If the JSON file doesn't exist, return None
        if not os.path.exists(self.json_file):
            return None

        try:
            with open(self.json_file, "r") as f:
                data = json.load(f)
            
            # Extract last processed file
            last_processed_file = data.get("last_processed")
            
            # If the value is empty, None, or missing, find the most recent file (current time - 1 min)
            if not last_processed_file:  
                return self.get_most_recent_file()

            return last_processed_file

        except json.JSONDecodeError:
            return None
    
    def update_last_processed(self, file_name):
        """Updates the JSON file with the newly processed file."""
        with open(self.json_file, "w") as f:
            json.dump({"last_processed": file_name}, f)

    
    def get_modified_time_truncated(self, file_path):
        # Get the modification time in seconds
        mod_time = os.path.getmtime(file_path)
        
        # Convert it to a datetime object
        dt = datetime.fromtimestamp(mod_time)
        
        # Truncate seconds, milliseconds, and microseconds
        truncated_dt = dt.replace(second=0, microsecond=0)
        
        # Convert back to timestamp
        return truncated_dt.timestamp()

                
    def extract_timestamp_from_filename(self, file_name):
        """Extract timestamp from the last part of the file name. Assumes format *_YYYYMMDD_HHMMSS_to_YYYYMMDD_HHMMSS.txt."""
        match = re.search(r"_(\d{8}_\d{6})_to_", file_name)
        if match:
            return datetime.strptime(match.group(1), "%Y%m%d_%H%M%S").replace(tzinfo=pytz.utc)
        return None
    
    def get_latest_file(self):
        """Retrieve the oldest file generated after the last processed file but before current time - 1 minute."""
        while True:
            last_processed_file = self.read_last_processed()
            last_processed_time = None

            if last_processed_file:
                last_processed_time = self.extract_timestamp_from_filename(last_processed_file)
            

            now = datetime.utcnow().replace(second=0, microsecond=0, tzinfo=pytz.utc)
            # print(f"Last Processed Time: {last_processed_time}")
            # print(f"Now Time: {now}")
            
            files = [os.path.join(self.tick_dir, f) for f in os.listdir(self.tick_dir) if os.path.isfile(os.path.join(self.tick_dir, f))]
            
            # Extract timestamps from filenames and filter valid ones
            file_timestamps = []
            for f in files:
                file_time = self.extract_timestamp_from_filename(os.path.basename(f))
                if file_time:
                    file_timestamps.append((f, file_time))
            
            # Sort files by extracted timestamp (oldest first)
            file_timestamps.sort(key=lambda x: x[1])
            
            # Filter files based on last processed time and cutoff time
            eligible_files = [f for f, t in file_timestamps if (last_processed_time is None or t > last_processed_time) and t < now]
            
            
            if eligible_files:
                logger.info(f"Last Processed File: {last_processed_file}")
                selected_file = eligible_files[0]
                self.update_last_processed(os.path.basename(selected_file))
                return selected_file
            else:
                logger.info("Waiting..")
                time.sleep(1)


    def extract_last_values(self, filename):
        """Extract the last updated values for each field from the file."""
        ticker_data = defaultdict(lambda: {field: None for field in self.field_names})

        if not os.path.exists(filename):
            self.log(f"File {filename} does not exist. Skipping.")
            return ticker_data

        with open(filename, 'r') as file:
            for line in file:
                try:
                    data = eval(line.strip())
                    security = data.get('SECURITY', 'UNKNOWN')
                    for field in self.field_names:
                        if field in data:
                            ticker_data[security][field] = data[field]
                except Exception as e:
                    self.log(f"Error parsing line: {line.strip()} - {e}")
                    continue

        return ticker_data

   
    def write_to_summary_file(self, ticker_data, input_filename, max_workers=10):
        """Append summarized data to the summary file using parallel execution."""
        record_datetime = datetime.now(pytz.utc).strftime("%Y-%m-%d %H:%M:%S.%f")
        snap_time_str = input_filename.split("_to_")[-1].replace(".txt", "")
        snap_time = datetime.strptime(snap_time_str, "%Y%m%d_%H%M%S").strftime("%Y-%m-%d %H:%M:%S.%f")

        data_dict = {}
        kafka_pub = KafkaPublisher(instance_id)
        for security, data in ticker_data.items():
            
            data_dict["FILE_NAME"] = input_filename
            data_dict["TICKER"] =  security
            data_dict["ASK"] = data["ASK"]
            data_dict["BID"] = data["BID"]
            data_dict["LAST_PRICE"] = data["LAST_PRICE"]
            data_dict["RECORD_DATETIME"] = record_datetime
            data_dict["SNAP_DATETIME"] = snap_time
            data_dict["INSERTED_BY_HOSTNAME"] = HOSTNAME
            data_dict["RECORD_INSERTION_TIME"] = str(record_datetime)
            
            json_data = json.dumps(data_dict)
            kafka_pub.publish_messages("bpipe-bar-data", security, json_data)
            
            

    def process_files(self):
        """Process files in the tick directory."""
        current_file = self.get_latest_file()
        if not current_file:
            logger.info(f"No files found in the directory: {self.tick_dir}")
            return

        while current_file:
            logger.info(f"\nProcessing file: {current_file}")
            # print(f"Bar Generation Start at: {datetime.now(pytz.utc).strftime('%Y-%m-%d %H:%M:%S.%f')}")
            ticker_data = self.extract_last_values(current_file)
            # print(f"Bar Generation Ends at: {datetime.now(pytz.utc).strftime('%Y-%m-%d %H:%M:%S.%f')}")
            
            self.write_to_summary_file(ticker_data, os.path.basename(current_file))
            logger.info(f"Data Written for {current_file}")
            current_file = self.get_latest_file()


def update_global_paths(new_instance_id):
    """Update global paths with the given instance_id"""
    global instance_id, BPIPE_RAW_PATH, LAST_PROCESSED_JSON_FILE
    instance_id = new_instance_id
    BPIPE_RAW_PATH = os.path.join(bpipe_tick_dir, f"bpipe_streaming_{HOSTNAME}_{instance_id}_PROD")
    LAST_PROCESSED_JSON_FILE = os.path.join(bpipe_last_processed_dir, f"last_processed_{HOSTNAME}_{instance_id}.json")

    return BPIPE_RAW_PATH, LAST_PROCESSED_JSON_FILE

if __name__ == "__main__":

    # if len(sys.argv) < 2:
    #     logger.info("Usage: python file_processor.py <config_head>")
    #     sys.exit(1)

    # config_head = sys.argv[1]
    
    parser = argparse.ArgumentParser(description='BPIPE Bar Creator')
    parser.add_argument('config_head', type=str, help='Configuration head to use')
    parser.add_argument('--instance_id', type=str, default='', help='Instance ID to use in file names')
    args = parser.parse_args()
    
    config_head = args.config_head
    instance_id = args.instance_id
    
    BPIPE_RAW_PATH, LAST_PROCESSED_JSON_FILE = update_global_paths(args.instance_id)

    project_root_temp= os.environ.get('JGDATA_PATH')
    TOML_CONFIG_FILE = project_root_temp + "/conf/sources/bar_creator/bar_config_prod.toml"
    processor = FileProcessor(TOML_CONFIG_FILE, config_head, BPIPE_RAW_PATH, LAST_PROCESSED_JSON_FILE, instance_id)
    processor.process_files()
